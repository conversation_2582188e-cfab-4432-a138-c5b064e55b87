{"system": {"name": "AutoIns - 自动录单系统", "version": "2.0.0", "description": "多保险公司自动录单集成系统", "timezone": "Asia/Shanghai", "locale": "zh-CN"}, "database": {"connections": {"main": {"name": "主数据库", "connection": "mysql", "prefix": "ato_", "description": "自动录单系统主数据库"}, "subsystems": {"youfu": {"name": "优孚系统", "connection": "mysql_yf", "model": "DataYoufu", "order_field": "SerialNo", "time_field": "DateTime", "is_timestamp": true, "platform_app_id": "INS1569686500", "email_sender": "<EMAIL>"}, "baoya_old": {"name": "保呀系统(旧版)", "connection": "mysql_by", "model": "DataBaoya", "order_field": "sys_order_no", "time_field": "created_at", "is_timestamp": false, "platform_app_id": "INS1569723235", "email_sender": "<EMAIL>"}, "baoya_new": {"name": "保呀系统(新版)", "connection": "mysql_by_new", "model": "DataBaoyaNew", "order_field": "order_no", "time_field": "submitted_at", "is_timestamp": false, "platform_app_id": "INS1635757943", "email_sender": "<EMAIL>"}, "aladdin": {"name": "阿拉丁系统", "connection": "mysql_ald", "model": "<PERSON><PERSON><PERSON><PERSON>", "order_field": "order_no", "time_field": "created_at", "is_timestamp": false, "platform_app_id": "INS1569686501", "email_sender": "<EMAIL>"}, "zhonglian": {"name": "中联系统", "connection": "mysql_zl", "model": "DataZhongLian", "order_field": "order_no", "time_field": "created_at", "is_timestamp": false, "platform_app_id": "INS1569686502", "email_sender": "<EMAIL>"}}}}, "insurance_companies": {"picc": {"name": "人保财险", "modes": ["AUTO_PICC", "AUTO_PICC_INTL"], "type": "automation", "description": "人保财险自动录单", "controller": "PICCController", "features": {"domestic": true, "international": true, "auto_entry": true, "api_integration": false}}, "cpic": {"name": "太保财险", "modes": ["AUTO_CPIC", "AUTO_CPIC_INTL"], "type": "automation", "description": "太保财险自动录单", "controller": "CPICController", "features": {"domestic": true, "international": true, "auto_entry": true, "api_integration": false}}, "pingan": {"name": "平安财险", "modes": ["AUTO_PINGAN", "AUTO_PINGAN_INTL", "AUTO_PINGAN_CBEC"], "type": "automation", "description": "平安财险自动录单", "controller": "PINGANController", "features": {"domestic": true, "international": true, "cbec": true, "auto_entry": true, "api_integration": false}}, "huatai": {"name": "华泰财险", "modes": ["API_HUATAI"], "type": "api", "description": "华泰财险API接口", "controller": "HUATAIController", "data_format": "xml", "features": {"domestic": true, "international": false, "auto_entry": false, "api_integration": true}}, "sinosig": {"name": "中华联合", "modes": ["API_SINOSIG", "API_SINOSIG_QZ"], "type": "api", "description": "中华联合API接口", "controller": "SinosigController", "data_format": "xml", "features": {"domestic": true, "international": true, "auto_entry": false, "api_integration": true}}, "starr": {"name": "史带财险", "modes": ["API_STARR"], "type": "api", "description": "史带财险API接口", "controller": "STARRController", "data_format": "json", "features": {"domestic": true, "international": false, "auto_entry": false, "api_integration": true}}, "guoren": {"name": "国人财险", "modes": ["AUTO_GUOREN"], "type": "automation", "description": "国人财险自动录单", "controller": "GUORENController", "features": {"domestic": true, "international": false, "auto_entry": true, "api_integration": false}}, "bdyf": {"name": "第三方平台", "modes": ["AUTO_BDYF"], "type": "automation", "description": "第三方平台自动录单", "controller": "BDYFController", "features": {"domestic": true, "international": false, "auto_entry": true, "api_integration": false}}}, "status_definitions": {"message_status": {"0": {"name": "未处理", "description": "报文已创建，等待处理", "color": "gray"}, "1": {"name": "已提交", "description": "已提交到保险公司，等待结果", "color": "blue"}, "2": {"name": "已完成", "description": "已获取保单号，处理完成", "color": "green"}, "-1": {"name": "已作废", "description": "处理失败，已作废", "color": "red"}}, "notify_status": {"0": {"name": "未处理", "description": "通知未处理", "color": "gray"}, "1": {"name": "待发送", "description": "等待发送通知", "color": "yellow"}, "2": {"name": "已发送", "description": "通知已发送", "color": "green"}, "3": {"name": "发送失败", "description": "通知发送失败", "color": "red"}, "4": {"name": "中意雇主待发送", "description": "中意雇主特殊状态", "color": "orange"}}, "mail_status": {"1": {"name": "正在发送", "description": "邮件正在发送中", "color": "blue"}, "2": {"name": "已发送", "description": "邮件发送成功", "color": "green"}, "3": {"name": "发送失败", "description": "邮件发送失败", "color": "red"}}}, "scheduled_tasks": {"failure_event": {"command": "messages:failure-event", "schedule": "everyFiveMinutes", "description": "检测录单失败并发送回调通知", "timeout": 300}, "checking_yf_policy": {"command": "messages:checking-yf-policy", "schedule": "everyMinute", "description": "检查优孚平台录单异常保单", "timeout": 60}, "send_mail": {"command": "autoins:send-mail", "schedule": "everyTenMinutes", "description": "自动发送报备邮件", "timeout": 600}, "send_sub_system_mail": {"command": "autoins:send-sub-system-mail", "schedule": "everyTenMinutes", "description": "自动发送子系统报备邮件", "timeout": 600}}, "business_rules": {"time_handling": {"departure_time_adjustment": {"description": "起运时间调整规则", "rules": ["如果起运时间是当天，延后1小时", "如果当前分钟数超过50分钟，再延后1小时", "如果调整后时间超过24点，改为次日00:00"]}, "failure_detection": {"description": "失败检测时间规则", "rules": ["平安跨境电商：2小时后算失败", "其他模式：10分钟后算失败"]}}, "rate_conversion": {"description": "费率转换规则", "formula": "平台费率(万分之几) ÷ 10 = 保险公司费率(千分之几)"}, "invoice_number_format": {"description": "发票号格式规则", "rules": ["保呀平台：invNo + ' / ' + freightNo", "其他平台：orderNo + ' / ' + invNo"]}}, "error_handling": {"retry_policy": {"max_retries": 3, "retry_delay": 60, "backoff_multiplier": 2}, "timeout_settings": {"api_timeout": 30, "database_timeout": 10, "queue_timeout": 300}, "error_codes": {"1001": "认证失败", "1002": "权限不足", "1003": "数据验证失败", "2001": "网络连接超时", "2002": "保险公司接口异常", "3001": "数据库连接失败", "3002": "数据写入失败"}}, "performance_settings": {"cache": {"platform_products_ttl": 3600, "insurance_config_ttl": 86400, "failure_events_ttl": 432000}, "queue": {"default_queue": "default", "high_priority_queue": "high", "low_priority_queue": "low", "max_attempts": 3, "retry_after": 90}, "database": {"chunk_size": 100, "connection_pool_size": 10, "query_timeout": 30}}, "monitoring": {"metrics": ["api_response_time", "database_query_time", "queue_processing_time", "error_rate", "success_rate"], "alerts": {"error_rate_threshold": 0.05, "response_time_threshold": 5000, "queue_size_threshold": 1000}, "log_levels": {"production": "error", "staging": "warning", "development": "debug"}}, "security": {"jwt": {"ttl": 60, "refresh_ttl": 20160, "algorithm": "HS256"}, "rate_limiting": {"api_requests_per_minute": 60, "login_attempts_per_minute": 5}, "encryption": {"sensitive_fields": ["password", "secret_key", "api_key"]}}}