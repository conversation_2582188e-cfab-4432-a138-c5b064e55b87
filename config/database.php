<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
        ],

        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'autoins'),
            'username' => env('DB_USERNAME', 'root'),
            'password' => env('DB_PASSWORD', 'root'),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => 'ato_',
            'strict' => true,
            'engine' => null,
        ],

        'mysql_ald' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_ALD', '127.0.0.1'),
            'port' => env('DB_PORT_ALD', '3306'),
            'database' => env('DB_DATABASE_ALD', 'forge'),
            'username' => env('DB_USERNAME_ALD', 'forge'),
            'password' => env('DB_PASSWORD_ALD', ''),
            'unix_socket' => env('DB_SOCKET_ALD', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],

        'mysql_lby' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_LBY', '127.0.0.1'),
            'port' => env('DB_PORT_LBY', '3306'),
            'database' => env('DB_DATABASE_LBY', 'forge'),
            'username' => env('DB_USERNAME_LBY', 'forge'),
            'password' => env('DB_PASSWORD_LBY', ''),
            'unix_socket' => env('DB_SOCKET_LBY', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],

        'mysql_zl' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_ZL', '127.0.0.1'),
            'port' => env('DB_PORT_ZL', '3306'),
            'database' => env('DB_DATABASE_ZL', 'forge'),
            'username' => env('DB_USERNAME_ZL', 'forge'),
            'password' => env('DB_PASSWORD_ZL', ''),
            'unix_socket' => env('DB_SOCKET_ZL', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],

        'mysql_by' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_BY', '127.0.0.1'),
            'port' => env('DB_PORT_BY', '3306'),
            'database' => env('DB_DATABASE_BY', 'forge'),
            'username' => env('DB_USERNAME_BY', 'forge'),
            'password' => env('DB_PASSWORD_BY', ''),
            'unix_socket' => env('DB_SOCKET_BY', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],

        'mysql_by_new' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_BY_NEW', '127.0.0.1'),
            'port' => env('DB_PORT_BY_NEW', '3306'),
            'database' => env('DB_DATABASE_BY_NEW', 'forge'),
            'username' => env('DB_USERNAME_BY_NEW', 'forge'),
            'password' => env('DB_PASSWORD_BY_NEW', ''),
            'unix_socket' => env('DB_SOCKET_BY_NEW', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],

        'mysql_yf' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_YF', '127.0.0.1'),
            'port' => env('DB_PORT_YF', '3306'),
            'database' => env('DB_DATABASE_YF', 'forge'),
            'username' => env('DB_USERNAME_YF', 'forge'),
            'password' => env('DB_PASSWORD_YF', ''),
            'unix_socket' => env('DB_SOCKET_YF', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '1433'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer set of commands than a typical key-value systems
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => 'predis',

        'default' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => 0,
        ],

    ],

];
