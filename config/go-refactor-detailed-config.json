{
  "system_info": {
    "name": "AutoIns Go重构版本",
    "version": "3.0.0",
    "description": "基于Go语言的自动录单系统重构版本",
    "target_go_version": "1.21+",
    "architecture": "微服务架构"
  },
  "insurance_companies": {
    "picc": {
      "name": "人保财险",
      "modes": ["AUTO_PICC", "AUTO_PICC_INTL"],
      "type": "automation",
      "accounts": {
        "yrwl02": {
          "agreement_start_date": "2019-05-27",
          "description": "赢睿物流02账号"
        },
        "yrwl01": {
          "agreement_start_date": "2020-05-22",
          "description": "赢睿物流01账号"
        },
        "cdwh1": {
          "agreement_start_date": "2019-10-18",
          "description": "成都万恒1号账号"
        },
        "cdwh2": {
          "agreement_start_date": "2019-10-18",
          "description": "成都万恒2号账号"
        },
        "cdwh3": {
          "agreement_start_date": "2019-10-18",
          "description": "成都万恒3号账号"
        },
        "wanheng": {
          "agreement_start_date": "2020-05-04",
          "description": "万恒账号"
        },
        "muzixing0001": {
          "agreement_start_date": "2019-09-24",
          "description": "木子行0001账号"
        },
        "muzixing02": {
          "agreement_start_date": "2019-09-20",
          "description": "木子行02账号"
        },
        "muzixing03": {
          "agreement_start_date": "2019-09-20",
          "description": "木子行03账号"
        },
        "muzixing4": {
          "agreement_start_date": "2019-09-20",
          "description": "木子行4号账号"
        },
        "muzixing05": {
          "agreement_start_date": "2019-09-20",
          "description": "木子行05账号"
        },
        "muzixing06": {
          "agreement_start_date": "2019-09-20",
          "description": "木子行06账号"
        },
        "quanqiutong01": {
          "agreement_start_date": "2019-09-24",
          "description": "全球通01账号"
        },
        "quanqiutong2": {
          "agreement_start_date": "2019-09-24",
          "description": "全球通2号账号"
        },
        "sxdh": {
          "agreement_start_date": "2020-07-14",
          "description": "陕西大华账号"
        },
        "BAOCANGWANG02": {
          "agreement_start_date": "2020-07-17",
          "description": "保仓网02账号"
        },
        "BAOCANGWANG04": {
          "agreement_start_date": "2020-10-16",
          "description": "保仓网04账号"
        },
        "YNYJB2": {
          "agreement_start_date": "2020-12-01",
          "description": "云南运吉宝2号账号"
        },
        "4403A00210": {
          "agreement_start_date": "2020-03-12",
          "description": "4403A00210账号"
        },
        "210314yjb02": {
          "agreement_start_date": "2021-01-09",
          "description": "210314运吉宝02账号"
        },
        "DEJUN": {
          "agreement_start_date": "2022-03-23",
          "description": "德骏账号"
        }
      },
      "clause_mapping": {
        "1": "国内公路货运保险",
        "3": "国内航空货运保险",
        "5": "国内水路、陆路货运综合险",
        "6": "国内水路、陆路货运基本险",
        "7": "国内水路货运保险综合保险",
        "8": "国内水路货运保险基本保险",
        "9": "国内铁路货运保险综合保险",
        "10": "国内铁路货运保险基本保险",
        "1000": "国内鲜活货物运输保险条款（2009版）",
        "1001": "国内邮（包）件保险条款（2009版）",
        "1002": "滚装船运输保险条款",
        "1003": "无车承运人责任保险条款",
        "1004": "生鲜冷链物流保险条款（注册号：09IT2017000020028）"
      },
      "addition_clause_mapping": {
        "1005": "国内水路、陆路货物运输保险附加盗窃、抢劫保险条款",
        "1004": "国内水路、陆路货物运输保险附加盗窃、抢劫保险条款",
        "165": "公路货物运输保险附加盗窃、抢劫保险条款（2009版）",
        "0": ""
      },
      "special_rules": {
        "platform_5_invoice_format": "invNo + ' / ' + freightNo",
        "default_invoice_format": "orderNo + ' / ' + invNo",
        "client_data_platform_5": {
          "holder_province": "110000",
          "holder_city": "110100",
          "holder_county": "110101",
          "holder_customer_type": "0",
          "holder_overseas": "0",
          "holder_document_type": "统一社会信用代码",
          "holder_identity": "91310230MA1K2FMP33",
          "holder_start_date_valid": "2019-01-04",
          "holder_end_date_valid": "2039-01-03",
          "holder_unit_properties": "300",
          "recognizee_province": "110000",
          "recognizee_city": "110100",
          "recognizee_county": "110101",
          "recognizee_customer_type": "0",
          "recognizee_overseas": "0",
          "recognizee_document_type": "统一社会信用代码",
          "recognizee_identity": "91310115MA1H90HR24",
          "recognizee_start_date_valid": "2019-01-04",
          "recognizee_end_date_valid": "2039-01-03",
          "recognizee_unit_properties": "300"
        }
      }
    },
    "cpic": {
      "name": "太保财险",
      "modes": ["AUTO_CPIC", "AUTO_CPIC_INTL"],
      "type": "automation",
      "accounts": {
        "SHTB": {
          "protocol_no": "A|3010200|C20180144-19001P000431|CAGHYX190032",
          "unit_code": "3010200",
          "franchise": "本保单其他承保条件同协议。",
          "rate_info": ["0.5", "3000000", "50"],
          "description": "上海太保账号"
        },
        "YRWL": {
          "protocol_no": "A|3010100|C20190295|0",
          "unit_code": "3010100",
          "franchise": "本保单其他承保条件同协议; 每次事故绝对免赔额为人民币1000或损失金额的10%，两者取高;",
          "rate_info": ["0.15", "500000", "3"],
          "description": "赢睿物流账号"
        },
        "SHGH3": {
          "protocol_no": "A|3010100|C20200410P000187|COPSHH200411",
          "unit_code": "3010100",
          "franchise": "本保单其他承保条件同协议。",
          "rate_info": ["0.12", "5000000", "15"],
          "description": "上海港海3号账号"
        },
        "COPCHQ210033": {
          "protocol_no": "A|6020100|C20210059P000006|COPCHQ210033",
          "unit_code": "6020100",
          "franchise": "使用settings[1]",
          "rate_info": ["0.20", "5000000", "15"],
          "description": "COPCHQ210033账号"
        },
        "CAGWUX210005": {
          "protocol_no": "A|3020300|C20210014P000012|CAGWUX210005",
          "unit_code": "3020300",
          "franchise": "使用settings[1]",
          "rate_info": ["0.20", "5000000", "15"],
          "description": "CAGWUX210005账号"
        },
        "COPCHQ220028": {
          "protocol_no": "A|6020100|C20210059-22001|0",
          "unit_code": "6020100",
          "franchise": "使用settings[1]",
          "rate_info": ["0.20", "5000000", "15"],
          "description": "COPCHQ220028账号"
        },
        "CDYF": {
          "protocol_no": "C|3010100|CSHHHYX2024P000473|0|CSHHHYX2024Q000452",
          "unit_code": "3010100",
          "franchise": "每次事故的综合免赔为绝对免赔额2000人民币或损失金额的10%，两者以高者为准 ，包括全损。\\r\\n火灾、翻车事故每次事故的综合免赔为绝对免赔额2000人民币或损失金额的15%，两者以高者为准 ，包括全损。\\r\\n针对酒类（单瓶价格低于100元/瓶）的综合免赔为绝对免赔额2000人民币或损失金额的10%，两者以高者为准 ，包括全损。\\r\\n针对易碎品的综合免赔为保额金额的3% ，包括全损。",
          "rate_info_jb_0309": ["0.12", "3000000", "15"],
          "rate_info_default": ["0.15", "3000000", "15"],
          "description": "成都优孚账号",
          "special_processing": {
            "holder_name": "成都优孚世纪信息技术有限公司",
            "has_cargo_coverage": true,
            "item19": "0,1,2,3",
            "cargo_coverage_jb_0309": "{\"tCargoCoverageId\":{\"auditType\":\"C\",\"unitCode\":\"3010100\",\"applyNo\":\"CSHHHYX2024P000473\",\"applyEndorseNo\":\"0\",\"coverageNo\":2},\"classesCode\":\"11040200\",\"flightAreaCode\":\"0\",\"rate\":0.000120,\"detailLimit\":3000000.0000,\"startPlace\":null,\"endPlace\":null,\"tCargoCoverageCargos\":[{\"id\":400475,\"code\":\"0309\",\"name\":\"轻工品类（非易碎）\",\"cibscode\":\"0309\"},{\"id\":400476,\"code\":\"0701\",\"name\":\"设备仪器类（整机）\",\"cibscode\":\"0701\"}],\"tCargoCoverageMains\":[{\"id\":214913,\"code\":\"HY000063\",\"name\":null,\"cibscode\":null}],\"tCargoCoveragePacks\":[{\"id\":297632,\"code\":\"104\",\"name\":\"裸装\",\"cibscode\":\"05\"},{\"id\":297630,\"code\":\"101\",\"name\":\"箱装\",\"cibscode\":\"01\"},{\"id\":297631,\"code\":\"103\",\"name\":\"托盘\",\"cibscode\":\"03\"}],\"tCargoCoverageKinds\":[{\"id\":378435,\"code\":\"4\",\"name\":\"公路\",\"cibscode\":\"4\"}],\"tCargoCoverageClauses\":[{\"id\":276994,\"code\":\"HY000063.HYCC00118\",\"name\":\"国内水陆路基本险\",\"cibscode\":\"JB\"}],\"classesName\":\"公路货运险\"}",
            "cargo_coverage_jb_0301": "{\"tCargoCoverageId\":{\"auditType\":\"C\",\"unitCode\":\"3010100\",\"applyNo\":\"CSHHHYX2024P000473\",\"applyEndorseNo\":\"0\",\"coverageNo\":4},\"classesCode\":\"11040200\",\"flightAreaCode\":\"0\",\"rate\":0.000150,\"detailLimit\":3000000.0000,\"startPlace\":null,\"endPlace\":null,\"tCargoCoverageCargos\":[{\"id\":400478,\"code\":\"0301\",\"name\":\"玻璃、陶瓷、搪瓷等易碎品类\",\"cibscode\":\"0301\"}],\"tCargoCoverageMains\":[{\"id\":214915,\"code\":\"HY000063\",\"name\":null,\"cibscode\":null}],\"tCargoCoveragePacks\":[{\"id\":297634,\"code\":\"101\",\"name\":\"箱装\",\"cibscode\":\"01\"}],\"tCargoCoverageKinds\":[{\"id\":378437,\"code\":\"4\",\"name\":\"公路\",\"cibscode\":\"4\"}],\"tCargoCoverageClauses\":[{\"id\":276996,\"code\":\"HY000063.HYCC00118\",\"name\":\"国内水陆路基本险\",\"cibscode\":\"JB\"}],\"classesName\":\"公路货运险\"}",
            "cargo_coverage_zh": "{\"tCargoCoverageId\":{\"auditType\":\"C\",\"unitCode\":\"3010100\",\"applyNo\":\"CSHHHYX2024P000473\",\"applyEndorseNo\":\"0\",\"coverageNo\":1},\"classesCode\":\"11040200\",\"flightAreaCode\":\"0\",\"rate\":0.000150,\"detailLimit\":3000000.0000,\"startPlace\":null,\"endPlace\":null,\"tCargoCoverageCargos\":[{\"id\":400473,\"code\":\"0810\",\"name\":\"民用电子产品\",\"cibscode\":\"0810\"},{\"id\":400471,\"code\":\"0701\",\"name\":\"设备仪器类（整机）\",\"cibscode\":\"0701\"},{\"id\":400460,\"code\":\"0514\",\"name\":\"不锈钢及其制品\",\"cibscode\":\"0514\"},{\"id\":400463,\"code\":\"0309\",\"name\":\"轻工品类（非易碎）\",\"cibscode\":\"0309\"},{\"id\":400464,\"code\":\"0310\",\"name\":\"塑料制品\",\"cibscode\":\"0310\"},{\"id\":400469,\"code\":\"0513\",\"name\":\"钢材、钢管、铸铁类\",\"cibscode\":\"0513\"},{\"id\":400468,\"code\":\"0711\",\"name\":\"医疗设备\",\"cibscode\":\"0711\"},{\"id\":400472,\"code\":\"0702\",\"name\":\"机器设备类（配件）\",\"cibscode\":\"0702\"},{\"id\":400466,\"code\":\"0812\",\"name\":\"电子产品配件\",\"cibscode\":\"0812\"},{\"id\":400467,\"code\":\"0705\",\"name\":\"电力设备\",\"cibscode\":\"0705\"},{\"id\":400470,\"code\":\"0207\",\"name\":\"糖类\",\"cibscode\":\"0207\"},{\"id\":400474,\"code\":\"0504\",\"name\":\"五金类（除钢材类外）\",\"cibscode\":\"0504\"},{\"id\":400461,\"code\":\"0311\",\"name\":\"橡胶制品\",\"cibscode\":\"0311\"},{\"id\":400462,\"code\":\"0405\",\"name\":\"纺织品\",\"cibscode\":\"0405\"},{\"id\":400465,\"code\":\"0806\",\"name\":\"电脑、手提（除大型终端）\",\"cibscode\":\"0806\"},{\"id\":400459,\"code\":\"0803\",\"name\":\"手机\",\"cibscode\":\"0803\"}],\"tCargoCoverageMains\":[{\"id\":214912,\"code\":\"HY000063\",\"name\":null,\"cibscode\":null}],\"tCargoCoveragePacks\":[{\"id\":297625,\"code\":\"104\",\"name\":\"裸装\",\"cibscode\":\"05\"},{\"id\":297627,\"code\":\"103\",\"name\":\"托盘\",\"cibscode\":\"03\"},{\"id\":297629,\"code\":\"101\",\"name\":\"箱装\",\"cibscode\":\"01\"},{\"id\":297628,\"code\":\"105\",\"name\":\"桶装\",\"cibscode\":\"06\"},{\"id\":297624,\"code\":\"102\",\"name\":\"袋装\",\"cibscode\":\"02\"}],\"tCargoCoverageKinds\":[{\"id\":378434,\"code\":\"4\",\"name\":\"公路\",\"cibscode\":\"4\"}],\"tCargoCoverageClauses\":[{\"id\":276993,\"code\":\"HY000063.HYCC00119\",\"name\":\"国内水陆路综合险\",\"cibscode\":\"ZH\"}],\"classesName\":\"公路货运险\"}",
            "special_terms": "本保单其他承保条件同协议;\\n1.对于食品、饮料、药品（无温控要求）或其他有外包装的日用品，在保险事故仅导致外包装损失，内包装及商品本身无损失的情况下，本保单仅就受损的外包装承担赔偿责任；在保险事故导致部分损失时，本保单仅就受损部分的保险损失承担保险责任，不承担整箱、整件货物被拒收或无法销售带来的损失；\\n2、承载保险货物车辆运输途中未发生交通事故，保险人不承担串味所致损失的赔偿责任。若运输货物为钢材以及相关制品,且货物裸装，则锈损为除外责任；若需运输食品, 则腐烂变质为除外责任；\\n3、所有标的发生保险责任内的事故损失时，均以修复为第一赔偿方式；\\n4、如被保险人违反交通安全运输规定或相关装载运输规定的，保险人有权拒赔；\\n5、若保险标的（货物）已运抵目的地，而未及时提货，保险人对因此造成的损失不承担赔偿责任；\\n8、对未生成保单号，或起运/投保前已发生事故的保险标的（货物），保险人不承担赔偿责任；\\n9、机械设备必须有木箱包围及底部托盘、金属固定架等防震防撞外包装，裸装、纸壳包装、塑料泡沫外包装的机械设备只承保火灾爆炸、交通事故、上下装卸的损失；\\n10、当承保货物为易碎品时，保险人仅承担因交通事故和火灾、爆炸导致的标的损失;\\n11、出险时，如运输车辆为重型低平板半挂车，且行驶证标注"仅可用于运送不可拆解物体"，如实际运输货物为普通货物，在满足安全装载规定的情况下，每次事故绝对免赔率加扣10%。"
          }
        }
      },
      "pack_type_mapping": {
        "裸装": "05",
        "散装": "04",
        "纸箱": "01",
        "木箱": "01",
        "捆包": "08",
        "袋装": "02",
        "篓装": "01",
        "托盘": "03",
        "桶装": "06",
        "罐装": "07"
      },
      "transport_type_mapping": {
        "3": "1",
        "4": "5",
        "5": "4",
        "6": "3"
      }
    },
    "pingan": {
      "name": "平安财险",
      "modes": ["AUTO_PINGAN", "AUTO_PINGAN_INTL", "AUTO_PINGAN_CBEC"],
      "type": "automation",
      "special_accounts": {
        "enterprise_accounts": [
          "CGHZYRWLKJ00002",
          "CGNJYB00013",
          "CGKMYJB00002",
          "CGFZZF200001",
          "CGGZYNYZK200001",
          "CGSHZHMBX00002",
          "CGSHZQLJJ00001",
          "CGGZQLBXJJ00001",
          "CGZYJJ00002",
          "CGGZLJGYL00001"
        ],
        "special_identity_accounts": [
          "CGGZQLBXJJ00001",
          "CGZYJJ00002"
        ],
        "yrkj_accounts": [
          "CGHZYRWLKJ00002",
          "CGNJYB00013",
          "CGZYJJ00002",
          "CGSHZQLJJ00001",
          "CGGZQLBXJJ00001",
          "CGGZZJGJLY00001",
          "CGKMYJB00002",
          "CGFZZF200001",
          "CGGZYNYZK200001",
          "CGSHZHMBX00002",
          "CGGZLJGYL00001",
          "CGGZHNHYT00001"
        ]
      },
      "account_special_rules": {
        "CGSHZQLJJ00001": {
          "name": "深圳平安-运吉宝",
          "special": "",
          "deductible": ""
        },
        "CGSHZHMBX00002": {
          "name": "深圳平安-保呀",
          "special": "",
          "deductible": ""
        },
        "CGZYJJ00002": {
          "name": "上海平安",
          "special_conditions": {
            "subject_normal": {
              "special": "",
              "goods_type_10_deductible": "每次事故绝对免赔为保额的3%"
            }
          },
          "default_identity": "91310230MA1K2FMP33"
        },
        "CGGZQLBXJJ00001": {
          "name": "广东平安",
          "special_conditions": {
            "subject_not_manual": {
              "special": "",
              "deductible": ""
            }
          },
          "default_identity": "91310230MA1K2FMP33"
        }
      },
      "certificate_type_rules": {
        "default_holder_type": "03",
        "default_recognizee_type": "03",
        "enterprise_holder_type": "99",
        "enterprise_recognizee_type": "01"
      },
      "special_processing": {
        "naked_goods_deductible": "裸装：本保单不承保由于刮擦，锈损，凹瘪引起的损失。"
      }
    },
    "huatai": {
      "name": "华泰财险",
      "modes": ["API_HUATAI"],
      "type": "api",
      "data_format": "xml",
      "goods_type_mapping": {
        "纺织原料及纺织制品": ["SX001411", "SX00140065"],
        "机器设备及其零件、附件": ["SX001416", "SX00140087"],
        "食品": ["SX001404", "SX00140019"],
        "化学工业及其相关工业产品": ["SX001406", "SX00140040"],
        "塑料及其制品;橡胶及其制品": ["SX001407", "SX00140041"],
        "木制品、木材": ["SX001409", "SX00140046"],
        "仪器、乐器、医疗设备及零件、附件（非精密仪器）": ["SX001418", "SX00140092"],
        "杂项制品": ["SX001420", "SX00140098"],
        "电脑/平板电脑、手机等电子产品": ["SX001416", "SX00140101"],
        "水果": ["SX001402", "SX00140008"],
        "蔬菜": ["SX001402", "SX00140007"],
        "鲜花": ["SX001402", "SX00140006"],
        "其他鲜活": ["SX001402", "SX00140012"],
        "玻璃及玻璃制品": ["SX001413", "SX00140072"],
        "大理石、瓷砖、石材及其制品": ["SX001413", "SX00140070"],
        "陶瓷制品": ["SX001413", "SX00140071"],
        "太阳能电池板": ["SX001413", "SX00140072"],
        "其他易碎品": ["SX001413", "SX00140072"],
        "新车": ["SX001417", "SX00140089"],
        "二手车": ["SX001417", "SX00140089"],
        "冷藏食品、农副产品": ["SX001404", "SX00140019"],
        "冷藏水产品": ["SX001401", "SX00140003"],
        "其他冷藏品": ["SX001401", "SX00140002"],
        "9类危险品": ["SX001406", "SX00140040"],
        "8类危险品": ["SX001406", "SX00140040"],
        "6类危险品": ["SX001406", "SX00140040"],
        "5类危险品": ["SX001406", "SX00140040"],
        "4类危险品": ["SX001406", "SX00140040"],
        "3类危险品": ["SX001406", "SX00140040"],
        "2类危险品": ["SX001406", "SX00140040"],
        "煤、炭": ["SX001405", "SX00140028"],
        "矿石、矿粉、矿砂等": ["SX001405", "SX00140027"],
        "其他矿产资源类": ["SX001405", "SX00140027"],
        "对运输有防震动、防倾斜、防尘等特殊要求的仪器": ["SX001418", "SX00140092"],
        "目的地国家无法维修的仪器": ["SX001418", "SX00140092"],
        "单件货物保额超过RMB200万元的仪器": ["SX001418", "SX00140092"]
      },
      "transport_type_mapping": {
        "3": {
          "厢式货车": ["SX001501", "01"],
          "非厢式货车": ["SX001501", "05"]
        },
        "4": {
          "厢式货车": ["SX001503", "01"],
          "非厢式货车": ["SX001503", "02"]
        },
        "5": {
          "厢式货车": ["SX001502", "01"],
          "非厢式货车": ["SX001502", "02"]
        },
        "6": {
          "厢式货车": ["SX001505", "01"],
          "非厢式货车": ["SX001505", "02"]
        }
      },
      "clause_mapping": {
        "JBX": ["SX300211", "基本险"],
        "ZHX": ["SX300212", "综合险"]
      },
      "default_phone": "18926805333",
      "fixed_values": {
        "survey_address_id": "501422495713",
        "survey_address": "17/F,Block B,Center Plaza 161 Linhexi Av.,Tianhe District, Guangzhou TEL:4006095509 FAX: 020-87567201",
        "from_country": "HTC01",
        "to_country": "HTC01",
        "id_type": "99"
      }
    },
    "sinosig": {
      "name": "中华联合",
      "modes": ["API_SINOSIG", "API_SINOSIG_QZ"],
      "type": "api",
      "data_format": "xml",
      "product_settings": {
        "2020022194297": {
          "name": "保呀产品",
          "sys_flag": "BAOYA",
          "com_code": "07710200",
          "protocol_no": "10771YAB02023000006",
          "operate_code": "BAOYA",
          "underwrite_status": "00",
          "lading_no_field": "freightNo",
          "invoice_no_field": "invNo",
          "claim_site_processing": "convert_address_and_cut_18_chars"
        },
        "default": {
          "name": "阿拉丁产品",
          "sys_flag": "aladdin",
          "com_code": "07514300",
          "protocol_no": "10771YAB02020000035",
          "operate_code": "aladdin",
          "underwrite_status": "01",
          "lading_no_format": "invNo + '-' + orderNo",
          "invoice_no": "",
          "claim_site": "中国"
        }
      },
      "packer_code_mapping": {
        "裸装": ["024", "标准包装"],
        "散装": ["024", "标准包装"],
        "纸箱": ["002", "纸箱"],
        "木箱": ["001", "木箱"],
        "捆包": ["024", "标准包装"],
        "袋装": ["023", "袋子"],
        "篓装": ["024", "标准包装"],
        "托盘": ["020", "托盘"],
        "桶装": ["019", "桶"],
        "罐装": ["024", "标准包装"]
      },
      "special_processing": {
        "percent_encoding": "将%替换为&#37;",
        "xml_encoding": "UTF-8",
        "send_seq_generation": "md5(orderNo)",
        "class_code": "09",
        "is_trans_code": "false"
      }
    },
    "starr": {
      "name": "史带财险",
      "modes": ["API_STARR"],
      "type": "api",
      "data_format": "json",
      "fixed_config": {
        "partner_code": "YOUFU",
        "partner_key": "km2a0f13c9dkb8",
        "product_code": 60021,
        "operation_type": "006",
        "pay_treatment_method": "2",
        "duration_type": "M",
        "duration": 1,
        "cargo_transport_way": "3",
        "email": "<EMAIL>"
      },
      "identity_processing": {
        "id_card_length_check": 15,
        "birth_extraction": "substr(idcard, 6, 8) for 18-digit, '19' + substr(idcard, 6, 6) for 15-digit",
        "gender_extraction": "last_digit % 2 ? 'M' : 'F'",
        "company_detection": "strstr(name, '公司')",
        "company_part_type": "QY",
        "company_card_type": "104",
        "individual_part_type": "GR",
        "individual_card_type": "1"
      },
      "special_processing": {
        "start_time_offset": "+24小时",
        "key_generation": "MD5(PartnerCode + PartnerKey + TransCode).toUpperCase()",
        "trans_code_generation": "create_guid()",
        "ph_rel_to_ins": "5"
      }
    },
    "bdyf": {
      "name": "第三方平台",
      "modes": ["AUTO_BDYF"],
      "type": "automation",
      "products": {
        "pingan_puhuo_jb_zhengzhou": {
          "product_name": "郑州平安普货（西藏除外 剔除罐头、木材、原棉，限额300万）(基本险)",
          "company": "pingan",
          "company_logo": "images/tb16.png",
          "insure_type": "puhuo",
          "clause_type": "jb",
          "rate": "1.2"
        },
        "pingan_puhuo_zh_zhengzhou": {
          "product_name": "郑州平安普货（剔除罐头木材 原棉 不保裸装和薄膜包装）西藏除外，限额300万(综合险)",
          "company": "pingan",
          "company_logo": "images/tb16.png",
          "insure_type": "puhuo",
          "clause_type": "zh",
          "rate": "1.2"
        },
        "pingan_shangpinche_jb_": {
          "product_name": "平安商品车( 限额200万)(基本险)",
          "company": "pingan",
          "company_logo": "images/tb16.png",
          "insure_type": "shangpinche",
          "clause_type": "jb",
          "rate": "1.5"
        },
        "taipingyang_puhuo_jb_": {
          "product_name": "上海太平洋普货(基本险)",
          "company": "taipingyang",
          "company_logo": "images/tb17.png",
          "insure_type": "puhuo",
          "clause_type": "jb",
          "rate": "1.2"
        },
        "pingan_puhuo_jb_nanjing": {
          "product_name": "南京平安普货（限额200）(基本险)",
          "company": "pingan",
          "company_logo": "images/tb16.png",
          "insure_type": "puhuo",
          "clause_type": "jb",
          "rate": "1"
        },
        "taipingyang_xianhuo_lc_yunnan": {
          "product_name": "云南太平洋冷藏一切险（青海、西藏、内蒙古除外，被保人为货主 限额100万）(冷藏险)",
          "company": "taipingyang",
          "company_logo": "images/tb17.png",
          "insure_type": "xianhuo",
          "clause_type": "lc",
          "rate": "2.5"
        },
        "renbao_puhuo_jb_hangzhou": {
          "product_name": "杭州人保普货（ 西藏 青海 除外   被保人货主、物流公司、司机  限额200万)(基本险)",
          "company": "renbao",
          "company_logo": "images/tb14.png",
          "insure_type": "puhuo",
          "clause_type": "jb",
          "rate": "1"
        },
        "renbao_puhuo_zh_hangzhou": {
          "product_name": "杭州人保普货（ 西藏青海除外 西藏 青海 除外  剔除纸品， 被保人货主、物流公司、司机  限额200万）(综合险)",
          "company": "renbao",
          "company_logo": "images/tb14.png",
          "insure_type": "puhuo",
          "clause_type": "zh",
          "rate": "1.3"
        }
      },
      "special_processing": {
        "amount_unit": "万元",
        "amount_conversion": "insuredAmount / 10000",
        "trans_type": "汽运",
        "load_type": "非集装箱",
        "contract_no_format": "orderNo + '/' + invNo",
        "null_value_handling": "empty值转为空字符串",
        "numeric_value_handling": "数字值转为字符串"
      }
    }
  },
  "common_business_rules": {
    "time_processing": {
      "departure_time_adjustment": {
        "same_day_offset": "1小时",
        "minute_threshold": 50,
        "additional_offset_when_over_threshold": "1小时",
        "next_day_reset": "00:00"
      },
      "failure_detection_timeout": {
        "pingan_cbec": "2小时",
        "other_modes": "10分钟"
      }
    },
    "rate_conversion": {
      "formula": "platform_rate(万分之几) / 10 = insurance_rate(千分之几)",
      "description": "平台费率单位是万分之几，保险公司费率单位是千分之几"
    },
    "invoice_number_format": {
      "platform_5": "invNo + ' / ' + freightNo",
      "other_platforms": "orderNo + ' / ' + invNo"
    },
    "amount_formatting": {
      "precision": 2,
      "function": "sprintf('%.2f', floatval(amount))"
    }
  },
  "go_architecture_design": {
    "microservices": {
      "api_gateway": {
        "description": "API网关服务",
        "responsibilities": ["路由", "认证", "限流", "日志"]
      },
      "insurance_service": {
        "description": "保险业务服务",
        "responsibilities": ["保单处理", "数据转换", "状态管理"]
      },
      "adapter_service": {
        "description": "保险公司适配器服务",
        "responsibilities": ["第三方接口调用", "数据格式转换", "错误处理"]
      },
      "notification_service": {
        "description": "通知服务",
        "responsibilities": ["推送通知", "邮件发送", "回调处理"]
      },
      "scheduler_service": {
        "description": "定时任务服务",
        "responsibilities": ["异常检测", "数据同步", "邮件报备"]
      }
    },
    "data_structures": {
      "insurance_request": {
        "platform_id": "int64",
        "product_id": "int64",
        "order_no": "string",
        "mode": "string",
        "holder_name": "string",
        "recognizee_name": "string",
        "goods_name": "string",
        "insured_amount": "float64",
        "premium": "float64",
        "departure_date": "int64",
        "from_loc": "string",
        "to_loc": "string",
        "via_loc": "*string",
        "transport": "*string",
        "inv_no": "*string"
      },
      "adapter_interface": {
        "methods": [
          "Process(ctx context.Context, data InsuranceData) (AdapterResponse, error)",
          "Validate(data InsuranceData) error",
          "Transform(data InsuranceData) (map[string]interface{}, error)"
        ]
      }
    },
    "configuration_management": {
      "config_sources": ["环境变量", "配置文件", "配置中心"],
      "hot_reload": true,
      "validation": "结构体标签验证"
    },
    "error_handling": {
      "error_types": ["业务错误", "系统错误", "网络错误"],
      "retry_strategy": "指数退避",
      "circuit_breaker": "熔断器模式"
    },
    "monitoring": {
      "metrics": ["Prometheus", "Grafana"],
      "tracing": ["Jaeger", "OpenTelemetry"],
      "logging": ["结构化日志", "日志聚合"]
    }
  }
}
