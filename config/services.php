<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Stripe, Mailgun, SparkPost and others. This file provides a sane
    | default location for this type of information, allowing packages
    | to have a conventional place to find your various credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
    ],

    'ses' => [
        'key' => env('SES_KEY'),
        'secret' => env('SES_SECRET'),
        'region' => 'us-east-1',
    ],

    'sparkpost' => [
        'secret' => env('SPARKPOST_SECRET'),
    ],

    'stripe' => [
        'model' => App\User::class,
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
    ],

    'by_mail' => [
        'driver' => 'smtp',
        'host' => env('BY_MAIL_HOST'),
        'port' => '465',
        'from' => [
            'address' => env('BY_MAIL_FROM_ADDRESS'),
            'name' => env('BY_MAIL_FROM_NAME'),
        ],
        'encryption' => 'ssl',
        'username' => env('BY_MAIL_USERNAME'),
        'password' => env('BY_MAIL_PASSWORD'),
    ],

    'other_mail' => [
        'driver' => 'smtp',
        'host' => env('OTHER_MAIL_HOST'),
        'port' => '465',
        'from' => [
            'address' => env('OTHER_MAIL_FROM_ADDRESS'),
            'name' => env('OTHER_MAIL_FROM_NAME'),
        ],
        'encryption' => 'ssl',
        'username' => env('OTHER_MAIL_USERNAME'),
        'password' => env('OTHER_MAIL_PASSWORD'),
    ]

];
