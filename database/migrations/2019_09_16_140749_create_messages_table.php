<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('platform_id');
            $table->unsignedInteger('product_id');
            $table->string('mode', 20);
            $table->string('order_no', 50);
            $table->string('apply_no', 50)->nullable();
            $table->string('policy_no', 50)->nullable();
            $table->timestamp('done_at')->nullable();
            $table->tinyInteger('is_locked')->default('0');
            $table->tinyInteger('status')->default('1');
            $table->softDeletes();    
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('messages');
    }
}
