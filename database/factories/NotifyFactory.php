<?php

use <PERSON>aker\Generator as Faker;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(App\Models\Notify::class, function (Faker $faker) {
    return [
        'message_id' => $faker->numberBetween(1, 200),
        'content' => 'hi',
        'url' => $faker->url,
        'done_at' => $faker->date("Y-m-d H:i:s", 'now'),
        'callback' => 'hello',
        'status' => 0,
        'created_at' => $faker->date("Y-m-d H:i:s", 'now'),
        'updated_at' => $faker->date("Y-m-d H:i:s", 'now'),
    ];
});
