<?php

use <PERSON>aker\Generator as Faker;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(App\Models\Message::class, function (Faker $faker) {
    return [
        'platform_id' => 1,
        'product_id' => 1,
        'mode' => 'AUTO_PICC',
        'order_no' => $faker->words(3, true),
        'apply_no' => $faker->words(3, true),
        'policy_no' => $faker->words(3, true),
        'done_at' => $faker->date("Y-m-d H:i:s", 'now'),
        'is_locked' => 0,
        'status' => 1,
        'created_at' => $faker->date("Y-m-d H:i:s", 'now'),
        'updated_at' => $faker->date("Y-m-d H:i:s", 'now'),
    ];
});
