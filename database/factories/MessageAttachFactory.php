<?php

use <PERSON>aker\Generator as Faker;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(App\Models\MessageAttach::class, function (Faker $faker) {
    $json = '{
        "holderName": "福建中保在线网络科技有限公司（ts）",
        "holderAddr": "",
        "recognizeeAddr": "",
        "documentType": "其他",
        "recognizeeIdenty": "",
        "recognizeePhone": "",
        "recognizeeOrg": "",
        "recognizeeName": "测试",
        "invNo": "测试1568689783",
        "goodsName": "测试",
        "weights": "",
        "goodsTypeID": "624",
        "quantity": "测试",
        "pack": "",
        "transportTypeID": "5",
        "transport": "测试",
        "transportNo": "",
        "fromLoc": "测试",
        "viaLoc": ""
    }';
    return [
        'message_id' => $faker->numberBetween(1, 200),
        'source' => $json,
        'content' => $json,
        'callback' => $json,
        'created_at' => $faker->date("Y-m-d H:i:s", 'now'),
        'updated_at' => $faker->date("Y-m-d H:i:s", 'now'),
    ];
});
