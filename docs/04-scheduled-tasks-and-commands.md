# 定时任务与命令行脚本

## 1. 任务调度概述

### 1.1 调度配置
系统使用Laravel的任务调度器，在 `app/Console/Kernel.php` 中配置：

```php
protected function schedule(Schedule $schedule)
{
    // 录单异常事件处理
    $schedule->command('messages:failure-event')->everyFiveMinutes();
    
    // 检查优孚录单报文状态
    $schedule->command('messages:checking-yf-policy')->everyMinute();
    
    // 自动发送报备邮件
    $schedule->command('autoins:send-mail')->everyTenMinutes();
    
    // 自动发送子系统报备邮件
    $schedule->command('autoins:send-sub-system-mail')->everyTenMinutes();
}
```

### 1.2 任务执行频率
| 任务 | 频率 | 描述 |
|------|------|------|
| 录单异常事件 | 每5分钟 | 检测录单失败并发送回调通知 |
| 优孚保单检查 | 每1分钟 | 检查优孚平台录单异常保单 |
| 报备邮件发送 | 每10分钟 | 发送主系统报备邮件 |
| 子系统邮件发送 | 每10分钟 | 发送子系统报备邮件 |

## 2. 核心命令详解

### 2.1 录单异常事件处理 (MessagesFailure)

#### 2.1.1 命令签名
```bash
php artisan messages:failure-event
```

#### 2.1.2 执行逻辑
```php
public function handle()
{
    Message::where('status', 1)
        ->where(function ($q) {
            $q->where(function ($q) {
                // 平安跨境电商特殊处理：2小时后才算失败
                $q->where('mode', 'AUTO_PINGAN_CBEC')
                    ->whereBetween('done_at', [
                        Carbon::now()->subMinute(120)->startOfMinute(), 
                        Carbon::now()->subMinute(5)
                    ]);
            })->orWhere(function ($q) {
                // 其他模式：10分钟后算失败
                $q->where('mode', '!=', 'AUTO_PINGAN_CBEC')
                    ->whereBetween('done_at', [
                        Carbon::now()->subMinute(10)->startOfMinute(), 
                        Carbon::now()->subMinute(5)
                    ]);
            });
        })
        ->with(['notifies', 'attaches'])
        ->chunk(100, function ($messages) {
            $messages->each(function ($message) {
                if ($this->isTriggerable($message)) {
                    // 标记为已发送，避免重复处理
                    Cache::put('failure.events.' . $message['id'], Carbon::now(), Carbon::now()->addDay(5));
                    
                    // 发送失败回调通知
                    $this->sendFailureEvent($this->baoyaNotificationURL($message), [
                        'event' => 'failure',
                        'sys_order_no' => $message['order_no'],
                        'reason' => $this->guessFailureReason($message['attaches']['callback']),
                    ]);
                }
            });
        });
}
```

#### 2.1.3 失败原因分析
```php
protected function guessFailureReason($callback)
{
    $callback = json_decode($callback, true);
    
    // 根据回调信息判断失败原因
    if (isset($callback['error'])) {
        return $callback['error'];
    }
    
    if (isset($callback['message'])) {
        return $callback['message'];
    }
    
    return '录单超时或系统异常';
}
```

### 2.2 优孚保单检查 (CheckingYFPolicy)

#### 2.2.1 命令签名
```bash
php artisan messages:checking-yf-policy
```

#### 2.2.2 执行逻辑
```php
public function handle()
{
    DB::transaction(function (){
        $messages = Message::where('status', 1)
            ->where('platform_id', 1)           // 优孚平台ID
            ->whereNull('apply_no')             // 未获取投保单号
            ->whereNull('policy_no')            // 未获取保单号
            ->where('handle_num', '<', 1)       // 处理次数限制
            ->with('attaches')
            ->orderBy('id', 'desc')
            ->get();
            
        foreach ($messages as $message){
            switch ($message['mode']){
                case 'AUTO_CPIC':
                    $this->handleCPIC($message);
                    break;
                default:
                    break;
            }
        }
    });
}
```

#### 2.2.3 太保保单特殊处理
```php
protected function handleCPIC($message)
{
    // 只处理当天的保单
    if(date('Y-m-d') == date('Y-m-d', strtotime($message['created_at']))){
        // 超过5分钟未处理的保单
        if((time() - strtotime($message['created_at'])) > 300){
            // 重置状态，增加处理次数
            $message->update([
                'status' => 0, 
                'is_entry' => 0, 
                'handle_num' => $message['handle_num'] + 1, 
                'error_num' => $message['error_num'] + 1
            ]);
            
            // 更新起运时间
            $content = json_decode($message['attaches']['content'], true);
            if($content['message']['cargoInfoDto']['sailDateHours'] <= date('H')){
                $content['message']['cargoInfoDto']['sailDateHours'] = (string) (date('H') + 1);
                $message->attaches()->update(['content' => jsonFormat($content)]);
            }
        }
    }
}
```

### 2.3 自动发送报备邮件 (AutoSendMail)

#### 2.3.1 命令签名
```bash
php artisan autoins:send-mail
```

#### 2.3.2 执行逻辑
```php
public function handle()
{
    // 查询需要发送邮件的报文
    $messages = Message::whereIn('status', [0, 1])
        ->where('created_at', '<', date('Y-m-d H:i:s', (time() - 10 * 60)))
        ->where(function ($q) {
            $q->where(function ($q) {
                // 人保特殊处理：需要有投保单号和保单号
                $q->whereIn('mode', ['AUTO_PICCLTD_INTL', 'AUTO_PICCLTD'])
                    ->whereNull('apply_no')
                    ->whereNull('policy_no');
            })->orWhere(function ($q) {
                // 其他模式直接处理
                $q->whereNotIn('mode', ['AUTO_PICCLTD_INTL', 'AUTO_PICCLTD']);
            });
        })
        ->with(['platform', 'product'])
        ->get();
        
    foreach ($messages as $message) {
        try {
            // 检查是否已发送邮件
            if ($this->isMail($message->platform_id, $message->order_no)) {
                continue;
            }
            
            // 查找对应的子系统
            $modelName = null;
            $sender = null;
            foreach (subsystem() as $key => $value) {
                if ($value[3] == $message->platform->app_id) {
                    $modelName = $key;
                    $sender = $value[4];
                }
            }
            
            if (!$modelName || !$sender) {
                continue;
            }
            
            // 查询子系统保单数据
            $modelPath = '\\App\\Models\\' . $modelName;
            $model = new $modelPath();
            $data = $model->index(subsystem()[$modelName][0], $message->order_no);
            
            if (!$data) {
                continue;
            }
            
            // 发送邮件
            $this->sendMail($message, $data, $sender);
            
        } catch (\Exception $e) {
            Log::error('SendMailError:' . $e->getMessage());
            continue;
        }
    }
}
```

#### 2.3.3 邮件内容构建
```php
protected function sendMail($message, $data, $sender)
{
    $mail_message = [
        'title' => $message->platform->title,
        'create_time' => $data->create_time,
        'species' => $data->species,
        'insured_name' => $data->insured_name,
        'departure' => $data->departure,
        'stopovers' => $data->stopovers,
        'destination' => $data->destination,
        'start_time' => $data->start_time,
        'goods_name' => $data->goods_name,
        'goods_amount' => $data->goods_amount,
        'pack_type' => $data->pack_type,
        'vehicle_license_no' => $data->vehicle_license_no,
        'coverage' => '￥' . $data->coverage . ' (' . num_to_rmb($data->coverage) . ')',
        'deductible' => $data->deductible,
        'specials' => $data->specials,
        'order_no' => $message->order_no
    ];
    
    $emailData = (object)[
        'content' => $mail_message,
        'sender' => $sender,
        'theme' => '【' . $message->order_no . '】投保邮件报备',
        'platform_id' => $message->platform_id,
        'order_no' => $message->order_no,
        'user' => array_merge(mail_address(), $data['email'])
    ];
    
    dispatch(new SendReminderEmail($emailData));
}
```

### 2.4 子系统报备邮件 (AutoSubSystemSendMail)

#### 2.4.1 命令签名
```bash
php artisan autoins:send-sub-system-mail
```

#### 2.4.2 执行逻辑
```php
public function handle()
{
    foreach (subsystem() as $key => $value) {
        $modelPath = '\\App\\Models\\' . $key;
        $model = new $modelPath();
        
        // 时间范围：10-40分钟前的数据
        $time1 = time() - 10 * 60;
        $time2 = time() - 40 * 60;
        $sender = $value[4];
        
        // 根据时间字段类型处理
        if (!$value[2]) { // 非时间戳格式
            $time1 = date('Y-m-d H:i:s', $time1);
            $time2 = date('Y-m-d H:i:s', $time2);
        }
        
        // 查询子系统未提交的保单
        $data = $model->indexs($value[1], $time1, $time2);
        
        // 获取平台数据
        $platform = Platform::where('app_id', $value[3])->first();
        
        if (isset($data)) {
            foreach ($data as $k2 => $v2) {
                // 检查是否已提交到自动录单系统
                $message_data = Message::where('platform_id', $platform->id)
                    ->where('order_no', $v2[$value[0]])
                    ->first();
                    
                if (!$message_data) {
                    // 检查是否已发送邮件
                    if (!$this->isMail($platform->id, $v2[$value[0]])) {
                        $this->sendSubSystemMail($v2, $value, $platform, $sender);
                    }
                }
            }
        }
    }
}
```

## 3. 队列任务系统

### 3.1 邮件发送队列 (SendReminderEmail)

#### 3.1.1 队列配置
```php
class SendReminderEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    public $tries = 3;                    // 最大重试次数
    
    public function retryUntil()
    {
        return now()->addSeconds(40);     // 重试超时时间
    }
}
```

#### 3.1.2 邮件发送逻辑
```php
public function handle()
{
    if($this->data->sender == '<EMAIL>'){
        // 使用Laravel Mail发送
        Mail::send('workbench.mail.mail', $this->data->content, function ($message){
            $message->to($this->data->user);
            $message->subject($this->data->theme);
        });
    } else {
        // 使用自定义邮件函数发送
        $content = $this->getMailContent($this->data->content);
        $setting = config('services.other_mail');
        sys_mail(implode(';',$this->data->user), '', $this->data->theme, $content, $setting);
    }
    
    // 更新邮件发送状态
    MailDelivery::where('platform_id',$this->data->platform_id)
        ->where('order_no',$this->data->order_no)
        ->update(['status'=>2,'send_time'=>date("Y-m-d H:i:s",time())]);
}
```

#### 3.1.3 失败处理
```php
public function failed(\Exception $exception)
{
    Log::error('Message:'.$exception->getMessage());
    
    // 标记邮件发送失败
    MailDelivery::where('platform_id',$this->data->platform_id)
        ->where('order_no',$this->data->order_no)
        ->update(['status'=>3]);
}
```

### 3.2 队列驱动配置
```php
// config/queue.php
'default' => env('QUEUE_DRIVER', 'sync'),

'connections' => [
    'database' => [
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
    ],
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
    ],
]
```

## 4. 子系统配置

### 4.1 子系统映射
```php
// app/Helpers/functions.php
function subsystem(){
    return [
        'DataYoufu' => [
            'SerialNo',                    // 订单号字段
            'DateTime',                    // 时间字段
            true,                          // 时间是否为时间戳
            'INS1569686500',              // 平台app_id
            '<EMAIL>'         // 邮件发送者
        ],
        'DataBaoya' => [
            'sys_order_no',
            'created_at',
            false,
            'INS1569723235',
            '<EMAIL>'
        ],
        'DataBaoyaNew' => [
            'order_no',
            'submitted_at',
            false,
            'INS1635757943',
            '<EMAIL>'
        ],
    ];
}
```

## 5. 监控与日志

### 5.1 缓存机制
- 使用Cache防止重复处理失败事件
- 缓存键格式：`failure.events.{message_id}`
- 缓存时间：5天

### 5.2 日志记录
- 邮件发送错误日志
- 任务执行状态日志
- 异常处理日志

### 5.3 状态跟踪
- 邮件发送状态：1(正在发送) → 2(已发送) / 3(发送失败)
- 报文处理状态：0(未处理) → 1(已提交) → 2(已完成)
- 处理次数限制：防止无限重试

## 6. 部署与运维

### 6.1 Cron配置
```bash
# 添加到系统crontab
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### 6.2 队列工作进程
```bash
# 启动队列工作进程
php artisan queue:work

# 使用Supervisor管理队列进程
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path-to-your-project/artisan queue:work --sleep=3 --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/path-to-your-project/worker.log
```

### 6.3 性能优化
- 使用chunk()分批处理大量数据
- 合理设置队列重试次数和超时时间
- 定期清理过期的缓存和日志文件
