# 核心业务模块分析

## 1. 报文处理模块 (Message Processing)

### 1.1 模块概述
报文处理是系统的核心功能，负责接收投保平台的保单数据，转换为保险公司格式，并管理保单录入状态。

### 1.2 核心流程
```
投保平台数据 → JWT认证 → 数据验证 → 格式转换 → 保险公司API → 状态更新
```

### 1.3 关键组件

#### 1.3.1 主控制器
- **文件**: `app/Http/Controllers/Api/InsureController.php`
- **主要方法**: `create()` - 处理保单创建请求

#### 1.3.2 数据模型
- **Message**: 报文主表，存储保单基本信息
- **MessageAttach**: 报文附表，存储原始数据和转换后数据
- **状态管理**: 
  - `0`: 未处理 (报文尚未提交到保险公司)
  - `1`: 已提交 (已提交但不确定是否成功)
  - `2`: 已完成 (提交成功并返回保单号)
  - `-1`: 已作废

#### 1.3.3 保险公司适配器
系统支持多家保险公司，每家都有独立的控制器：
- **人保**: `PICCController`, `PICCIntlController`
- **太保**: `CPICController`, `CPICIntlController`
- **平安**: `PINGANController`, `PINGANIntlController`
- **华泰**: `HUATAIController`
- **中华联合**: `SinosigController`
- **史带**: `STARRController`
- **国人**: `GUORENController`
- **其他**: `BDYFController`, `SINOSIG_QINZHOUController`

### 1.4 数据转换逻辑
每个保险公司控制器实现 `approval()` 方法，负责：
1. 数据格式转换
2. 字段映射
3. 业务规则应用
4. API调用准备

## 2. 推送模块 (Notification System)

### 2.1 模块概述
负责保单生效后向投保平台发送回调通知，确保业务闭环。

### 2.2 核心组件

#### 2.2.1 数据模型
- **Notify**: 推送通知表
- **状态管理**:
  - `0`: 未处理 (平台提交后未生效)
  - `1`: 待发送 (保单已生效，但未发送到平台端)
  - `2`: 已发送 (已推送到平台端)
  - `3`: 发送失败 (未推送到平台端)
  - `4`: 待发送 (中意雇主待发送状态)

#### 2.2.2 失败处理机制
- **文件**: `app/Console/Commands/MessagesFailure.php`
- **功能**: 检测录入失败的消息并触发失败回调
- **执行频率**: 每5分钟
- **处理逻辑**:
  1. 查找状态为"已提交"但超时的报文
  2. 推测失败原因
  3. 发送失败回调到投保平台

### 2.3 回调机制
- 使用HTTP POST发送JSON格式数据
- 支持重试机制（最多3次）
- 记录推送日志

## 3. 平台管理模块 (Platform Management)

### 3.1 模块概述
管理接入的投保平台，生成API认证参数，控制平台访问权限。

### 3.2 核心功能

#### 3.2.1 平台注册
- **控制器**: `app/Http/Controllers/Workbench/PlatformsController.php`
- **认证机制**: JWT + app_id + secret_key
- **参数生成**:
  ```php
  $data['app_id'] = 'INS'.time();
  $data['secret_key'] = sha1($data['app_id'], FALSE);
  ```

#### 3.2.2 产品分配
- 平台可分配多个产品
- 通过 `PlatformProduct` 中间表管理关系
- 支持产品启用/禁用状态

### 3.3 认证流程
```
投保平台 → app_id + secret_key → JWT Token → API访问
```

## 4. 产品管理模块 (Product Management)

### 4.1 模块概述
管理保险产品配置，定义产品参数和保险公司映射关系。

### 4.2 核心属性
- **product_code**: 产品代码（自动生成）
- **mode**: 投保方式（决定使用哪个保险公司适配器）
- **config**: 产品配置（JSON格式）
- **account**: 保险公司账户
- **status**: 产品状态

### 4.3 投保方式映射
```php
'AUTO_PICC' => PICCController::class,
'AUTO_CPIC' => CPICController::class,
'AUTO_PINGAN' => PINGANController::class,
'API_SINOSIG' => SinosigController::class,
// ... 其他映射
```

## 5. 邮件发送模块 (Email Notification)

### 5.1 模块概述
当自动录单失败时，系统自动发送邮件通知相关人员，确保问题及时处理。

### 5.2 邮件类型

#### 5.2.1 录单失败邮件
- **触发条件**: 报文状态为未处理或已提交超过10分钟
- **发送频率**: 每10分钟检查一次
- **收件人**: 保险公司 + 系统内部人员

#### 5.2.2 子系统报备邮件
- **触发条件**: 子系统有保单但未提交到自动录单系统
- **发送频率**: 每10分钟检查一次
- **时间范围**: 10-40分钟前的保单

### 5.3 邮件发送机制

#### 5.3.1 队列处理
- **Job类**: `app/Jobs/SendReminderEmail.php`
- **队列系统**: Laravel Queue
- **失败重试**: 支持失败处理

#### 5.3.2 邮件模板
- **模板文件**: `resources/views/workbench/mail/mail.blade.php`
- **内容包含**: 保单详情、被保险人信息、货物信息等

#### 5.3.3 发送状态管理
- **MailDelivery模型**: 记录邮件发送状态
- **状态类型**:
  - `1`: 正在发送
  - `2`: 已发送
  - `3`: 发送失败

### 5.4 邮件配置
- 支持多种邮件发送方式
- 系统邮件 vs 第三方邮件服务
- 收件人地址配置化管理

## 6. 子系统集成

### 6.1 支持的子系统
```php
'DataYoufu' => ['SerialNo','DateTime',true,'INS1569686500','<EMAIL>'],
'DataBaoya' => ['sys_order_no','created_at',false,'INS1569723235','<EMAIL>'],
'DataBaoyaNew' => ['order_no','submitted_at',false,'INS1635757943','<EMAIL>'],
```

### 6.2 数据同步机制
- 定期检查子系统数据
- 自动同步未提交保单
- 邮件报备机制

## 7. 错误处理与监控

### 7.1 异常处理
- 数据库事务保护
- 详细错误日志记录
- 失败重试机制

### 7.2 状态监控
- 实时统计未处理/已提交报文数量
- 邮件发送失败监控
- 系统健康状态检查

## 8. 安全机制

### 8.1 API安全
- JWT Token认证
- 请求参数验证
- 平台权限控制

### 8.2 数据安全
- 敏感数据加密存储
- 软删除机制
- 操作日志记录
