# 重构指南与配置文件

## 1. 重构总体策略

### 1.1 重构目标
- **代码现代化**: 升级到Laravel 8+，使用现代PHP特性
- **架构优化**: 改进代码结构，提高可维护性
- **性能提升**: 优化数据库查询和缓存策略
- **安全加强**: 增强数据验证和安全防护
- **可扩展性**: 支持更多保险公司和业务场景

### 1.2 重构原则
1. **向后兼容**: 保持API接口兼容性
2. **渐进式重构**: 分模块逐步重构
3. **测试驱动**: 先写测试，再重构代码
4. **文档同步**: 重构过程中同步更新文档

### 1.3 重构阶段规划
```
阶段1: 基础设施升级 (2周)
├── Laravel框架升级
├── PHP版本升级
├── 依赖包更新
└── 基础配置调整

阶段2: 核心模块重构 (4周)
├── 数据模型重构
├── API控制器重构
├── 业务逻辑抽象
└── 错误处理优化

阶段3: 架构优化 (3周)
├── 服务层抽象
├── 事件驱动架构
├── 缓存策略优化
└── 队列系统改进

阶段4: 功能增强 (3周)
├── 监控系统集成
├── 日志系统优化
├── 性能监控
└── 安全加固

阶段5: 测试与部署 (2周)
├── 单元测试完善
├── 集成测试
├── 性能测试
└── 生产部署
```

## 2. 技术栈升级建议

### 2.1 框架升级
```php
// 当前版本: Laravel 5.5
// 目标版本: Laravel 9.x

// 升级路径
Laravel 5.5 → Laravel 5.8 → Laravel 6.x → Laravel 8.x → Laravel 9.x
```

### 2.2 PHP版本升级
```php
// 当前版本: PHP 7.0
// 目标版本: PHP 8.1

// 新特性利用
- 类型声明
- 属性提升
- 联合类型
- 命名参数
- 匹配表达式
```

### 2.3 依赖包升级
```json
{
    "require": {
        "php": "^8.1",
        "laravel/framework": "^9.0",
        "tymon/jwt-auth": "^2.0",
        "predis/predis": "^2.0",
        "guzzlehttp/guzzle": "^7.0"
    },
    "require-dev": {
        "phpunit/phpunit": "^9.0",
        "mockery/mockery": "^1.4",
        "laravel/telescope": "^4.0"
    }
}
```

## 3. 代码结构重构

### 3.1 目录结构优化
```
app/
├── Actions/              # 业务动作类
│   ├── Insurance/
│   ├── Notification/
│   └── Mail/
├── Data/                 # 数据传输对象
│   ├── InsuranceData.php
│   └── NotificationData.php
├── Enums/                # 枚举类
│   ├── InsuranceStatus.php
│   └── NotificationStatus.php
├── Events/               # 事件类
│   ├── PolicyCreated.php
│   └── PolicyFailed.php
├── Listeners/            # 事件监听器
│   ├── SendNotification.php
│   └── SendEmail.php
├── Services/             # 服务层
│   ├── InsuranceService.php
│   ├── NotificationService.php
│   └── Adapters/
│       ├── PICCAdapter.php
│       └── CPICAdapter.php
├── Repositories/         # 数据仓库
│   ├── MessageRepository.php
│   └── PlatformRepository.php
└── Exceptions/           # 自定义异常
    ├── InsuranceException.php
    └── ValidationException.php
```

### 3.2 服务层抽象
```php
// app/Services/InsuranceService.php
<?php

namespace App\Services;

use App\Data\InsuranceData;
use App\Enums\InsuranceStatus;
use App\Events\PolicyCreated;
use App\Repositories\MessageRepository;
use App\Services\Adapters\AdapterFactory;

class InsuranceService
{
    public function __construct(
        private MessageRepository $messageRepository,
        private AdapterFactory $adapterFactory
    ) {}

    public function processInsurance(InsuranceData $data): array
    {
        // 创建报文记录
        $message = $this->messageRepository->create([
            'platform_id' => $data->platformId,
            'product_id' => $data->productId,
            'order_no' => $data->orderNo,
            'status' => InsuranceStatus::PENDING,
        ]);

        try {
            // 获取适配器
            $adapter = $this->adapterFactory->make($data->mode);
            
            // 处理投保
            $result = $adapter->process($data);
            
            // 更新状态
            $this->messageRepository->update($message->id, [
                'status' => InsuranceStatus::SUBMITTED,
                'done_at' => now(),
            ]);

            // 触发事件
            event(new PolicyCreated($message, $result));

            return $result;
        } catch (\Exception $e) {
            $this->handleError($message, $e);
            throw $e;
        }
    }

    private function handleError($message, \Exception $e): void
    {
        $this->messageRepository->update($message->id, [
            'status' => InsuranceStatus::FAILED,
            'error_num' => $message->error_num + 1,
        ]);
    }
}
```

### 3.3 适配器工厂模式
```php
// app/Services/Adapters/AdapterFactory.php
<?php

namespace App\Services\Adapters;

use App\Services\Adapters\Contracts\InsuranceAdapterInterface;

class AdapterFactory
{
    private array $adapters = [
        'AUTO_PICC' => PICCAdapter::class,
        'AUTO_CPIC' => CPICAdapter::class,
        'AUTO_PINGAN' => PINGANAdapter::class,
        'API_HUATAI' => HUATAIAdapter::class,
    ];

    public function make(string $mode): InsuranceAdapterInterface
    {
        if (!isset($this->adapters[$mode])) {
            throw new \InvalidArgumentException("Unsupported insurance mode: {$mode}");
        }

        return app($this->adapters[$mode]);
    }
}

// app/Services/Adapters/Contracts/InsuranceAdapterInterface.php
<?php

namespace App\Services\Adapters\Contracts;

use App\Data\InsuranceData;

interface InsuranceAdapterInterface
{
    public function process(InsuranceData $data): array;
    public function validate(InsuranceData $data): bool;
    public function transform(InsuranceData $data): array;
}
```

### 3.4 数据传输对象
```php
// app/Data/InsuranceData.php
<?php

namespace App\Data;

use Spatie\LaravelData\Data;

class InsuranceData extends Data
{
    public function __construct(
        public int $platformId,
        public int $productId,
        public string $orderNo,
        public string $mode,
        public string $holderName,
        public string $recognizeeName,
        public string $goodsName,
        public float $insuredAmount,
        public float $premium,
        public int $departureDate,
        public string $fromLoc,
        public string $toLoc,
        public ?string $viaLoc = null,
        public ?string $transport = null,
        public ?string $invNo = null,
    ) {}

    public static function rules(): array
    {
        return [
            'platformId' => 'required|integer|exists:platforms,id',
            'productId' => 'required|integer|exists:products,id',
            'orderNo' => 'required|string|max:50',
            'mode' => 'required|string|in:AUTO_PICC,AUTO_CPIC,AUTO_PINGAN',
            'holderName' => 'required|string|max:255',
            'recognizeeName' => 'required|string|max:255',
            'goodsName' => 'required|string|max:500',
            'insuredAmount' => 'required|numeric|min:0',
            'premium' => 'required|numeric|min:0',
            'departureDate' => 'required|integer',
            'fromLoc' => 'required|string|max:255',
            'toLoc' => 'required|string|max:255',
        ];
    }
}
```

## 4. 数据库优化

### 4.1 索引优化
```sql
-- 添加复合索引
CREATE INDEX idx_messages_platform_status_created ON ato_messages(platform_id, status, created_at);
CREATE INDEX idx_messages_order_platform ON ato_messages(order_no, platform_id);
CREATE INDEX idx_notifies_message_status ON ato_notifies(message_id, status);

-- 添加分区表（按月分区）
ALTER TABLE ato_messages PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ... 更多分区
);
```

### 4.2 查询优化
```php
// 使用查询构建器优化
class MessageRepository
{
    public function findPendingMessages(int $limit = 100): Collection
    {
        return Message::select(['id', 'platform_id', 'product_id', 'order_no', 'status'])
            ->with(['platform:id,title,app_id', 'product:id,mode'])
            ->whereIn('status', [0, 1])
            ->where('created_at', '<', now()->subMinutes(10))
            ->limit($limit)
            ->get();
    }

    public function findFailedMessages(): Collection
    {
        return Message::where('status', 1)
            ->where(function ($query) {
                $query->where(function ($q) {
                    $q->where('mode', 'AUTO_PINGAN_CBEC')
                        ->where('done_at', '<', now()->subHours(2));
                })->orWhere(function ($q) {
                    $q->where('mode', '!=', 'AUTO_PINGAN_CBEC')
                        ->where('done_at', '<', now()->subMinutes(10));
                });
            })
            ->with(['notifies', 'attaches'])
            ->chunk(100);
    }
}
```

## 5. 缓存策略优化

### 5.1 Redis缓存配置
```php
// config/cache.php
'stores' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
    ],
],

// config/database.php
'redis' => [
    'client' => env('REDIS_CLIENT', 'phpredis'),
    'options' => [
        'cluster' => env('REDIS_CLUSTER', 'redis'),
        'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
    ],
    'default' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_DB', '0'),
    ],
    'cache' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_CACHE_DB', '1'),
    ],
];
```

### 5.2 缓存使用策略
```php
// 平台产品缓存
class PlatformService
{
    public function getPlatformProducts(int $platformId): Collection
    {
        return Cache::tags(['platforms', 'products'])
            ->remember("platform.{$platformId}.products", 3600, function () use ($platformId) {
                return Platform::find($platformId)->products;
            });
    }

    public function clearPlatformCache(int $platformId): void
    {
        Cache::tags(['platforms'])->flush();
    }
}

// 配置缓存
class ConfigService
{
    public function getInsuranceConfig(string $mode): array
    {
        return Cache::remember("insurance.config.{$mode}", 86400, function () use ($mode) {
            return Product::where('mode', $mode)->first()->config ?? [];
        });
    }
}
```

## 6. 事件驱动架构

### 6.1 事件定义
```php
// app/Events/PolicyCreated.php
<?php

namespace App\Events;

use App\Models\Message;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PolicyCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Message $message,
        public array $result
    ) {}
}

// app/Events/PolicyFailed.php
<?php

namespace App\Events;

use App\Models\Message;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PolicyFailed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Message $message,
        public \Exception $exception
    ) {}
}
```

### 6.2 事件监听器
```php
// app/Listeners/SendNotification.php
<?php

namespace App\Listeners;

use App\Events\PolicyCreated;
use App\Services\NotificationService;

class SendNotification
{
    public function __construct(
        private NotificationService $notificationService
    ) {}

    public function handle(PolicyCreated $event): void
    {
        $this->notificationService->sendPolicyNotification(
            $event->message,
            $event->result
        );
    }
}

// app/Listeners/SendEmail.php
<?php

namespace App\Listeners;

use App\Events\PolicyCreated;
use App\Jobs\SendReminderEmail;

class SendEmail
{
    public function handle(PolicyCreated $event): void
    {
        dispatch(new SendReminderEmail($event->message));
    }
}
```

## 7. 配置文件模板

### 7.1 环境配置模板
```bash
# .env.production
APP_NAME="AutoIns System"
APP_ENV=production
APP_KEY=base64:your_32_character_key_here
APP_DEBUG=false
APP_URL=https://autoins.yourdomain.com

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=autoins_prod
DB_USERNAME=autoins_user
DB_PASSWORD=secure_password

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=redis_password
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1

# 队列配置
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database

# 缓存配置
CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_TTL=60
JWT_REFRESH_TTL=20160

# 邮件配置
MAIL_MAILER=smtp
MAIL_HOST=smtp.yourdomain.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mail_password
MAIL_ENCRYPTION=ssl
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="AutoIns System"

# 日志配置
LOG_CHANNEL=daily
LOG_LEVEL=info
LOG_DAYS=14

# 监控配置
TELESCOPE_ENABLED=false
```

### 7.2 Docker配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
    volumes:
      - ./storage:/var/www/html/storage
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: autoins
      MYSQL_USER: autoins_user
      MYSQL_PASSWORD: user_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  mysql_data:
  redis_data:
```

### 7.3 Supervisor配置
```ini
# /etc/supervisor/conf.d/autoins-worker.conf
[program:autoins-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/worker.log
stopwaitsecs=3600

[program:autoins-scheduler]
process_name=%(program_name)s
command=/bin/bash -c "while [ true ]; do (php /var/www/html/artisan schedule:run --verbose --no-interaction &); sleep 60; done"
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/scheduler.log
```

## 8. 重构检查清单

### 8.1 代码质量检查
- [ ] 所有类都有类型声明
- [ ] 使用现代PHP特性（属性提升、联合类型等）
- [ ] 遵循PSR-12编码标准
- [ ] 所有方法都有返回类型声明
- [ ] 使用依赖注入而非静态调用

### 8.2 架构检查
- [ ] 业务逻辑从控制器中抽离到服务层
- [ ] 使用事件驱动架构处理副作用
- [ ] 数据访问通过仓库模式
- [ ] 使用DTO传输数据
- [ ] 适配器模式处理第三方集成

### 8.3 性能检查
- [ ] 数据库查询优化
- [ ] 添加适当的索引
- [ ] 使用缓存减少重复计算
- [ ] 队列处理耗时任务
- [ ] 使用连接池优化数据库连接

### 8.4 安全检查
- [ ] 输入验证和过滤
- [ ] SQL注入防护
- [ ] XSS防护
- [ ] CSRF保护
- [ ] 敏感数据加密存储

### 8.5 测试检查
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试覆盖核心流程
- [ ] API测试覆盖所有接口
- [ ] 性能测试验证响应时间
- [ ] 安全测试验证防护措施
