# 配置管理与环境设置

## 1. 环境配置概述

### 1.1 基础应用配置
```php
// config/app.php
'name' => env('APP_NAME', 'Laravel'),
'env' => env('APP_ENV', 'production'),
'debug' => env('APP_DEBUG', false),
'url' => env('APP_URL', 'http://localhost'),
'timezone' => 'Asia/Shanghai',
'locale' => 'zh-CN',
```

### 1.2 环境变量模板
```bash
# .env.example
APP_NAME=AutoIns
APP_ENV=production
APP_KEY=base64:generated_key
APP_DEBUG=false
APP_URL=https://autoins.example.com

# 主数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=autoins
DB_USERNAME=autoins_user
DB_PASSWORD=secure_password

# 队列配置
QUEUE_DRIVER=database
CACHE_DRIVER=redis
SESSION_DRIVER=redis

# JWT认证配置
JWT_SECRET=generated_jwt_secret
JWT_TTL=60

# 邮件配置
MAIL_DRIVER=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mail_password
MAIL_ENCRYPTION=ssl
```

## 2. 数据库连接配置

### 2.1 多数据库架构
系统支持7个数据库连接，用于集成不同的子系统：

```php
// config/database.php
'connections' => [
    // 主数据库 - 自动录单系统
    'mysql' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST', '127.0.0.1'),
        'port' => env('DB_PORT', '3306'),
        'database' => env('DB_DATABASE', 'autoins'),
        'username' => env('DB_USERNAME', 'root'),
        'password' => env('DB_PASSWORD', 'root'),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => 'ato_',
        'strict' => true,
    ],
    
    // 优孚系统数据库
    'mysql_yf' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST_YF', '127.0.0.1'),
        'port' => env('DB_PORT_YF', '3306'),
        'database' => env('DB_DATABASE_YF', 'forge'),
        'username' => env('DB_USERNAME_YF', 'forge'),
        'password' => env('DB_PASSWORD_YF', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
    ],
    
    // 保呀系统数据库(旧版)
    'mysql_by' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST_BY', '127.0.0.1'),
        'port' => env('DB_PORT_BY', '3306'),
        'database' => env('DB_DATABASE_BY', 'forge'),
        'username' => env('DB_USERNAME_BY', 'forge'),
        'password' => env('DB_PASSWORD_BY', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
    ],
    
    // 保呀系统数据库(新版)
    'mysql_by_new' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST_BY_NEW', '127.0.0.1'),
        'port' => env('DB_PORT_BY_NEW', '3306'),
        'database' => env('DB_DATABASE_BY_NEW', 'forge'),
        'username' => env('DB_USERNAME_BY_NEW', 'forge'),
        'password' => env('DB_PASSWORD_BY_NEW', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
    ],
    
    // 阿拉丁系统数据库
    'mysql_ald' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST_ALD', '127.0.0.1'),
        'port' => env('DB_PORT_ALD', '3306'),
        'database' => env('DB_DATABASE_ALD', 'forge'),
        'username' => env('DB_USERNAME_ALD', 'forge'),
        'password' => env('DB_PASSWORD_ALD', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
    ],
    
    // 中联系统数据库
    'mysql_zl' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST_ZL', '127.0.0.1'),
        'port' => env('DB_PORT_ZL', '3306'),
        'database' => env('DB_DATABASE_ZL', 'forge'),
        'username' => env('DB_USERNAME_ZL', 'forge'),
        'password' => env('DB_PASSWORD_ZL', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
    ],
    
    // 其他系统数据库
    'mysql_lby' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST_LBY', '127.0.0.1'),
        'port' => env('DB_PORT_LBY', '3306'),
        'database' => env('DB_DATABASE_LBY', 'forge'),
        'username' => env('DB_USERNAME_LBY', 'forge'),
        'password' => env('DB_PASSWORD_LBY', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
    ],
];
```

### 2.2 环境变量配置
```bash
# 主数据库
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=autoins
DB_USERNAME=autoins_user
DB_PASSWORD=secure_password

# 优孚系统数据库
DB_HOST_YF=*************
DB_PORT_YF=3306
DB_DATABASE_YF=youfu_db
DB_USERNAME_YF=youfu_user
DB_PASSWORD_YF=youfu_password

# 保呀系统数据库(旧版)
DB_HOST_BY=*************
DB_PORT_BY=3306
DB_DATABASE_BY=baoya_old_db
DB_USERNAME_BY=baoya_user
DB_PASSWORD_BY=baoya_password

# 保呀系统数据库(新版)
DB_HOST_BY_NEW=*************
DB_PORT_BY_NEW=3306
DB_DATABASE_BY_NEW=baoya_new_db
DB_USERNAME_BY_NEW=baoya_new_user
DB_PASSWORD_BY_NEW=baoya_new_password

# 阿拉丁系统数据库
DB_HOST_ALD=*************
DB_PORT_ALD=3306
DB_DATABASE_ALD=aladdin_db
DB_USERNAME_ALD=aladdin_user
DB_PASSWORD_ALD=aladdin_password

# 中联系统数据库
DB_HOST_ZL=*************
DB_PORT_ZL=3306
DB_DATABASE_ZL=zhonglian_db
DB_USERNAME_ZL=zhonglian_user
DB_PASSWORD_ZL=zhonglian_password

# 其他系统数据库
DB_HOST_LBY=*************
DB_PORT_LBY=3306
DB_DATABASE_LBY=other_db
DB_USERNAME_LBY=other_user
DB_PASSWORD_LBY=other_password
```

## 3. 认证与安全配置

### 3.1 JWT认证配置
```php
// config/jwt.php
'secret' => env('JWT_SECRET'),
'ttl' => env('JWT_TTL', 60),
'refresh_ttl' => env('JWT_REFRESH_TTL', 20160),
'algo' => env('JWT_ALGO', 'HS256'),
'required_claims' => [
    'iss',
    'iat',
    'exp',
    'nbf',
    'sub',
    'jti',
],
'persistent_claims' => [],
'lock_subject' => true,
'leeway' => env('JWT_LEEWAY', 0),
'blacklist_enabled' => env('JWT_BLACKLIST_ENABLED', true),
'blacklist_grace_period' => env('JWT_BLACKLIST_GRACE_PERIOD', 0),
'decrypt_cookies' => false,
'providers' => [
    'jwt' => Tymon\JWTAuth\Providers\JWT\Lcobucci::class,
    'auth' => Tymon\JWTAuth\Providers\Auth\Illuminate::class,
    'storage' => Tymon\JWTAuth\Providers\Storage\Illuminate::class,
],
```

### 3.2 认证守卫配置
```php
// config/auth.php
'defaults' => [
    'guard' => 'web',
    'passwords' => 'users',
],

'guards' => [
    'web' => [
        'driver' => 'session',
        'provider' => 'users',
    ],
    'api' => [
        'driver' => 'token',
        'provider' => 'users',
    ],
],

'providers' => [
    'users' => [
        'driver' => 'eloquent',
        'model' => App\Models\User::class,
    ],
],
```

## 4. 邮件服务配置

### 4.1 主邮件配置
```php
// config/mail.php
'driver' => env('MAIL_DRIVER', 'smtp'),
'host' => env('MAIL_HOST', 'smtp.mailgun.org'),
'port' => env('MAIL_PORT', 587),
'from' => [
    'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
    'name' => env('MAIL_FROM_NAME', 'Example'),
],
'encryption' => env('MAIL_ENCRYPTION', 'tls'),
'username' => env('MAIL_USERNAME'),
'password' => env('MAIL_PASSWORD'),
```

### 4.2 多邮件服务配置
```php
// config/services.php
'by_mail' => [
    'driver' => 'smtp',
    'host' => env('BY_MAIL_HOST'),
    'port' => '465',
    'from' => [
        'address' => env('BY_MAIL_FROM_ADDRESS'),
        'name' => env('BY_MAIL_FROM_NAME'),
    ],
    'encryption' => 'ssl',
    'username' => env('BY_MAIL_USERNAME'),
    'password' => env('BY_MAIL_PASSWORD'),
],

'other_mail' => [
    'driver' => 'smtp',
    'host' => env('OTHER_MAIL_HOST'),
    'port' => '465',
    'from' => [
        'address' => env('OTHER_MAIL_FROM_ADDRESS'),
        'name' => env('OTHER_MAIL_FROM_NAME'),
    ],
    'encryption' => 'ssl',
    'username' => env('OTHER_MAIL_USERNAME'),
    'password' => env('OTHER_MAIL_PASSWORD'),
]
```

### 4.3 邮件环境变量
```bash
# 主邮件服务
MAIL_DRIVER=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mail_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="AutoIns System"

# 保呀邮件服务
BY_MAIL_HOST=smtp.51baoya.com
BY_MAIL_FROM_ADDRESS=<EMAIL>
BY_MAIL_FROM_NAME="保呀系统"
BY_MAIL_USERNAME=<EMAIL>
BY_MAIL_PASSWORD=baoya_mail_password

# 其他邮件服务
OTHER_MAIL_HOST=smtp.baodehao.com
OTHER_MAIL_FROM_ADDRESS=<EMAIL>
OTHER_MAIL_FROM_NAME="保得好系统"
OTHER_MAIL_USERNAME=<EMAIL>
OTHER_MAIL_PASSWORD=other_mail_password
```

## 5. 队列与缓存配置

### 5.1 队列配置
```php
// config/queue.php
'default' => env('QUEUE_DRIVER', 'sync'),

'connections' => [
    'sync' => [
        'driver' => 'sync',
    ],
    'database' => [
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
    ],
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
    ],
],

'failed' => [
    'database' => env('DB_CONNECTION', 'mysql'),
    'table' => 'failed_jobs',
],
```

### 5.2 缓存配置
```php
// config/cache.php
'default' => env('CACHE_DRIVER', 'file'),

'stores' => [
    'file' => [
        'driver' => 'file',
        'path' => storage_path('framework/cache/data'),
    ],
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
    ],
    'database' => [
        'driver' => 'database',
        'table' => 'cache',
        'connection' => null,
    ],
],
```

### 5.3 Redis配置
```bash
# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DATABASE=0

# 队列配置
QUEUE_DRIVER=redis
CACHE_DRIVER=redis
SESSION_DRIVER=redis
```

## 6. 第三方服务配置

### 6.1 服务提供商配置
```php
// config/services.php
'mailgun' => [
    'domain' => env('MAILGUN_DOMAIN'),
    'secret' => env('MAILGUN_SECRET'),
],

'ses' => [
    'key' => env('SES_KEY'),
    'secret' => env('SES_SECRET'),
    'region' => 'us-east-1',
],

'sparkpost' => [
    'secret' => env('SPARKPOST_SECRET'),
],
```

## 7. 部署配置

### 7.1 生产环境配置
```bash
# 生产环境 .env
APP_ENV=production
APP_DEBUG=false
APP_LOG_LEVEL=error

# 性能优化
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_DRIVER=redis

# 安全配置
APP_KEY=base64:secure_32_character_key
JWT_SECRET=secure_jwt_secret_key
```

### 7.2 开发环境配置
```bash
# 开发环境 .env
APP_ENV=local
APP_DEBUG=true
APP_LOG_LEVEL=debug

# 开发工具
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_DRIVER=sync
```

### 7.3 测试环境配置
```bash
# 测试环境 .env
APP_ENV=testing
APP_DEBUG=true
APP_LOG_LEVEL=debug

# 测试数据库
DB_DATABASE=autoins_test
CACHE_DRIVER=array
SESSION_DRIVER=array
QUEUE_DRIVER=sync
```

## 8. 配置优化与安全

### 8.1 配置缓存
```bash
# 生产环境优化命令
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize
```

### 8.2 安全最佳实践
1. **环境变量安全**
   - 使用强密码和复杂密钥
   - 定期轮换敏感配置
   - 不在版本控制中存储敏感信息

2. **数据库安全**
   - 使用专用数据库用户
   - 限制数据库访问权限
   - 启用SSL连接

3. **应用安全**
   - 关闭生产环境调试模式
   - 设置适当的日志级别
   - 配置HTTPS和安全头

### 8.3 监控配置
```bash
# 日志配置
LOG_CHANNEL=daily
LOG_LEVEL=info

# 错误报告
APP_DEBUG=false
APP_LOG_LEVEL=error
```
