# Go语言重构架构设计文档

## 1. 系统架构概览

### 1.1 微服务架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Load Balancer  │    │   Config Center │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
    ┌────────────────────────────┼────────────────────────────┐
    │                            │                            │
┌───▼────┐  ┌──────────┐  ┌─────▼─────┐  ┌──────────┐  ┌────▼────┐
│Insurance│  │ Adapter  │  │Notification│  │Scheduler │  │Database │
│Service  │  │ Service  │  │  Service   │  │ Service  │  │ Service │
└─────────┘  └──────────┘  └───────────┘  └──────────┘  └─────────┘
```

### 1.2 技术栈选择

- **Go版本**: 1.21+
- **Web框架**: Gin/Echo
- **数据库**: PostgreSQL + Redis
- **消息队列**: RabbitMQ/NATS
- **配置管理**: Viper + Consul
- **日志**: Zap + ELK Stack
- **监控**: Prometheus + Grafana
- **链路追踪**: Jaeger
- **容器化**: Docker + Kubernetes

## 2. 核心服务设计

### 2.1 Insurance Service (保险业务服务)

```go
// 主要职责：保单处理、业务逻辑、状态管理
type InsuranceService struct {
    repo       InsuranceRepository
    adapter    AdapterManager
    notifier   NotificationService
    validator  PolicyValidator
    logger     *zap.Logger
}

// 核心接口定义
type InsuranceService interface {
    CreatePolicy(ctx context.Context, req *CreatePolicyRequest) (*PolicyResponse, error)
    GetPolicy(ctx context.Context, id string) (*Policy, error)
    UpdatePolicyStatus(ctx context.Context, id string, status PolicyStatus) error
    ProcessCallback(ctx context.Context, callback *CallbackData) error
}

// 保单数据结构
type Policy struct {
    ID              string    `json:"id" db:"id"`
    PlatformID      int64     `json:"platform_id" db:"platform_id"`
    ProductID       int64     `json:"product_id" db:"product_id"`
    OrderNo         string    `json:"order_no" db:"order_no"`
    Mode            string    `json:"mode" db:"mode"`
    HolderName      string    `json:"holder_name" db:"holder_name"`
    RecognizeeName  string    `json:"recognizee_name" db:"recognizee_name"`
    GoodsName       string    `json:"goods_name" db:"goods_name"`
    InsuredAmount   float64   `json:"insured_amount" db:"insured_amount"`
    Premium         float64   `json:"premium" db:"premium"`
    DepartureDate   int64     `json:"departure_date" db:"departure_date"`
    FromLoc         string    `json:"from_loc" db:"from_loc"`
    ToLoc           string    `json:"to_loc" db:"to_loc"`
    ViaLoc          *string   `json:"via_loc" db:"via_loc"`
    Transport       *string   `json:"transport" db:"transport"`
    InvNo           *string   `json:"inv_no" db:"inv_no"`
    Status          PolicyStatus `json:"status" db:"status"`
    CreatedAt       time.Time `json:"created_at" db:"created_at"`
    UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`
}

// 保单状态枚举
type PolicyStatus string

const (
    PolicyStatusPending    PolicyStatus = "pending"
    PolicyStatusProcessing PolicyStatus = "processing"
    PolicyStatusSuccess    PolicyStatus = "success"
    PolicyStatusFailed     PolicyStatus = "failed"
    PolicyStatusCancelled  PolicyStatus = "cancelled"
)
```

### 2.2 Adapter Service (适配器服务)

```go
// 适配器管理器
type AdapterManager struct {
    adapters map[string]InsuranceAdapter
    logger   *zap.Logger
}

// 保险公司适配器接口
type InsuranceAdapter interface {
    Process(ctx context.Context, policy *Policy) (*AdapterResponse, error)
    Validate(policy *Policy) error
    Transform(policy *Policy) (map[string]interface{}, error)
    GetCompanyInfo() CompanyInfo
}

// 适配器响应
type AdapterResponse struct {
    Success     bool                   `json:"success"`
    PolicyNo    string                 `json:"policy_no,omitempty"`
    Message     string                 `json:"message"`
    Data        map[string]interface{} `json:"data,omitempty"`
    Error       error                  `json:"error,omitempty"`
}

// 具体适配器实现示例 - PICC
type PICCAdapter struct {
    config     *PICCConfig
    httpClient *http.Client
    logger     *zap.Logger
}

func (p *PICCAdapter) Process(ctx context.Context, policy *Policy) (*AdapterResponse, error) {
    // 1. 数据验证
    if err := p.Validate(policy); err != nil {
        return nil, fmt.Errorf("validation failed: %w", err)
    }
    
    // 2. 数据转换
    data, err := p.Transform(policy)
    if err != nil {
        return nil, fmt.Errorf("transform failed: %w", err)
    }
    
    // 3. 调用保险公司API
    response, err := p.callPICCAPI(ctx, data)
    if err != nil {
        return nil, fmt.Errorf("api call failed: %w", err)
    }
    
    return response, nil
}

func (p *PICCAdapter) Transform(policy *Policy) (map[string]interface{}, error) {
    // 获取账号配置
    accountConfig, exists := p.config.Accounts[policy.AccountCode]
    if !exists {
        return nil, fmt.Errorf("account config not found: %s", policy.AccountCode)
    }
    
    // 构建请求数据
    data := map[string]interface{}{
        "agreementStartDate": accountConfig.AgreementStartDate,
        "holderName":        policy.HolderName,
        "recognizeeName":    policy.RecognizeeName,
        "goodsName":         policy.GoodsName,
        "insuredAmount":     policy.InsuredAmount,
        "premium":           policy.Premium,
        "fromLoc":           policy.FromLoc,
        "toLoc":             policy.ToLoc,
    }
    
    // 特殊处理逻辑
    if policy.PlatformID == 5 {
        data["invoiceNo"] = fmt.Sprintf("%s / %s", policy.InvNo, policy.FreightNo)
        data = p.applyPlatform5ClientData(data, policy)
    } else {
        data["invoiceNo"] = fmt.Sprintf("%s / %s", policy.OrderNo, policy.InvNo)
    }
    
    return data, nil
}
```

### 2.3 Notification Service (通知服务)

```go
// 通知服务
type NotificationService struct {
    pushSender  PushSender
    emailSender EmailSender
    smsSender   SMSSender
    logger      *zap.Logger
}

type NotificationService interface {
    SendPushNotification(ctx context.Context, req *PushRequest) error
    SendEmail(ctx context.Context, req *EmailRequest) error
    SendSMS(ctx context.Context, req *SMSRequest) error
    SendCallback(ctx context.Context, req *CallbackRequest) error
}

// 推送通知
type PushRequest struct {
    PlatformID int64                  `json:"platform_id"`
    Type       NotificationType       `json:"type"`
    Data       map[string]interface{} `json:"data"`
    Timestamp  int64                  `json:"timestamp"`
}

type NotificationType string

const (
    NotificationTypeSuccess NotificationType = "success"
    NotificationTypeFailed  NotificationType = "failed"
    NotificationTypeTimeout NotificationType = "timeout"
)

// 推送实现
func (n *NotificationService) SendPushNotification(ctx context.Context, req *PushRequest) error {
    // 获取平台推送配置
    config, err := n.getPlatformConfig(req.PlatformID)
    if err != nil {
        return fmt.Errorf("get platform config failed: %w", err)
    }
    
    // 构建推送数据
    pushData := map[string]interface{}{
        "type":      req.Type,
        "data":      req.Data,
        "timestamp": req.Timestamp,
    }
    
    // 发送推送
    return n.pushSender.Send(ctx, config.PushURL, pushData)
}
```

### 2.4 Scheduler Service (定时任务服务)

```go
// 定时任务服务
type SchedulerService struct {
    cron       *cron.Cron
    insurance  InsuranceService
    notification NotificationService
    logger     *zap.Logger
}

// 定时任务接口
type SchedulerService interface {
    Start() error
    Stop() error
    AddJob(spec string, job Job) error
}

// 任务接口
type Job interface {
    Run(ctx context.Context) error
    Name() string
}

// 异常检测任务
type ExceptionDetectionJob struct {
    insurance InsuranceService
    logger    *zap.Logger
}

func (j *ExceptionDetectionJob) Run(ctx context.Context) error {
    // 检测超时的保单
    timeoutPolicies, err := j.insurance.GetTimeoutPolicies(ctx)
    if err != nil {
        return fmt.Errorf("get timeout policies failed: %w", err)
    }
    
    // 处理超时保单
    for _, policy := range timeoutPolicies {
        if err := j.processTimeoutPolicy(ctx, policy); err != nil {
            j.logger.Error("process timeout policy failed", 
                zap.String("policy_id", policy.ID), 
                zap.Error(err))
        }
    }
    
    return nil
}

// 邮件发送任务
type EmailSendJob struct {
    notification NotificationService
    logger       *zap.Logger
}

func (j *EmailSendJob) Run(ctx context.Context) error {
    // 获取待发送邮件
    emails, err := j.getPendingEmails(ctx)
    if err != nil {
        return fmt.Errorf("get pending emails failed: %w", err)
    }
    
    // 发送邮件
    for _, email := range emails {
        if err := j.notification.SendEmail(ctx, email); err != nil {
            j.logger.Error("send email failed", 
                zap.String("email_id", email.ID), 
                zap.Error(err))
        }
    }
    
    return nil
}
```

## 3. 数据层设计

### 3.1 数据库设计

```sql
-- 保单表
CREATE TABLE policies (
    id VARCHAR(36) PRIMARY KEY,
    platform_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    order_no VARCHAR(100) NOT NULL,
    mode VARCHAR(50) NOT NULL,
    holder_name VARCHAR(200) NOT NULL,
    recognizee_name VARCHAR(200) NOT NULL,
    goods_name TEXT NOT NULL,
    insured_amount DECIMAL(15,2) NOT NULL,
    premium DECIMAL(10,2) NOT NULL,
    departure_date BIGINT NOT NULL,
    from_loc VARCHAR(200) NOT NULL,
    to_loc VARCHAR(200) NOT NULL,
    via_loc VARCHAR(200),
    transport VARCHAR(100),
    inv_no VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_platform_id (platform_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 保险公司配置表
CREATE TABLE insurance_companies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    modes JSON NOT NULL,
    type VARCHAR(20) NOT NULL,
    config JSON NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 平台配置表
CREATE TABLE platforms (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    push_url VARCHAR(500),
    push_token VARCHAR(200),
    email_config JSON,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 Repository模式

```go
// 保单仓库接口
type InsuranceRepository interface {
    Create(ctx context.Context, policy *Policy) error
    GetByID(ctx context.Context, id string) (*Policy, error)
    GetByOrderNo(ctx context.Context, orderNo string) (*Policy, error)
    Update(ctx context.Context, policy *Policy) error
    UpdateStatus(ctx context.Context, id string, status PolicyStatus) error
    GetTimeoutPolicies(ctx context.Context, timeout time.Duration) ([]*Policy, error)
    List(ctx context.Context, filter *PolicyFilter) ([]*Policy, error)
}

// 保单仓库实现
type insuranceRepository struct {
    db     *sql.DB
    logger *zap.Logger
}

func (r *insuranceRepository) Create(ctx context.Context, policy *Policy) error {
    query := `
        INSERT INTO policies (
            id, platform_id, product_id, order_no, mode, holder_name,
            recognizee_name, goods_name, insured_amount, premium,
            departure_date, from_loc, to_loc, via_loc, transport, inv_no, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    _, err := r.db.ExecContext(ctx, query,
        policy.ID, policy.PlatformID, policy.ProductID, policy.OrderNo,
        policy.Mode, policy.HolderName, policy.RecognizeeName,
        policy.GoodsName, policy.InsuredAmount, policy.Premium,
        policy.DepartureDate, policy.FromLoc, policy.ToLoc,
        policy.ViaLoc, policy.Transport, policy.InvNo, policy.Status,
    )
    
    if err != nil {
        return fmt.Errorf("create policy failed: %w", err)
    }
    
    return nil
}
```

## 4. 配置管理

### 4.1 配置结构

```go
// 应用配置
type Config struct {
    Server     ServerConfig     `mapstructure:"server"`
    Database   DatabaseConfig   `mapstructure:"database"`
    Redis      RedisConfig      `mapstructure:"redis"`
    Insurance  InsuranceConfig  `mapstructure:"insurance"`
    Notification NotificationConfig `mapstructure:"notification"`
    Scheduler  SchedulerConfig  `mapstructure:"scheduler"`
    Log        LogConfig        `mapstructure:"log"`
}

// 保险配置
type InsuranceConfig struct {
    Companies map[string]CompanyConfig `mapstructure:"companies"`
    Timeout   time.Duration           `mapstructure:"timeout"`
    Retry     RetryConfig             `mapstructure:"retry"`
}

// 保险公司配置
type CompanyConfig struct {
    Name     string                 `mapstructure:"name"`
    Type     string                 `mapstructure:"type"`
    Endpoint string                 `mapstructure:"endpoint"`
    Auth     AuthConfig             `mapstructure:"auth"`
    Accounts map[string]AccountConfig `mapstructure:"accounts"`
    Settings map[string]interface{} `mapstructure:"settings"`
}

// 账号配置
type AccountConfig struct {
    Code        string                 `mapstructure:"code"`
    Description string                 `mapstructure:"description"`
    Settings    map[string]interface{} `mapstructure:"settings"`
}
```

### 4.2 配置加载

```go
// 配置管理器
type ConfigManager struct {
    viper  *viper.Viper
    consul *api.Client
    logger *zap.Logger
}

func NewConfigManager() *ConfigManager {
    v := viper.New()
    v.SetConfigName("config")
    v.SetConfigType("yaml")
    v.AddConfigPath("./configs")
    v.AddConfigPath(".")
    
    return &ConfigManager{
        viper: v,
    }
}

func (c *ConfigManager) Load() (*Config, error) {
    // 读取本地配置文件
    if err := c.viper.ReadInConfig(); err != nil {
        return nil, fmt.Errorf("read config failed: %w", err)
    }
    
    // 从Consul读取动态配置
    if err := c.loadFromConsul(); err != nil {
        c.logger.Warn("load from consul failed", zap.Error(err))
    }
    
    var config Config
    if err := c.viper.Unmarshal(&config); err != nil {
        return nil, fmt.Errorf("unmarshal config failed: %w", err)
    }
    
    return &config, nil
}
```

## 5. 错误处理与监控

### 5.1 错误处理

```go
// 错误类型定义
type ErrorType string

const (
    ErrorTypeBusiness ErrorType = "business"
    ErrorTypeSystem   ErrorType = "system"
    ErrorTypeNetwork  ErrorType = "network"
)

// 自定义错误
type AppError struct {
    Type    ErrorType `json:"type"`
    Code    string    `json:"code"`
    Message string    `json:"message"`
    Details string    `json:"details,omitempty"`
    Cause   error     `json:"-"`
}

func (e *AppError) Error() string {
    return fmt.Sprintf("[%s] %s: %s", e.Type, e.Code, e.Message)
}

// 错误处理中间件
func ErrorHandlerMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Next()
        
        if len(c.Errors) > 0 {
            err := c.Errors.Last().Err
            
            var appErr *AppError
            if errors.As(err, &appErr) {
                c.JSON(http.StatusBadRequest, gin.H{
                    "error": appErr,
                })
            } else {
                c.JSON(http.StatusInternalServerError, gin.H{
                    "error": "internal server error",
                })
            }
        }
    }
}
```

### 5.2 监控指标

```go
// 监控指标
var (
    // 请求计数器
    requestCounter = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    // 请求耗时
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
        },
        []string{"method", "endpoint"},
    )
    
    // 保单处理计数器
    policyCounter = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "policies_processed_total",
            Help: "Total number of policies processed",
        },
        []string{"company", "status"},
    )
)

// 监控中间件
func MetricsMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        c.Next()
        
        duration := time.Since(start).Seconds()
        status := strconv.Itoa(c.Writer.Status())
        
        requestCounter.WithLabelValues(c.Request.Method, c.FullPath(), status).Inc()
        requestDuration.WithLabelValues(c.Request.Method, c.FullPath()).Observe(duration)
    }
}
```

## 6. 部署与运维

### 6.1 Docker配置

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o main ./cmd/server

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs

CMD ["./main"]
```

### 6.2 Kubernetes配置

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: autoins-go
spec:
  replicas: 3
  selector:
    matchLabels:
      app: autoins-go
  template:
    metadata:
      labels:
        app: autoins-go
    spec:
      containers:
      - name: autoins-go
        image: autoins-go:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: host
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

## 总结

本Go语言重构架构设计提供了：

1. **清晰的微服务架构**：职责分离，易于扩展和维护
2. **完整的适配器模式**：支持多保险公司接入，易于添加新的保险公司
3. **强类型系统**：利用Go的类型系统确保数据安全
4. **完善的错误处理**：统一的错误处理机制
5. **全面的监控体系**：指标收集、链路追踪、日志聚合
6. **云原生部署**：支持Docker和Kubernetes部署

这个架构设计确保了系统的高可用性、可扩展性和可维护性，为业务的快速发展提供了坚实的技术基础。
