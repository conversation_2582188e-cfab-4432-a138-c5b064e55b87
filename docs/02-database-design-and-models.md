# 数据库设计与模型关系

## 1. 核心数据库表结构

### 1.1 主要业务表

#### 1.1.1 platforms (投保平台表)
```sql
CREATE TABLE platforms (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,                -- 平台名称
    app_id VARCHAR(255) UNIQUE NOT NULL,        -- 应用ID (格式: INS+时间戳)
    secret_key VARCHAR(255) NOT NULL,           -- 密钥 (SHA1加密)
    status TINYINT DEFAULT 1,                   -- 状态 (1:禁用, 2:启用)
    deleted_at TIMESTAMP NULL,                  -- 软删除时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 1.1.2 products (产品表)
```sql
CREATE TABLE products (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_code VARCHAR(255) UNIQUE,           -- 产品代码 (格式: YYYYMMDD+5位随机数)
    title VARCHAR(255) NOT NULL,                -- 产品名称
    account VARCHAR(100) NOT NULL,              -- 保险公司账户
    mode VARCHAR(20) NOT NULL,                  -- 投保方式 (决定使用哪个适配器)
    config TEXT,                                -- 产品配置 (JSON格式)
    status TINYINT DEFAULT 1,                   -- 状态 (1:禁用, 2:启用)
    deleted_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 1.1.3 messages (报文主表)
```sql
CREATE TABLE messages (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    platform_id INT UNSIGNED NOT NULL,         -- 关联平台ID
    product_id INT UNSIGNED NOT NULL,           -- 关联产品ID
    mode VARCHAR(20) NOT NULL,                  -- 投保方式
    order_no VARCHAR(50) NOT NULL,              -- 订单号
    apply_no VARCHAR(50),                       -- 投保单号
    policy_no VARCHAR(50),                      -- 保单号
    error_num TINYINT DEFAULT 0,                -- 录单异常处理次数
    done_at TIMESTAMP NULL,                     -- 处理时间
    is_locked TINYINT DEFAULT 0,                -- 是否锁定
    is_entry BOOLEAN DEFAULT FALSE,             -- 是否在录单中
    status TINYINT DEFAULT 1,                   -- 状态 (0:未处理, 1:已提交, 2:已完成, -1:已作废)
    file_path VARCHAR(255),                     -- 保单文件路径
    is_download TINYINT DEFAULT 0,              -- 是否已下载 (0:未下载, 1:已下载)
    deleted_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 1.1.4 message_attaches (报文附表)
```sql
CREATE TABLE message_attaches (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    message_id INT UNSIGNED NOT NULL,           -- 关联报文ID
    source LONGTEXT,                            -- 原始数据 (JSON格式)
    content LONGTEXT,                           -- 转换后数据 (JSON格式)
    callback TEXT,                              -- 回调数据 (JSON格式)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 1.1.5 notifies (推送通知表)
```sql
CREATE TABLE notifies (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    message_id INT UNSIGNED NOT NULL,           -- 关联报文ID
    content VARCHAR(1024),                      -- 推送内容
    url VARCHAR(255) NOT NULL,                  -- 推送URL
    done_at TIMESTAMP NULL,                     -- 推送时间
    callback VARCHAR(1024),                     -- 回调结果
    status TINYINT DEFAULT 1,                   -- 状态 (0:未处理, 1:待发送, 2:已发送, 3:发送失败, 4:中意雇主待发送)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 1.1.6 platform_products (平台产品关联表)
```sql
CREATE TABLE platform_products (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    platform_id INT UNSIGNED NOT NULL,         -- 平台ID
    product_id INT UNSIGNED NOT NULL,           -- 产品ID
    status TINYINT DEFAULT 1,                   -- 状态 (1:禁用, 2:启用)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 1.1.7 users (系统用户表)
```sql
CREATE TABLE users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    fullname VARCHAR(255) NOT NULL,             -- 全名
    mobile VARCHAR(255) NOT NULL,               -- 手机号
    username VARCHAR(255) UNIQUE NOT NULL,      -- 用户名
    password VARCHAR(255) NOT NULL,             -- 密码
    remember_token VARCHAR(100),                -- 记住登录令牌
    deleted_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 1.1.8 mail_delivery (邮件发送记录表)
```sql
CREATE TABLE mail_delivery (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    platform_id INT UNSIGNED NOT NULL,         -- 平台ID
    order_no VARCHAR(50) NOT NULL,              -- 订单号
    status TINYINT DEFAULT 1,                   -- 状态 (1:正在发送, 2:已发送, 3:发送失败)
    send_time TIMESTAMP NULL                    -- 发送时间
);
```

### 1.2 数据库连接配置

系统支持多数据库连接，用于集成不同的子系统：

```php
// config/database.php
'connections' => [
    'mysql' => [        // 主数据库
        'prefix' => 'ato_',
        'database' => env('DB_DATABASE', 'autoins'),
    ],
    'mysql_yf' => [     // 优孚系统
        'database' => env('DB_DATABASE_YF', 'forge'),
    ],
    'mysql_by' => [     // 保呀系统(旧)
        'database' => env('DB_DATABASE_BY', 'forge'),
    ],
    'mysql_by_new' => [ // 保呀系统(新)
        'database' => env('DB_DATABASE_BY_NEW', 'forge'),
    ],
    'mysql_ald' => [    // 阿拉丁系统
        'database' => env('DB_DATABASE_ALD', 'forge'),
    ],
    'mysql_lby' => [    // 其他系统
        'database' => env('DB_DATABASE_LBY', 'forge'),
    ],
    'mysql_zl' => [     // 中联系统
        'database' => env('DB_DATABASE_ZL', 'forge'),
    ],
]
```

## 2. 模型关系设计

### 2.1 核心模型关系

#### 2.1.1 Platform 模型
```php
class Platform extends Authenticatable implements JWTSubject
{
    // 一个平台可以有多个产品
    public function products()
    {
        return $this->belongsToMany(Product::class, 'platform_products');
    }
    
    // 一个平台可以有多个报文
    public function messages()
    {
        return $this->hasMany(Message::class);
    }
}
```

#### 2.1.2 Product 模型
```php
class Product extends Model
{
    // 一个产品可以属于多个平台
    public function platforms()
    {
        return $this->belongsToMany(Platform::class, 'platform_products');
    }
    
    // 一个产品可以有多个报文
    public function messages()
    {
        return $this->hasMany(Message::class);
    }
}
```

#### 2.1.3 Message 模型
```php
class Message extends Model
{
    // 报文属于一个平台
    public function platform()
    {
        return $this->belongsTo(Platform::class);
    }
    
    // 报文属于一个产品
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    // 报文有一个附表
    public function attaches()
    {
        return $this->hasOne(MessageAttach::class);
    }
    
    // 报文可以有多个推送通知
    public function notifies()
    {
        return $this->hasMany(Notify::class);
    }
}
```

#### 2.1.4 Notify 模型
```php
class Notify extends Model
{
    // 通知属于一个报文
    public function message()
    {
        return $this->belongsTo(Message::class);
    }
}
```

### 2.2 外部系统数据模型

#### 2.2.1 子系统配置
```php
// app/Helpers/functions.php
function subsystem(){
    return [
        'DataYoufu' => [
            'SerialNo',                    // 订单号字段
            'DateTime',                    // 时间字段
            true,                          // 时间是否为时间戳
            'INS1569686500',              // 平台app_id
            '<EMAIL>'         // 邮件发送者
        ],
        'DataBaoya' => [
            'sys_order_no',
            'created_at',
            false,
            'INS1569723235',
            '<EMAIL>'
        ],
        'DataBaoyaNew' => [
            'order_no',
            'submitted_at',
            false,
            'INS1635757943',
            '<EMAIL>'
        ],
    ];
}
```

#### 2.2.2 DataYoufu 模型 (优孚系统)
```php
class DataYoufu extends Model
{
    protected $connection = 'mysql_yf';
    protected $table = "cs_ins_form as a";
    
    // 查询单个保单
    public function index($field_name, $order_no);
    
    // 查询时间范围内的保单
    public function indexs($field_name, $time1, $time2);
}
```

#### 2.2.3 DataBaoya 模型 (保呀系统-旧)
```php
class DataBaoya extends Model
{
    protected $connection = 'mysql_by';
    protected $table = "by_policies as a";
    
    // 关联查询: by_policies + by_product_rates + by_policy_cargos
}
```

#### 2.2.4 DataBaoyaNew 模型 (保呀系统-新)
```php
class DataBaoyaNew extends Model
{
    protected $connection = 'mysql_by_new';
    protected $table = "policies as policy";
    
    // 复杂关联查询，包含货币转换逻辑
}
```

## 3. 数据流转设计

### 3.1 报文处理流程
```
1. 投保平台提交数据 → messages表 (status=0)
2. 数据转换处理 → message_attaches表 (source + content)
3. 提交保险公司 → messages表 (status=1)
4. 获取保单号 → messages表 (status=2, policy_no)
5. 推送通知 → notifies表
```

### 3.2 状态管理
- **Message状态**: 0(未处理) → 1(已提交) → 2(已完成) / -1(已作废)
- **Notify状态**: 0(未处理) → 1(待发送) → 2(已发送) / 3(发送失败)
- **邮件状态**: 1(正在发送) → 2(已发送) / 3(发送失败)

### 3.3 数据同步机制
- 定时任务检查子系统数据
- 自动同步未提交保单到主系统
- 邮件报备机制确保数据完整性

## 4. 索引与性能优化

### 4.1 建议索引
```sql
-- 报文表索引
CREATE INDEX idx_messages_platform_order ON messages(platform_id, order_no);
CREATE INDEX idx_messages_status_created ON messages(status, created_at);
CREATE INDEX idx_messages_mode_done ON messages(mode, done_at);

-- 通知表索引
CREATE INDEX idx_notifies_message_status ON notifies(message_id, status);
CREATE INDEX idx_notifies_status_created ON notifies(status, created_at);

-- 平台产品关联表索引
CREATE INDEX idx_platform_products_platform ON platform_products(platform_id);
CREATE INDEX idx_platform_products_product ON platform_products(product_id);
```

### 4.2 查询优化
- 使用软删除避免数据丢失
- 分页查询减少内存占用
- 适当使用缓存机制
- 定期清理历史数据

## 5. 数据安全与备份

### 5.1 数据安全
- 敏感字段加密存储
- API访问权限控制
- 操作日志记录
- 软删除机制

### 5.2 数据完整性
- 外键约束确保关联完整性
- 事务处理保证数据一致性
- 定期数据校验
- 异常数据监控
