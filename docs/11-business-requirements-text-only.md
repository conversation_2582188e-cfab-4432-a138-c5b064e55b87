# 自动录单系统业务需求文档

## 1. 系统概述

自动录单系统是一个多保险公司自动化投保平台，支持11家保险公司的API对接和自动化投保流程。系统需要处理不同保险公司、不同账号的特殊业务规则和数据格式要求。

## 2. 人保财险 (PICC) 业务需求

### 2.1 账号管理需求

系统需要支持22个不同的人保账号，每个账号有独立的协议开始日期和业务规则：

**主要账号列表：**
- yrwl02：赢睿物流02账号，协议开始日期2019-05-27
- yrwl01：赢睿物流01账号，协议开始日期2020-05-22
- cdwh1/cdwh2/cdwh3：成都万恒系列账号，协议开始日期2019-10-18
- wanheng：万恒账号，协议开始日期2020-05-04
- muzixing系列：木子行0001、02、03、4、05、06账号，协议开始日期2019-09-20至2019-09-24
- quanqiutong系列：全球通01、2号账号，协议开始日期2019-09-24
- 其他专业账号：sxdh、BAOCANGWANG02/04、YNYJB2、4403A00210、210314yjb02、DEJUN

### 2.2 发票号格式特殊处理需求

**平台ID为5的特殊处理：**
- 发票号格式：发票号 + " / " + 运单号
- 其他平台格式：订单号 + " / " + 发票号

### 2.3 客户数据特殊处理需求

**平台ID为5时的固定客户信息：**
- 投保人地址：北京市东城区（固定编码110000/110100/110101）
- 投保人客户类型：团体（固定值0）
- 投保人证件类型：统一社会信用代码
- 投保人证件号：91310230MA1K2FMP33（固定）
- 证件有效期：2019-01-04至2039-01-03
- 被保人信息：类似投保人，但证件号为91310115MA1H90HR24

### 2.4 条款和附加条款映射需求

**主要条款类型：**
- 1：国内公路货运保险
- 3：国内航空货运保险
- 5：国内水路、陆路货运综合险
- 6：国内水路、陆路货运基本险
- 1000：国内鲜活货物运输保险条款（2009版）
- 1003：无车承运人责任保险条款
- 1004：生鲜冷链物流保险条款

**附加条款：**
- 1005/1004：国内水路、陆路货物运输保险附加盗窃、抢劫保险条款
- 165：公路货物运输保险附加盗窃、抢劫保险条款（2009版）

## 3. 太保财险 (CPIC) 业务需求

### 3.1 账号配置需求

**7个主要账号的差异化配置：**

**SHTB账号：**
- 协议号：A|3010200|C20180144-19001P000431|CAGHYX190032
- 单位代码：3010200
- 免赔条款：本保单其他承保条件同协议
- 费率信息：最低费率0.5‰，最高保额300万，最低保费50元

**YRWL账号：**
- 协议号：A|3010100|C20190295|0
- 单位代码：3010100
- 免赔条款：本保单其他承保条件同协议；每次事故绝对免赔额为人民币1000或损失金额的10%，两者取高
- 费率信息：最低费率0.15‰，最高保额50万，最低保费3元

**CDYF账号（成都优孚）特殊需求：**
- 协议号：C|3010100|CSHHHYX2024P000473|0|CSHHHYX2024Q000452
- 投保人名称强制设置为：成都优孚世纪信息技术有限公司
- 复杂的免赔条款：包含综合免赔、火灾翻车免赔、酒类免赔、易碎品免赔等多种情况
- 根据条款类型和货物类型设置不同的货物覆盖范围配置

### 3.2 CDYF账号特殊业务规则

**条款类型分支处理：**
- 基本险(JB) + 轻工品类(0309)：费率0.12‰，特定货物覆盖范围
- 基本险(JB) + 易碎品类(0301)：费率0.15‰，易碎品专用覆盖范围
- 综合险(ZH)：费率0.15‰，全面货物覆盖范围

**发票处理需求：**
- 发票类型：根据投保类型设置（1对应0，其他对应1）
- 需要处理发票抬头、营业执照号、开票人纳税识别号等信息

**特约条款需求：**
- 包含食品饮料外包装损失处理规则
- 串味损失除外责任
- 锈损和腐烂变质除外责任
- 修复优先赔偿方式
- 违规运输拒赔条款
- 机械设备包装要求
- 易碎品承保限制
- 重型低平板半挂车额外免赔

### 3.3 包装和运输方式映射需求

**包装方式代码：**
- 裸装→05，散装→04，纸箱→01，木箱→01，捆包→08
- 袋装→02，篓装→01，托盘→03，桶装→06，罐装→07

**运输方式代码：**
- 水路(3)→1，航空(4)→5，公路(5)→4，铁路(6)→3

## 4. 平安财险 (PINGAN) 业务需求

### 4.1 账号类型识别需求

**企业账号列表：**
需要识别以下账号为企业账号，使用企业证件类型：
- CGHZYRWLKJ00002、CGNJYB00013、CGKMYJB00002、CGFZZF200001
- CGGZYNYZK200001、CGSHZHMBX00002、CGSHZQLJJ00001、CGGZQLBXJJ00001
- CGZYJJ00002、CGGZLJGYL00001

### 4.2 证件类型处理需求

**证件类型规则：**
- 企业账号：投保人证件类型99，被保人证件类型01
- 个人账号：投保人证件类型03，被保人证件类型03

### 4.3 特殊账号业务规则

**CGZYJJ00002（上海平安）：**
- 当主体为normal且货物类型为10时：免赔条款为"每次事故绝对免赔为保额的3%"
- 默认身份证号：91310230MA1K2FMP33

**CGGZQLBXJJ00001（广东平安）：**
- 当主体不为manual时：执行特殊处理逻辑
- 默认身份证号：91310230MA1K2FMP33

**CGSHZQLJJ00001（深圳平安-运吉宝）：**
- 特殊免赔和条款处理

**CGSHZHMBX00002（深圳平安-保呀）：**
- 特殊免赔和条款处理

### 4.4 裸装货物特殊处理需求

当包装方式为"裸装"时，需要添加特殊免赔条款：
"裸装：本保单不承保由于刮擦，锈损，凹瘪引起的损失。"

## 5. 华泰财险 (HUATAI) 业务需求

### 5.1 数据格式需求

华泰财险使用XML数据格式进行数据交换，需要构建完整的XML结构。

### 5.2 货物类型映射需求

**主要货物类型映射：**
- 纺织原料及纺织制品：SX001411/SX00140065
- 机器设备及其零件、附件：SX001416/SX00140087
- 食品：SX001404/SX00140019
- 化学工业及其相关工业产品：SX001406/SX00140040
- 玻璃及玻璃制品：SX001413/SX00140072
- 新车/二手车：SX001417/SX00140089
- 各类鲜活货物：SX001402系列
- 各类危险品：SX001406/SX00140040
- 矿产资源类：SX001405系列

### 5.3 运输方式映射需求

**按运输方式和装载类型组合映射：**
- 水路+厢式货车：SX001501/01
- 水路+非厢式货车：SX001501/05
- 航空+厢式货车：SX001503/01
- 航空+非厢式货车：SX001503/02
- 公路+厢式货车：SX001502/01
- 公路+非厢式货车：SX001502/02
- 铁路+厢式货车：SX001505/01
- 铁路+非厢式货车：SX001505/02

### 5.4 条款映射需求

- 基本险(JBX)：SX300211
- 综合险(ZHX)：SX300212

### 5.5 固定配置需求

- 默认电话号码：18926805333
- 查勘地址ID：501422495713
- 查勘地址：17/F,Block B,Center Plaza 161 Linhexi Av.,Tianhe District, Guangzhou TEL:4006095509 FAX: 020-87567201
- 起运国家/目的国家：HTC01
- 证件类型：99

## 6. 中华联合 (SINOSIG) 业务需求

### 6.1 产品配置需求

**保呀产品（产品代码2020022194297）：**
- 系统标识：BAOYA
- 公司代码：07710200
- 协议号：10771YAB02023000006
- 操作代码：BAOYA
- 核保状态：00
- 提单号：使用运单号
- 发票号：使用发票号
- 理赔地点：需要地址转换并截取18个字符

**阿拉丁产品（默认产品）：**
- 系统标识：aladdin
- 公司代码：07514300
- 协议号：10771YAB02020000035
- 操作代码：aladdin
- 核保状态：01
- 提单号：发票号-订单号格式
- 发票号：空
- 理赔地点：中国

### 6.2 包装代码映射需求

**包装方式映射：**
- 裸装/散装/捆包/篓装/罐装：024标准包装
- 纸箱：002纸箱
- 木箱：001木箱
- 袋装：023袋子
- 托盘：020托盘
- 桶装：019桶

### 6.3 特殊处理需求

- 百分号替换：将%替换为&#37;
- XML编码：UTF-8
- 发送序列号：使用订单号的MD5值
- 分类代码：09
- 是否转运代码：false

## 7. 史带财险 (STARR) 业务需求

### 7.1 固定配置需求

- 合作伙伴代码：YOUFU
- 合作伙伴密钥：km2a0f13c9dkb8
- 产品代码：60021
- 操作类型：006
- 付费处理方式：2
- 期间类型：M
- 期间：1
- 货物运输方式：3
- 邮箱：<EMAIL>

### 7.2 身份证处理需求

**身份证号码处理：**
- 15位身份证：生日提取为"19"+第6-11位
- 18位身份证：生日提取为第6-13位
- 性别判断：最后一位数字，奇数为男(M)，偶数为女(F)

**客户类型判断：**
- 包含"公司"字样：企业客户(QY)，证件类型104
- 不包含"公司"：个人客户(GR)，证件类型1

### 7.3 时间处理需求

- 起保时间：在出发时间基础上增加24小时
- 密钥生成：MD5(合作伙伴代码+合作伙伴密钥+交易代码)转大写
- 交易代码：生成GUID
- 投被保人关系：5

## 8. 第三方平台 (BDYF) 业务需求

### 8.1 产品配置需求

**8个产品的差异化配置：**
- 郑州平安普货基本险/综合险：费率1.2‰
- 平安商品车基本险：费率1.5‰
- 上海太平洋普货基本险：费率1.2‰
- 南京平安普货基本险：费率1‰
- 云南太平洋冷藏一切险：费率2.5‰
- 杭州人保普货基本险：费率1‰
- 杭州人保普货综合险：费率1.3‰

### 8.2 数据转换需求

**金额单位转换：**
- 保额从元转换为万元（除以10000）

**固定值设置：**
- 运输类型：汽运
- 装载类型：非集装箱
- 合同号格式：订单号/发票号

**数据处理规则：**
- 空值处理：所有空值转换为空字符串
- 数字值处理：所有数字值转换为字符串格式

## 9. 通用业务规则

### 9.1 时间处理规则

**出发时间调整：**
- 如果是当天出发：增加1小时
- 如果分钟数大于50：增加1小时
- 如果调整后跨天：重置为次日00:00

### 9.2 费率转换规则

平台费率单位是万分之几，保险公司费率单位是千分之几，需要进行转换：
平台费率 ÷ 10 = 保险公司费率

### 9.3 金额格式化规则

所有金额字段需要保留2位小数，使用sprintf('%.2f', 金额)格式化。

### 9.4 异常检测规则

**超时检测：**
- 平安跨境电商模式：2小时超时
- 其他模式：10分钟超时

**失败检测：**
- 检测保单状态为失败的记录
- 发送异常通知
- 记录异常日志

## 10. 推送通知需求

### 10.1 推送类型

- 成功推送：保单投保成功
- 失败推送：保单投保失败
- 超时推送：保单处理超时

### 10.2 推送数据格式

推送数据需要包含：
- 推送类型
- 保单数据
- 时间戳
- 平台标识

### 10.3 邮件通知需求

**定时邮件发送：**
- 异常保单汇总邮件
- 系统状态报告邮件
- 业务数据统计邮件

## 11. 数据同步需求

### 11.1 子系统数据同步

需要与7个子系统进行数据同步：
- 各子系统有独立的数据库连接
- 需要定时同步保单状态
- 需要处理同步异常情况

### 11.2 状态管理需求

**保单状态流转：**
- 待处理 → 处理中 → 成功/失败
- 需要记录状态变更时间
- 需要记录状态变更原因

这份文档详细描述了系统的所有业务需求，为Go语言重构提供了完整的业务规则参考。
