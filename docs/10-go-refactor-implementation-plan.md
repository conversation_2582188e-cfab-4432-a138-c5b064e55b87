# Go语言重构实施计划

## 项目概述

将现有的Laravel 5.5 PHP自动录单系统重构为Go语言微服务架构，保持所有业务逻辑和特殊处理规则不变，提升系统性能、可维护性和扩展性。

## 重构目标

### 技术目标
- **性能提升**: 响应时间减少50%以上
- **并发能力**: 支持10倍以上并发处理能力
- **资源消耗**: 内存使用减少60%
- **部署效率**: 容器化部署，启动时间从分钟级降至秒级

### 业务目标
- **零业务中断**: 平滑迁移，不影响现有业务
- **功能完整性**: 100%保持现有功能和特殊处理逻辑
- **扩展性**: 支持新保险公司快速接入
- **可维护性**: 代码结构清晰，便于团队协作

## 实施阶段

### 第一阶段：基础设施搭建 (2周)

#### 1.1 项目初始化
```bash
# 项目结构创建
mkdir autoins-go
cd autoins-go

# Go模块初始化
go mod init github.com/company/autoins-go

# 目录结构
mkdir -p {cmd,internal,pkg,configs,deployments,docs,scripts}
mkdir -p internal/{adapter,service,repository,handler,middleware}
mkdir -p pkg/{logger,config,database,cache,queue}
```

#### 1.2 核心依赖选择
```go
// go.mod 核心依赖
require (
    github.com/gin-gonic/gin v1.9.1
    github.com/spf13/viper v1.16.0
    go.uber.org/zap v1.24.0
    github.com/jmoiron/sqlx v1.3.5
    github.com/go-redis/redis/v8 v8.11.5
    github.com/streadway/amqp v1.1.0
    github.com/prometheus/client_golang v1.16.0
    github.com/opentracing/opentracing-go v1.2.0
    github.com/robfig/cron/v3 v3.0.1
    gorm.io/gorm v1.25.2
    gorm.io/driver/mysql v1.5.1
)
```

#### 1.3 开发环境搭建
- Docker Compose环境配置
- 数据库迁移脚本
- 配置管理系统
- 日志系统初始化

### 第二阶段：核心服务开发 (4周)

#### 2.1 数据层开发 (1周)
```go
// 数据模型定义
type Policy struct {
    ID              string    `json:"id" gorm:"primaryKey"`
    PlatformID      int64     `json:"platform_id" gorm:"index"`
    ProductID       int64     `json:"product_id"`
    OrderNo         string    `json:"order_no" gorm:"index"`
    Mode            string    `json:"mode"`
    HolderName      string    `json:"holder_name"`
    RecognizeeName  string    `json:"recognizee_name"`
    GoodsName       string    `json:"goods_name"`
    InsuredAmount   float64   `json:"insured_amount"`
    Premium         float64   `json:"premium"`
    DepartureDate   int64     `json:"departure_date"`
    FromLoc         string    `json:"from_loc"`
    ToLoc           string    `json:"to_loc"`
    ViaLoc          *string   `json:"via_loc"`
    Transport       *string   `json:"transport"`
    InvNo           *string   `json:"inv_no"`
    Status          string    `json:"status" gorm:"index"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
}

// Repository接口实现
type policyRepository struct {
    db *gorm.DB
}

func (r *policyRepository) Create(ctx context.Context, policy *Policy) error {
    return r.db.WithContext(ctx).Create(policy).Error
}
```

**任务清单:**
- [ ] 数据库表结构设计和迁移
- [ ] GORM模型定义
- [ ] Repository接口设计
- [ ] 数据访问层实现
- [ ] 单元测试编写

#### 2.2 业务服务层开发 (1.5周)
```go
// 保险业务服务
type InsuranceService struct {
    repo       repository.PolicyRepository
    adapter    adapter.Manager
    notifier   notification.Service
    validator  validator.PolicyValidator
    logger     *zap.Logger
}

func (s *InsuranceService) CreatePolicy(ctx context.Context, req *CreatePolicyRequest) (*PolicyResponse, error) {
    // 1. 数据验证
    if err := s.validator.Validate(req); err != nil {
        return nil, fmt.Errorf("validation failed: %w", err)
    }
    
    // 2. 创建保单
    policy := s.buildPolicy(req)
    if err := s.repo.Create(ctx, policy); err != nil {
        return nil, fmt.Errorf("create policy failed: %w", err)
    }
    
    // 3. 异步处理
    go s.processPolicy(context.Background(), policy)
    
    return &PolicyResponse{
        ID:     policy.ID,
        Status: policy.Status,
    }, nil
}
```

**任务清单:**
- [ ] 业务逻辑服务实现
- [ ] 数据验证器开发
- [ ] 异步处理机制
- [ ] 错误处理机制
- [ ] 业务规则实现

#### 2.3 适配器系统开发 (1.5周)
```go
// 适配器管理器
type Manager struct {
    adapters map[string]Adapter
    config   *config.InsuranceConfig
    logger   *zap.Logger
}

// PICC适配器实现
type PICCAdapter struct {
    config     *PICCConfig
    httpClient *http.Client
    logger     *zap.Logger
}

func (p *PICCAdapter) Process(ctx context.Context, policy *Policy) (*Response, error) {
    // 数据转换
    data, err := p.Transform(policy)
    if err != nil {
        return nil, err
    }
    
    // API调用
    response, err := p.callAPI(ctx, data)
    if err != nil {
        return nil, err
    }
    
    return response, nil
}
```

**任务清单:**
- [ ] 适配器接口设计
- [ ] 11个保险公司适配器实现
- [ ] 特殊处理逻辑迁移
- [ ] HTTP客户端封装
- [ ] 重试机制实现

### 第三阶段：API和中间件开发 (2周)

#### 3.1 HTTP API开发 (1周)
```go
// API处理器
type PolicyHandler struct {
    service service.InsuranceService
    logger  *zap.Logger
}

func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
    var req CreatePolicyRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    response, err := h.service.CreatePolicy(c.Request.Context(), &req)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, response)
}

// 路由配置
func SetupRoutes(r *gin.Engine, handler *PolicyHandler) {
    api := r.Group("/api/v1")
    {
        api.POST("/policies", handler.CreatePolicy)
        api.GET("/policies/:id", handler.GetPolicy)
        api.PUT("/policies/:id/status", handler.UpdateStatus)
        api.POST("/callbacks/:company", handler.HandleCallback)
    }
}
```

#### 3.2 中间件开发 (1周)
```go
// JWT认证中间件
func JWTAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        if token == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "missing token"})
            c.Abort()
            return
        }
        
        // 验证JWT token
        claims, err := validateJWT(token)
        if err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
            c.Abort()
            return
        }
        
        c.Set("user", claims)
        c.Next()
    }
}

// 监控中间件
func MetricsMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        c.Next()
        duration := time.Since(start)
        
        // 记录指标
        requestCounter.WithLabelValues(
            c.Request.Method,
            c.FullPath(),
            strconv.Itoa(c.Writer.Status()),
        ).Inc()
        
        requestDuration.WithLabelValues(
            c.Request.Method,
            c.FullPath(),
        ).Observe(duration.Seconds())
    }
}
```

### 第四阶段：通知和定时任务系统 (2周)

#### 4.1 通知服务开发 (1周)
```go
// 通知服务
type NotificationService struct {
    pushSender  PushSender
    emailSender EmailSender
    queue       queue.Queue
    logger      *zap.Logger
}

func (n *NotificationService) SendPushNotification(ctx context.Context, req *PushRequest) error {
    // 构建推送数据
    data := map[string]interface{}{
        "type":      req.Type,
        "data":      req.Data,
        "timestamp": time.Now().Unix(),
    }
    
    // 获取平台配置
    config, err := n.getPlatformConfig(req.PlatformID)
    if err != nil {
        return err
    }
    
    // 发送推送
    return n.pushSender.Send(ctx, config.PushURL, data)
}
```

#### 4.2 定时任务系统 (1周)
```go
// 定时任务调度器
type Scheduler struct {
    cron    *cron.Cron
    jobs    map[string]Job
    logger  *zap.Logger
}

// 异常检测任务
type ExceptionDetectionJob struct {
    service service.InsuranceService
    logger  *zap.Logger
}

func (j *ExceptionDetectionJob) Run() {
    ctx := context.Background()
    
    // 检测超时保单
    policies, err := j.service.GetTimeoutPolicies(ctx, 10*time.Minute)
    if err != nil {
        j.logger.Error("get timeout policies failed", zap.Error(err))
        return
    }
    
    // 处理超时保单
    for _, policy := range policies {
        j.processTimeoutPolicy(ctx, policy)
    }
}
```

### 第五阶段：测试和部署 (3周)

#### 5.1 测试阶段 (2周)
```go
// 单元测试示例
func TestPolicyService_CreatePolicy(t *testing.T) {
    // 准备测试数据
    mockRepo := &MockPolicyRepository{}
    mockAdapter := &MockAdapterManager{}
    service := NewInsuranceService(mockRepo, mockAdapter, nil, nil, nil)
    
    req := &CreatePolicyRequest{
        PlatformID:     1,
        ProductID:      1,
        OrderNo:        "TEST001",
        HolderName:     "测试投保人",
        RecognizeeName: "测试被保人",
        // ... 其他字段
    }
    
    // 执行测试
    response, err := service.CreatePolicy(context.Background(), req)
    
    // 验证结果
    assert.NoError(t, err)
    assert.NotEmpty(t, response.ID)
    assert.Equal(t, "pending", response.Status)
}

// 集成测试
func TestPICCAdapter_Integration(t *testing.T) {
    // 使用真实配置进行集成测试
    adapter := NewPICCAdapter(testConfig)
    
    policy := &Policy{
        // 测试数据
    }
    
    response, err := adapter.Process(context.Background(), policy)
    assert.NoError(t, err)
    assert.True(t, response.Success)
}
```

**测试计划:**
- [ ] 单元测试 (覆盖率 > 80%)
- [ ] 集成测试
- [ ] API测试
- [ ] 性能测试
- [ ] 压力测试

#### 5.2 部署阶段 (1周)
```yaml
# docker-compose.yml
version: '3.8'
services:
  autoins-go:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: autoins
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 第六阶段：数据迁移和上线 (2周)

#### 6.1 数据迁移 (1周)
```go
// 数据迁移脚本
func MigrateData() error {
    // 1. 连接原有数据库
    oldDB, err := connectToOldDB()
    if err != nil {
        return err
    }
    
    // 2. 连接新数据库
    newDB, err := connectToNewDB()
    if err != nil {
        return err
    }
    
    // 3. 迁移保单数据
    if err := migratePolicies(oldDB, newDB); err != nil {
        return err
    }
    
    // 4. 迁移配置数据
    if err := migrateConfigs(oldDB, newDB); err != nil {
        return err
    }
    
    return nil
}
```

#### 6.2 灰度发布 (1周)
- 小流量测试 (5%)
- 逐步扩大流量 (20% -> 50% -> 100%)
- 监控关键指标
- 回滚预案准备

## 风险控制

### 技术风险
1. **数据一致性**: 使用事务和数据校验确保迁移准确性
2. **性能问题**: 提前进行压力测试和性能调优
3. **兼容性问题**: 保持API接口向后兼容

### 业务风险
1. **功能缺失**: 详细的功能对比测试
2. **数据丢失**: 完整的数据备份和恢复方案
3. **服务中断**: 蓝绿部署和快速回滚机制

### 应对措施
1. **分阶段发布**: 降低单次发布风险
2. **监控告警**: 实时监控系统状态
3. **回滚方案**: 快速回滚到稳定版本
4. **应急预案**: 详细的故障处理流程

## 成功标准

### 技术指标
- [ ] 响应时间 < 100ms (P95)
- [ ] 可用性 > 99.9%
- [ ] 错误率 < 0.1%
- [ ] 内存使用 < 512MB
- [ ] CPU使用 < 50%

### 业务指标
- [ ] 功能完整性 100%
- [ ] 数据准确性 100%
- [ ] 零业务中断
- [ ] 用户满意度 > 95%

### 质量指标
- [ ] 代码覆盖率 > 80%
- [ ] 代码质量评分 > 8.0
- [ ] 文档完整性 100%
- [ ] 安全扫描通过

## 团队分工

### 后端开发 (3人)
- 核心服务开发
- 适配器系统开发
- 数据层开发

### 运维工程师 (1人)
- 基础设施搭建
- 部署和监控
- 性能调优

### 测试工程师 (1人)
- 测试用例设计
- 自动化测试
- 性能测试

### 项目经理 (1人)
- 项目协调
- 进度管理
- 风险控制

## 时间节点

| 阶段 | 开始时间 | 结束时间 | 里程碑 |
|------|----------|----------|--------|
| 第一阶段 | Week 1 | Week 2 | 基础设施完成 |
| 第二阶段 | Week 3 | Week 6 | 核心服务完成 |
| 第三阶段 | Week 7 | Week 8 | API开发完成 |
| 第四阶段 | Week 9 | Week 10 | 通知系统完成 |
| 第五阶段 | Week 11 | Week 13 | 测试完成 |
| 第六阶段 | Week 14 | Week 15 | 正式上线 |

**总工期**: 15周 (约3.5个月)

## 总结

本重构计划提供了详细的实施路径，确保：
1. **技术先进性**: 采用Go语言和微服务架构
2. **业务连续性**: 保持所有现有功能和特殊逻辑
3. **风险可控**: 分阶段实施，完善的回滚机制
4. **质量保证**: 全面的测试和监控体系

通过这个计划，可以成功将现有系统重构为高性能、高可用的Go语言微服务系统。
