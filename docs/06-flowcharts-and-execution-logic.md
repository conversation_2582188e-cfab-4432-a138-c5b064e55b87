# 流程图与执行逻辑文档

## 1. 系统整体架构流程

### 1.1 系统架构图
```mermaid
graph TB
    A[投保平台] --> B[API网关]
    B --> C[JWT认证]
    C --> D[数据验证]
    D --> E[数据转换]
    E --> F[保险公司适配器]
    F --> G[自动录单/API调用]
    G --> H[状态更新]
    H --> I[推送通知]
    I --> J[邮件报备]
    
    K[定时任务] --> L[异常处理]
    K --> M[状态检查]
    K --> N[邮件发送]
    
    O[子系统数据库] --> P[数据同步]
    P --> Q[报备邮件]
```

### 1.2 数据流转图
```mermaid
sequenceDiagram
    participant P as 投保平台
    participant A as AutoIns系统
    participant I as 保险公司
    participant S as 子系统
    participant E as 邮件系统
    
    P->>A: 1. 提交投保数据
    A->>A: 2. JWT认证
    A->>A: 3. 数据验证转换
    A->>I: 4. 调用保险公司API
    I->>A: 5. 返回处理结果
    A->>A: 6. 更新报文状态
    A->>P: 7. 推送处理结果
    A->>E: 8. 发送报备邮件
    S->>A: 9. 定时同步数据
    A->>E: 10. 发送子系统邮件
```

## 2. 核心业务流程

### 2.1 投保处理主流程
```mermaid
flowchart TD
    A[接收投保请求] --> B{JWT认证}
    B -->|失败| C[返回认证错误]
    B -->|成功| D[验证平台产品权限]
    D -->|无权限| E[返回权限错误]
    D -->|有权限| F[数据格式验证]
    F -->|验证失败| G[返回验证错误]
    F -->|验证成功| H[创建报文记录]
    H --> I[数据格式转换]
    I --> J{选择保险公司适配器}
    
    J -->|人保| K[PICCController]
    J -->|太保| L[CPICController]
    J -->|平安| M[PINGANController]
    J -->|华泰| N[HUATAIController]
    J -->|其他| O[其他Controller]
    
    K --> P[调用保险公司接口]
    L --> P
    M --> P
    N --> P
    O --> P
    
    P --> Q{调用结果}
    Q -->|成功| R[更新报文状态为已提交]
    Q -->|失败| S[更新错误信息]
    
    R --> T[创建推送通知]
    S --> U[记录错误日志]
    
    T --> V[返回成功响应]
    U --> W[返回失败响应]
```

### 2.2 数据转换流程
```mermaid
flowchart TD
    A[原始投保数据] --> B[字段映射]
    B --> C[数据类型转换]
    C --> D[业务规则应用]
    D --> E[时间处理]
    E --> F[金额格式化]
    F --> G[费率转换]
    G --> H[特殊字段处理]
    H --> I[生成保险公司格式数据]
    
    E --> E1[起运时间校验]
    E1 --> E2{是否当天起运}
    E2 -->|是| E3[延后1小时]
    E2 -->|否| E4[保持原时间]
    
    G --> G1[平台费率万分之几]
    G1 --> G2[转换为千分之几]
    G2 --> G3[除以10]
    
    H --> H1[发票号处理]
    H1 --> H2{平台类型}
    H2 -->|保呀| H3[invNo + freightNo]
    H2 -->|其他| H4[orderNo + invNo]
```

### 2.3 状态管理流程
```mermaid
stateDiagram-v2
    [*] --> 未处理: 创建报文
    未处理 --> 处理中: 开始录单
    处理中 --> 已提交: 提交成功
    处理中 --> 未处理: 处理失败(重试)
    已提交 --> 已完成: 获取保单号
    已提交 --> 已作废: 录单失败
    已完成 --> [*]: 流程结束
    已作废 --> [*]: 流程结束
    
    note right of 未处理: status=0
    note right of 已提交: status=1
    note right of 已完成: status=2
    note right of 已作废: status=-1
```

## 3. 定时任务执行流程

### 3.1 异常事件处理流程
```mermaid
flowchart TD
    A[每5分钟执行] --> B[查询已提交但未完成的报文]
    B --> C{检查时间条件}
    C -->|平安跨境电商| D[2小时后算失败]
    C -->|其他模式| E[10分钟后算失败]
    
    D --> F{是否可触发}
    E --> F
    F -->|否| G[跳过处理]
    F -->|是| H[标记缓存防重复]
    H --> I[分析失败原因]
    I --> J[发送失败回调]
    J --> K[记录处理日志]
```

### 3.2 邮件发送流程
```mermaid
flowchart TD
    A[每10分钟执行] --> B[查询需要发送邮件的报文]
    B --> C{检查邮件发送状态}
    C -->|已发送| D[跳过]
    C -->|未发送| E[查找对应子系统]
    E --> F{找到子系统}
    F -->|否| G[跳过]
    F -->|是| H[查询子系统保单数据]
    H --> I{数据存在}
    I -->|否| J[跳过]
    I -->|是| K[构建邮件内容]
    K --> L[添加邮件发送记录]
    L --> M[加入邮件队列]
    M --> N[异步发送邮件]
```

### 3.3 优孚保单检查流程
```mermaid
flowchart TD
    A[每1分钟执行] --> B[查询优孚平台异常保单]
    B --> C[筛选条件]
    C --> D[status=1 且 platform_id=1]
    D --> E[apply_no和policy_no为空]
    E --> F[handle_num < 1]
    F --> G{保单模式}
    G -->|太保| H[handleCPIC处理]
    G -->|其他| I[跳过]
    
    H --> J{是否当天保单}
    J -->|否| K[跳过]
    J -->|是| L{超过5分钟}
    L -->|否| M[跳过]
    L -->|是| N[重置状态]
    N --> O[更新起运时间]
    O --> P[增加处理次数]
```

## 4. 错误处理与重试机制

### 4.1 错误处理流程
```mermaid
flowchart TD
    A[发生错误] --> B{错误类型}
    B -->|网络错误| C[记录网络异常]
    B -->|数据错误| D[记录数据异常]
    B -->|业务错误| E[记录业务异常]
    
    C --> F{重试次数}
    D --> G[返回错误信息]
    E --> G
    
    F -->|< 3次| H[延时重试]
    F -->|>= 3次| I[标记失败]
    
    H --> J[增加重试次数]
    J --> K[重新处理]
    I --> L[发送失败通知]
```

### 4.2 队列重试机制
```mermaid
flowchart TD
    A[邮件发送任务] --> B{发送结果}
    B -->|成功| C[更新状态为已发送]
    B -->|失败| D{重试次数}
    D -->|< 3次| E[加入重试队列]
    D -->|>= 3次| F[标记发送失败]
    
    E --> G[等待重试时间]
    G --> H[重新发送]
    H --> B
    
    F --> I[记录失败日志]
    C --> J[完成任务]
```

## 5. 数据同步机制

### 5.1 子系统数据同步流程
```mermaid
flowchart TD
    A[定时任务启动] --> B[遍历子系统配置]
    B --> C[连接子系统数据库]
    C --> D[查询时间范围内数据]
    D --> E[10-40分钟前的数据]
    E --> F{数据存在}
    F -->|否| G[处理下一个子系统]
    F -->|是| H[检查主系统是否存在]
    H --> I{主系统已存在}
    I -->|是| J[跳过该条数据]
    I -->|否| K[检查邮件发送状态]
    K --> L{已发送邮件}
    L -->|是| M[跳过该条数据]
    L -->|否| N[发送报备邮件]
    N --> O[处理下一条数据]
```

### 5.2 数据一致性保证
```mermaid
flowchart TD
    A[数据操作] --> B[开启事务]
    B --> C[执行业务逻辑]
    C --> D{操作结果}
    D -->|成功| E[提交事务]
    D -->|失败| F[回滚事务]
    
    E --> G[记录成功日志]
    F --> H[记录错误日志]
    
    G --> I[返回成功]
    H --> J[返回失败]
```

## 6. 监控与告警流程

### 6.1 系统监控流程
```mermaid
flowchart TD
    A[监控系统] --> B[检查服务状态]
    B --> C[检查数据库连接]
    C --> D[检查队列状态]
    D --> E[检查邮件服务]
    E --> F{发现异常}
    F -->|否| G[继续监控]
    F -->|是| H[发送告警]
    H --> I[记录告警日志]
    I --> J[通知运维人员]
```

### 6.2 性能监控流程
```mermaid
flowchart TD
    A[性能监控] --> B[API响应时间]
    B --> C[数据库查询时间]
    C --> D[队列处理时间]
    D --> E[内存使用情况]
    E --> F{超过阈值}
    F -->|否| G[继续监控]
    F -->|是| H[性能告警]
    H --> I[分析性能瓶颈]
    I --> J[优化建议]
```

## 7. 部署与运维流程

### 7.1 部署流程
```mermaid
flowchart TD
    A[代码提交] --> B[自动化测试]
    B --> C{测试通过}
    C -->|否| D[修复问题]
    C -->|是| E[构建部署包]
    E --> F[备份当前版本]
    F --> G[停止服务]
    G --> H[部署新版本]
    H --> I[数据库迁移]
    I --> J[配置更新]
    J --> K[启动服务]
    K --> L[健康检查]
    L --> M{检查通过}
    M -->|否| N[回滚版本]
    M -->|是| O[部署完成]
```

### 7.2 运维监控流程
```mermaid
flowchart TD
    A[运维监控] --> B[日志监控]
    B --> C[错误率监控]
    C --> D[性能监控]
    D --> E[资源监控]
    E --> F{发现问题}
    F -->|否| G[继续监控]
    F -->|是| H[问题分析]
    H --> I[制定解决方案]
    I --> J[执行修复]
    J --> K[验证修复效果]
    K --> L[更新监控策略]
```
