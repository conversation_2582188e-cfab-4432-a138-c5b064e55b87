# 保险公司特殊处理逻辑详细文档

## 概述

本文档详细记录了每个保险公司、每个账号的特殊处理逻辑和分支条件，为Go语言重构提供精确的业务规则参考。

## 1. 人保财险 (PICC) 特殊处理逻辑

### 1.1 账号配置与协议开始日期

```go
type PICCAccountConfig struct {
    AccountCode       string    `json:"account_code"`
    AgreementStartDate string   `json:"agreement_start_date"`
    Description       string    `json:"description"`
}

var PICCAccounts = map[string]PICCAccountConfig{
    "yrwl02":         {"yrwl02", "2019-05-27", "赢睿物流02账号"},
    "yrwl01":         {"yrwl01", "2020-05-22", "赢睿物流01账号"},
    "cdwh1":          {"cdwh1", "2019-10-18", "成都万恒1号账号"},
    "cdwh2":          {"cdwh2", "2019-10-18", "成都万恒2号账号"},
    "cdwh3":          {"cdwh3", "2019-10-18", "成都万恒3号账号"},
    "wanheng":        {"wanheng", "2020-05-04", "万恒账号"},
    "muzixing0001":   {"muzixing0001", "2019-09-24", "木子行0001账号"},
    "muzixing02":     {"muzixing02", "2019-09-20", "木子行02账号"},
    "muzixing03":     {"muzixing03", "2019-09-20", "木子行03账号"},
    "muzixing4":      {"muzixing4", "2019-09-20", "木子行4号账号"},
    "muzixing05":     {"muzixing05", "2019-09-20", "木子行05账号"},
    "muzixing06":     {"muzixing06", "2019-09-20", "木子行06账号"},
    "quanqiutong01":  {"quanqiutong01", "2019-09-24", "全球通01账号"},
    "quanqiutong2":   {"quanqiutong2", "2019-09-24", "全球通2号账号"},
    "sxdh":           {"sxdh", "2020-07-14", "陕西大华账号"},
    "BAOCANGWANG02":  {"BAOCANGWANG02", "2020-07-17", "保仓网02账号"},
    "BAOCANGWANG04":  {"BAOCANGWANG04", "2020-10-16", "保仓网04账号"},
    "YNYJB2":         {"YNYJB2", "2020-12-01", "云南运吉宝2号账号"},
    "4403A00210":     {"4403A00210", "2020-03-12", "4403A00210账号"},
    "210314yjb02":    {"210314yjb02", "2021-01-09", "210314运吉宝02账号"},
    "DEJUN":          {"DEJUN", "2022-03-23", "德骏账号"},
}
```

### 1.2 发票号格式特殊处理

```go
func (p *PICCAdapter) GetInvoiceNumber(policy InsurancePolicy, platform Platform) string {
    if platform.ID == 5 {
        // 平台ID为5时，使用特殊格式
        return fmt.Sprintf("%s / %s", policy.InvNo, policy.FreightNo)
    }
    // 其他平台使用默认格式
    return fmt.Sprintf("%s / %s", policy.OrderNo, policy.InvNo)
}
```

### 1.3 客户数据特殊处理（平台ID=5）

```go
func (p *PICCAdapter) GetClientData(policy InsurancePolicy, platform Platform) map[string]interface{} {
    if platform.ID == 5 {
        return map[string]interface{}{
            "holderProvince":           "110000",
            "holderCity":              "110100", 
            "holderCounty":            "110101",
            "holderCustomerType":      "0",
            "holderOverseas":          "0",
            "holderDocumentType":      "统一社会信用代码",
            "holderIdenty":            "91310230MA1K2FMP33",
            "holderStartDateValid":    "2019-01-04",
            "holderEndDateValid":      "2039-01-03",
            "holderUnitProperties":    "300",
            "holderLinkMan":           policy.HolderName,
            "recognizeeProvince":      "110000",
            "recognizeeCity":          "110100",
            "recognizeeCounty":        "110101",
            "recognizeeCustomerType":  "0",
            "recognizeeOverseas":      "0",
            "documentType":            "统一社会信用代码",
            "recognizeeIdenty":        "91310115MA1H90HR24",
            "recognizeeStartDateValid": "2019-01-04",
            "recognizeeEndDateValid":  "2039-01-03",
            "recognizeeUnitProperties": "300",
            "recognizeeLinkMan":       policy.RecognizeeName,
        }
    }
    return map[string]interface{}{}
}
```

## 2. 太保财险 (CPIC) 特殊处理逻辑

### 2.1 账号配置与协议信息

```go
type CPICAccountConfig struct {
    AccountCode  string   `json:"account_code"`
    ProtocolNo   string   `json:"protocol_no"`
    UnitCode     string   `json:"unit_code"`
    Franchise    string   `json:"franchise"`
    RateInfo     []string `json:"rate_info"` // [minRate, limitAmount, minPremium]
    Description  string   `json:"description"`
}

var CPICAccounts = map[string]CPICAccountConfig{
    "SHTB": {
        AccountCode:  "SHTB",
        ProtocolNo:   "A|3010200|C20180144-19001P000431|CAGHYX190032",
        UnitCode:     "3010200",
        Franchise:    "本保单其他承保条件同协议。",
        RateInfo:     []string{"0.5", "3000000", "50"},
        Description:  "上海太保账号",
    },
    "YRWL": {
        AccountCode:  "YRWL",
        ProtocolNo:   "A|3010100|C20190295|0",
        UnitCode:     "3010100",
        Franchise:    "本保单其他承保条件同协议; 每次事故绝对免赔额为人民币1000或损失金额的10%，两者取高;",
        RateInfo:     []string{"0.15", "500000", "3"},
        Description:  "赢睿物流账号",
    },
    "CDYF": {
        AccountCode:  "CDYF",
        ProtocolNo:   "C|3010100|CSHHHYX2024P000473|0|CSHHHYX2024Q000452",
        UnitCode:     "3010100",
        Franchise:    "每次事故的综合免赔为绝对免赔额2000人民币或损失金额的10%，两者以高者为准 ，包括全损。\\r\\n火灾、翻车事故每次事故的综合免赔为绝对免赔额2000人民币或损失金额的15%，两者以高者为准 ，包括全损。\\r\\n针对酒类（单瓶价格低于100元/瓶）的综合免赔为绝对免赔额2000人民币或损失金额的10%，两者以高者为准 ，包括全损。\\r\\n针对易碎品的综合免赔为保额金额的3% ，包括全损。",
        RateInfo:     []string{"0.15", "3000000", "15"},
        Description:  "成都优孚账号",
    },
}
```

### 2.2 CDYF账号特殊处理逻辑

```go
func (c *CPICAdapter) ProcessCDYFAccount(policy InsurancePolicy, setting []string) map[string]interface{} {
    data := c.getBaseData(policy, setting)
    
    if setting[0] == "CDYF" {
        // 强制设置投保人名称
        data["holderName"] = "成都优孚世纪信息技术有限公司"
        
        // 解析保险设置
        insSetting := strings.Split(setting[2], "^")
        
        // 根据条款类型和货物类型设置特殊费率
        if insSetting[6] == "JB" && insSetting[0] == "0309" {
            // 基本险 + 轻工品类（非易碎）
            data["rate"] = 0.12
            data["tCargoCoverage"] = c.getCargoCoverageJB0309()
        } else if insSetting[6] == "JB" && insSetting[0] == "0301" {
            // 基本险 + 易碎品类
            data["tCargoCoverage"] = c.getCargoCoverageJB0301()
        } else if insSetting[6] == "ZH" {
            // 综合险
            data["tCargoCoverage"] = c.getCargoCoverageZH()
        }
        
        // 发票相关处理
        data["billType"] = "0"
        if policy.InvType == "1" {
            data["billType"] = "0"
        } else {
            data["billType"] = "1"
        }
        data["invHead"] = policy.InvHead
        data["bussLicence"] = policy.BussLicence
        data["invHeadLicence"] = policy.InvHeadLicence
        
        // 设置特约条款
        data["specialize"] = c.getCDYFSpecialTerms()
    }
    
    return data
}
```

### 2.3 货物覆盖范围配置（CDYF专用）

```go
func (c *CPICAdapter) getCargoCoverageJB0309() string {
    return `{"tCargoCoverageId":{"auditType":"C","unitCode":"3010100","applyNo":"CSHHHYX2024P000473","applyEndorseNo":"0","coverageNo":2},"classesCode":"11040200","flightAreaCode":"0","rate":0.000120,"detailLimit":3000000.0000,"startPlace":null,"endPlace":null,"tCargoCoverageCargos":[{"id":400475,"code":"0309","name":"轻工品类（非易碎）","cibscode":"0309"},{"id":400476,"code":"0701","name":"设备仪器类（整机）","cibscode":"0701"}],"tCargoCoverageMains":[{"id":214913,"code":"HY000063","name":null,"cibscode":null}],"tCargoCoveragePacks":[{"id":297632,"code":"104","name":"裸装","cibscode":"05"},{"id":297630,"code":"101","name":"箱装","cibscode":"01"},{"id":297631,"code":"103","name":"托盘","cibscode":"03"}],"tCargoCoverageKinds":[{"id":378435,"code":"4","name":"公路","cibscode":"4"}],"tCargoCoverageClauses":[{"id":276994,"code":"HY000063.HYCC00118","name":"国内水陆路基本险","cibscode":"JB"}],"classesName":"公路货运险"}`
}
```

## 3. 平安财险 (PINGAN) 特殊处理逻辑

### 3.1 企业账号识别

```go
var PINGANEnterpriseAccounts = []string{
    "CGHZYRWLKJ00002",
    "CGNJYB00013", 
    "CGKMYJB00002",
    "CGFZZF200001",
    "CGGZYNYZK200001",
    "CGSHZHMBX00002",
    "CGSHZQLJJ00001",
    "CGGZQLBXJJ00001",
    "CGZYJJ00002",
    "CGGZLJGYL00001",
}

func (p *PINGANAdapter) IsEnterpriseAccount(accountCode string) bool {
    for _, account := range PINGANEnterpriseAccounts {
        if account == accountCode {
            return true
        }
    }
    return false
}
```

### 3.2 证件类型特殊处理

```go
func (p *PINGANAdapter) GetCertificateType(policy InsurancePolicy, accountCode string) (string, string) {
    holderType := "03"  // 默认个人
    recognizeeType := "03"  // 默认个人
    
    if p.IsEnterpriseAccount(accountCode) {
        holderType = "99"   // 企业
        recognizeeType = "01"  // 企业
    }
    
    return holderType, recognizeeType
}
```

### 3.3 特殊账号处理逻辑

```go
func (p *PINGANAdapter) ProcessSpecialAccount(policy InsurancePolicy, accountCode string) map[string]interface{} {
    data := make(map[string]interface{})
    
    switch accountCode {
    case "CGZYJJ00002":
        // 上海平安特殊处理
        data["special"] = ""
        if policy.Subject == "normal" && policy.GoodsType == "10" {
            data["deductible"] = "每次事故绝对免赔为保额的3%"
        }
        // 默认身份证号
        if policy.RecognizeeIdenty == "" {
            data["recognizeeIdenty"] = "91310230MA1K2FMP33"
        }
        
    case "CGGZQLBXJJ00001":
        // 广东平安特殊处理
        data["special"] = ""
        data["deductible"] = ""
        if policy.Subject != "manual" {
            // 非手动录入时的特殊处理
        }
        // 默认身份证号
        if policy.RecognizeeIdenty == "" {
            data["recognizeeIdenty"] = "91310230MA1K2FMP33"
        }
        
    case "CGSHZQLJJ00001":
        // 深圳平安-运吉宝
        data["name"] = "深圳平安-运吉宝"
        data["special"] = ""
        data["deductible"] = ""
        
    case "CGSHZHMBX00002":
        // 深圳平安-保呀
        data["name"] = "深圳平安-保呀"
        data["special"] = ""
        data["deductible"] = ""
    }
    
    return data
}
```

### 3.4 裸装货物特殊免赔处理

```go
func (p *PINGANAdapter) ProcessNakedGoods(policy InsurancePolicy) string {
    if policy.Pack == "裸装" {
        return "裸装：本保单不承保由于刮擦，锈损，凹瘪引起的损失。"
    }
    return ""
}
```

## 4. 华泰财险 (HUATAI) 特殊处理逻辑

### 4.1 XML数据结构处理

```go
type HUATAIXMLData struct {
    XMLName xml.Name `xml:"InsureInfo"`
    // XML结构定义
}

func (h *HUATAIAdapter) BuildXMLData(policy InsurancePolicy) string {
    // 构建XML数据结构
    data := HUATAIXMLData{
        // 填充数据
    }
    
    xmlData, _ := xml.Marshal(data)
    return string(xmlData)
}
```

### 4.2 货物类型映射

```go
var HUATAIGoodsTypeMapping = map[string][]string{
    "纺织原料及纺织制品": {"SX001411", "SX00140065"},
    "机器设备及其零件、附件": {"SX001416", "SX00140087"},
    "食品": {"SX001404", "SX00140019"},
    "玻璃及玻璃制品": {"SX001413", "SX00140072"},
    "新车": {"SX001417", "SX00140089"},
    "二手车": {"SX001417", "SX00140089"},
    // ... 更多映射
}
```

### 4.3 运输方式映射

```go
var HUATAITransportMapping = map[string]map[string][]string{
    "3": { // 水路
        "厢式货车": {"SX001501", "01"},
        "非厢式货车": {"SX001501", "05"},
    },
    "5": { // 公路
        "厢式货车": {"SX001502", "01"},
        "非厢式货车": {"SX001502", "02"},
    },
}
```

## 5. 中华联合 (SINOSIG) 特殊处理逻辑

### 5.1 产品配置处理

```go
func (s *SINOSIGAdapter) GetProductSettings(productCode string, policy InsurancePolicy) map[string]interface{} {
    if productCode == "2020022194297" {
        // 保呀产品
        return map[string]interface{}{
            "sysFlag":          "BAOYA",
            "comCode":          "07710200",
            "protocolNo":       "10771YAB02023000006",
            "operateCode":      "BAOYA",
            "underwriteStatus": "00",
            "ladingNo":         policy.FreightNo,
            "invoiceNo":        policy.InvNo,
            "claimSite":        s.cutStr(s.convertAddress(policy.ToLoc), 18),
            "packerCode":       s.getPackerCode(policy.Pack, 0),
            "packerName":       s.getPackerCode(policy.Pack, 1),
        }
    } else {
        // 阿拉丁产品
        return map[string]interface{}{
            "sysFlag":          "aladdin",
            "comCode":          "07514300",
            "protocolNo":       "10771YAB02020000035",
            "operateCode":      "aladdin",
            "underwriteStatus": "01",
            "ladingNo":         fmt.Sprintf("%s-%s", policy.InvNo, policy.OrderNo),
            "invoiceNo":        "",
            "claimSite":        "中国",
            "packerCode":       "024",
            "packerName":       "标准包装",
        }
    }
}
```

### 5.2 包装代码映射

```go
var SINOSIGPackerMapping = map[string][]string{
    "裸装": {"024", "标准包装"},
    "散装": {"024", "标准包装"},
    "纸箱": {"002", "纸箱"},
    "木箱": {"001", "木箱"},
    "捆包": {"024", "标准包装"},
    "袋装": {"023", "袋子"},
    "篓装": {"024", "标准包装"},
    "托盘": {"020", "托盘"},
    "桶装": {"019", "桶"},
    "罐装": {"024", "标准包装"},
}
```

## 6. 史带财险 (STARR) 特殊处理逻辑

### 6.1 身份证处理

```go
func (s *STARRAdapter) ProcessIdentity(policy InsurancePolicy) map[string]interface{} {
    data := make(map[string]interface{})
    
    idCard := policy.RecognizeeIdenty
    if len(idCard) == 15 || len(idCard) == 18 {
        // 提取生日
        if len(idCard) == 18 {
            data["birth"] = idCard[6:14]
        } else {
            data["birth"] = "19" + idCard[6:12]
        }
        
        // 提取性别
        lastDigit, _ := strconv.Atoi(string(idCard[len(idCard)-1]))
        if lastDigit%2 == 1 {
            data["gender"] = "M"
        } else {
            data["gender"] = "F"
        }
    }
    
    // 判断是否为公司
    if strings.Contains(policy.RecognizeeName, "公司") {
        data["partType"] = "QY"
        data["cardType"] = "104"
    } else {
        data["partType"] = "GR"
        data["cardType"] = "1"
    }
    
    return data
}
```

### 6.2 时间处理

```go
func (s *STARRAdapter) ProcessStartTime(departureDate int64) string {
    // 起保时间增加24小时
    startTime := time.Unix(departureDate, 0).Add(24 * time.Hour)
    return startTime.Format("2006-01-02 15:04:05")
}
```

## 7. 第三方平台 (BDYF) 特殊处理逻辑

### 7.1 产品配置

```go
var BDYFProducts = map[string]ProductConfig{
    "pingan_puhuo_jb_zhengzhou": {
        ProductName:  "郑州平安普货（西藏除外 剔除罐头、木材、原棉，限额300万）(基本险)",
        Company:      "pingan",
        CompanyLogo:  "images/tb16.png",
        InsureType:   "puhuo",
        ClauseType:   "jb",
        Rate:         "1.2",
    },
    // ... 更多产品配置
}
```

### 7.2 数据转换处理

```go
func (b *BDYFAdapter) ProcessData(policy InsurancePolicy, setting []string) map[string]interface{} {
    product := BDYFProducts[setting[2]]
    
    data := map[string]interface{}{
        "bx_company":         product.Company,
        "bx_company_logo":    product.CompanyLogo,
        "bx_type":           product.ClauseType,
        "bx_product_tag":    setting[2],
        "bx_hwtype":         product.InsureType,
        "myRates":           product.Rate,
        "bx_jine":           policy.InsuredAmount / 10000, // 转换为万元
        "bx_hetongnum":      fmt.Sprintf("%s/%s", policy.OrderNo, policy.InvNo),
        "transType":         "汽运",
        "loadType":          "非集装箱",
    }
    
    // 处理空值和数字值
    for key, value := range data {
        if value == nil || value == "" {
            data[key] = ""
        }
        if v, ok := value.(float64); ok {
            data[key] = fmt.Sprintf("%.2f", v)
        }
    }
    
    return data
}
```

## 8. 通用业务规则

### 8.1 时间处理规则

```go
func ProcessDepartureTime(departureDate int64) (string, string) {
    t := time.Unix(departureDate, 0)
    
    // 如果分钟数大于50，增加1小时
    if t.Minute() > 50 {
        t = t.Add(1 * time.Hour)
    }
    
    // 如果是当天，增加1小时
    now := time.Now()
    if t.Format("2006-01-02") == now.Format("2006-01-02") {
        t = t.Add(1 * time.Hour)
    }
    
    return t.Format("2006-01-02"), t.Format("15")
}
```

### 8.2 费率转换规则

```go
func ConvertRate(platformRate float64) float64 {
    // 平台费率单位是万分之几，保险公司费率单位是千分之几
    return platformRate / 10
}
```

### 8.3 金额格式化规则

```go
func FormatAmount(amount float64) string {
    return fmt.Sprintf("%.2f", amount)
}
```

## 总结

本文档详细记录了所有保险公司的特殊处理逻辑，包括：

1. **账号级别的配置差异**：每个账号的协议信息、费率设置、免赔条款等
2. **条件分支处理**：基于账号代码、产品类型、货物类型等的特殊逻辑
3. **数据转换规则**：不同保险公司要求的数据格式和字段映射
4. **业务规则实现**：时间处理、费率计算、金额格式化等通用规则

这些详细的业务规则为Go语言重构提供了精确的实现指导，确保重构后的系统能够完全保持原有的业务逻辑和特殊处理能力。
