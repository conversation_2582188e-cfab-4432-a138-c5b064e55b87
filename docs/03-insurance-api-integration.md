# 保险公司API集成逻辑

## 1. 集成架构概述

### 1.1 适配器模式设计
系统采用适配器模式，为每家保险公司实现独立的控制器，统一实现 `approval()` 方法接口：

```php
// 统一接口
public function approval($policy, $product, $settings, $platform = null)
{
    // 1. 数据预处理
    // 2. 字段映射转换
    // 3. 业务规则应用
    // 4. 返回标准格式数据
}
```

### 1.2 支持的保险公司
| 保险公司 | 控制器 | 类型 | 特点 |
|---------|--------|------|------|
| 人保财险 | PICCController | 自动录单 | 国内货运险 |
| 人保财险(进出口) | PICCIntlController | 自动录单 | 进出口货运险 |
| 太保财险 | CPICController | 自动录单 | 国内货运险 |
| 太保财险(进出口) | CPICIntlController | 自动录单 | 进出口货运险 |
| 平安财险 | PINGANController | 自动录单 | 国内货运险 |
| 平安财险(进出口) | PINGANIntlController | 自动录单 | 进出口货运险 |
| 华泰财险 | HUATAIController | API接口 | XML格式 |
| 中华联合 | SinosigController | API接口 | XML格式 |
| 史带财险 | STARRController | API接口 | JSON格式 |
| 国人财险 | GUORENController | 自动录单 | 特殊格式 |
| 其他平台 | BDYFController | 自动录单 | 第三方平台 |

## 2. 核心数据转换逻辑

### 2.1 通用数据映射

#### 2.1.1 时间处理逻辑
```php
// 起运时间处理 (通用逻辑)
if (strtotime($startDate) <= strtotime(date('Y-m-d'))) {
    $startDate = date('Y-m-d');
    $startTime = date('H') + 1; // 当天起运需要延后1小时
    if (date('i') > 50) {
        $startTime += 1; // 分钟超过50分钟再延后1小时
    }
    if ($startTime >= 24) {
        $startDate = date('Y-m-d', strtotime("+1 day"));
        $startTime = '00';
    }
}
```

#### 2.1.2 费率转换
```php
// 费率单位转换 (平台万分之几 → 保险公司千分之几)
"ratio" => $policy['rate'] / 10
```

#### 2.1.3 金额格式化
```php
// 保额格式化
"insuredAmount" => sprintf('%.2f', floatval($policy['insuredAmount']))
```

### 2.2 平台特殊处理

#### 2.2.1 发票号处理
```php
// 根据平台ID处理发票号格式
if($platform['id'] == 5) { // 保呀平台
    $invNo = $policy['invNo'] . ' / ' . $policy['freightNo'];
} else {
    $invNo = $policy['orderNo'] . ' / ' . $policy['invNo'];
}
```

## 3. 各保险公司具体实现

### 3.1 人保财险 (PICC)

#### 3.1.1 数据结构
```php
$data = [
    "config" => [
        "account" => $params['username'],
        "password" => $params['password'],
    ],
    "message" => [
        "holderName" => $policy['holderName'],        // 投保人名称
        "recognizeeName" => $policy['recognizeeName'], // 被保人名称
        "invNo" => $invNo,                            // 发票号
        "goodsName" => $policy['goodsName'],          // 货物名称
        "quantity" => $policy['quantity'],            // 包装及数量
        "transport" => $policy['transport'],          // 运输工具
        "fromLoc" => $policy['fromLoc'],              // 起运地
        "toLoc" => $policy['toLoc'],                  // 目的地
        "departureDate" => $startDate,                // 起运日期
        "insuredAmount" => $formattedAmount,          // 保额
        "ratio" => $policy['ratio'] / 10,             // 费率
        "premium" => $policy['premium'],              // 保费
        "deductible" => $allSettings[1],              // 免赔
        "remark" => $allSettings[2],                  // 特约
        // ... 其他固定字段
    ]
];
```

#### 3.1.2 特殊逻辑
- 账户数据映射：`getAccountData()`
- 条款名称映射：`getClauseName()`
- 附加险处理：`getAdditionClause()`

### 3.2 太保财险 (CPIC)

#### 3.2.1 数据结构
```php
$data = [
    "message" => [
        "insCommonInfoDto" => [
            "procotolNo" => $messageData['procotolNo'],    // 预保协议号
            "classesCode" => "********",                   // 险种代码
            "effectDate" => $startDate,                    // 签单日期
        ],
        "cargoInfoDto" => [
            "mark" => $messageData['mark'],                // 发票号
            "cargoName" => $messageData['cargoName'],      // 货物明细
            "sailDate" => $startDate,                      // 起运日期
            "sailDateHours" => $DateHours,                 // 起运小时
        ],
        "feeInfoDto" => [
            "amount" => $messageData['amount'],            // 保险金额
            "premium" => round($messageData['premium'], 2), // 保费
            "rate" => $messageData['rate'],                // 费率
        ],
        // ... 其他DTO结构
    ]
];
```

#### 3.2.2 特殊处理
- 配置解析：`getMessage()` 方法解析复杂配置字符串
- 字符处理：替换制表符和换行符
- 特殊账户处理：CDYF账户的特殊配置

### 3.3 平安财险 (PINGAN)

#### 3.3.1 数据预处理流程
```php
$policy = $this->prepareDepartureDate($policy);    // 起运时间处理
$policy = $this->prepareHolderData($policy);       // 投保人数据
$policy = $this->prepareClientData($policy);       // 被保人数据
$policy = $this->prepareCargoData($policy);        // 货物数据
$policy = $this->preparePremiumData($policy);      // 保费数据
$policy = $this->prepareAccountData($policy);      // 账户数据
```

#### 3.3.2 证件类型处理
```php
// 根据客户类型和账户设置证件类型
$policy['holderIdType'] = $policy['recognizeeIdType'] = '03';

if ($policy['holderCustomerType'] == '1') {
    $policy['holderIdType'] = '99'; // 企业
}

// 特殊账户的证件类型映射
if (in_array($account, ['CGHZYRWLKJ00002', 'CGNJYB00013', ...])) {
    $policy['holderIdType'] = '99';
}
```

### 3.4 华泰财险 (HUATAI)

#### 3.4.1 XML格式数据
```php
$xmlstr = '<?xml version="1.0" encoding="UTF-8"?>
<Policy>
    <PolicyInfo>
        <RdrCde>' . $clause[0] . '</RdrCde>
        <StartTM>' . date('Y-m-d\TH:i:s', $policy['departureDate']) . '</StartTM>
        <EndTM>' . date('Y-m-d\TH:i:s', $policy['departureDate'] + 365*24*3600) . '</EndTM>
        <SumInsured>' . $policy['insuredAmount'] . '</SumInsured>
        <Premium>' . $policy['premium'] . '</Premium>
    </PolicyInfo>
    <Cargo>
        <CargoName>' . $policy['goodsName'] . '</CargoName>
        <FromArea>' . $policy['fromLoc'] . '</FromArea>
        <ToArea>' . $policy['toLoc'] . '</ToArea>
    </Cargo>
    <Insured>
        <Name>' . $policy['recognizeeName'] . '</Name>
        <ID>' . $recognizeePhone . '</ID>
    </Insured>
</Policy>';

return [
    "config" => ["account" => $username, "password" => $password],
    "message" => base64_encode($xmlstr),
];
```

#### 3.4.2 货物类型映射
```php
protected function getGoodsType($goodsType)
{
    $data = [
        '纺织原料及纺织制品' => ['SX001411','SX00140065'],
        '机器设备及其零件、附件' => ['SX001416','SX00140087'],
        '食品' => ['SX001404','SX00140019'],
        // ... 更多映射
    ];
    return $data[$goodsType];
}
```

### 3.5 史带财险 (STARR)

#### 3.5.1 JSON格式数据
```php
$data = [
    "message" => [
        "ProductCode" => 60021,                    // 产品编号
        "TransCode" => $this->create_guid(),       // 唯一交易码
        "PolicyList" => [
            [
                "DurationType" => "M",             // 保障单位
                "SA" => $ins['insuredAmount'],     // 保额
                "PolicyHolder" => [
                    "Name" => $ins['holderName'],
                    "CardType" => $CardType,
                    "PartType" => $PartType,       // GR:个人, QY:企业
                ],
                "ExtData" => [
                    "CargoItem_Description" => $ins['goodsName'],
                    "CargoTransportWay_Description" => "3", // 运输方式
                    "VehiclePlateNo" => $ins['transport'],
                ],
            ]
        ],
        "Key" => strtoupper(md5($PartnerCode . $PartnerKey . $TransCode)),
    ]
];
```

#### 3.5.2 身份识别逻辑
```php
// 根据身份证号获取性别和生日
if (strlen($ins['recognizeeIdenty']) > 15) {
    $birth = substr($idcard, 6, 8);
    $sex = substr($idcard, -1, 1) % 2 ? '1' : '0';
    $Sex = $sex == 1 ? 'M' : 'F';
}

// 根据名称判断企业/个人
if (strstr($ins['recognizeeName'], '公司')) {
    $PartType = 'QY';  // 企业
    $CardType = '104';
} else {
    $PartType = 'GR';  // 个人
    $CardType = '1';
}
```

### 3.6 中华联合 (SINOSIG)

#### 3.6.1 XML格式处理
```php
$xmlstr = '<?xml version="1.0" encoding="UTF-8"?>
<TransPolicyRequest>
    <sendSeq>' . md5($policy['orderNo']) . '</sendSeq>
    <sendTime>' . date('Y-m-d H:i:s') . '</sendTime>
    <sysFlag>' . $platformSettings['sysFlag'] . '</sysFlag>
    <policy>
        <riskCode>' . $sys_params[1] . '</riskCode>
        <sumAmountRMB>' . $this->parse_coverage_to_num($policy['insuredAmount']) . '</sumAmountRMB>
        <sumPremiumRMB>' . $this->parse_coverage_to_num($policy['premium']) . '</sumPremiumRMB>
    </policy>
</TransPolicyRequest>';
```

#### 3.6.2 平台配置差异
```php
protected function getProductSettings($product, $policy)
{
    if($product['account'] == 'youfu') {
        return [
            'sysFlag' => 'youfu',
            'comCode' => '********',
            'protocolNo' => '10771YAB02020000006',
        ];
    } else {
        return [
            'sysFlag' => 'aladdin',
            'comCode' => '********', 
            'protocolNo' => '10771YAB02020000035',
        ];
    }
}
```

## 4. 错误处理与监控

### 4.1 数据验证
- 必填字段检查
- 数据格式验证
- 业务规则校验

### 4.2 异常处理
- 网络超时处理
- API响应错误处理
- 数据转换异常处理

### 4.3 日志记录
- 请求数据记录
- 响应结果记录
- 错误信息记录

## 5. 配置管理

### 5.1 产品配置
```json
{
    "username": "账户名",
    "password": "密码",
    "special_settings": {
        "rate_multiplier": 10,
        "time_offset": 1
    }
}
```

### 5.2 系统参数
- 技术配置：账号|密码|大类|小类|主险|附加险|保单号头
- 业务配置：免赔额|特约条款|费率设置
- 平台配置：接口地址|超时设置|重试次数

## 6. 扩展性设计

### 6.1 新增保险公司
1. 创建新的Controller类
2. 实现approval()方法
3. 在InsureController中添加case分支
4. 配置产品mode映射

### 6.2 接口标准化
- 统一的数据输入格式
- 标准的错误码定义
- 一致的响应结构
