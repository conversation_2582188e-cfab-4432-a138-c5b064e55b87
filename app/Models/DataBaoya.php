<?php

namespace App\Models;

use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class DataBaoya extends Model
{
    protected $connection = 'mysql_by';
    protected $table = "by_policies as a";
    public $timestamps = false;


    public function index($field_name,$order_no){

        $data = self::select(
            'a.sys_order_no',
            'a.insured_name as insured_name',
            'a.coverage',
            'a.created_at as create_time',

            'b.clause_main as species',
            'b.inform_emails as email',

            'c.departure as departure',
            'c.stopovers as stopovers',
            'c.destination as destination',
            'c.ship_time as start_time',
            'c.goods_name',
            'c.goods_amount',
            'c.pack_type',
            'c.vehicle_license_no as vehicle_license_no',
            'c.deductible',
            'c.specials'
            )
            ->join('by_product_rates as b','b.product_code','a.product_code')
            ->join('by_policy_cargos as c','c.policy_id','a.id')
            ->where('a.'.$field_name,$order_no)
            ->first();
        if($data){
            $data['email'] = explode(';',$data['email']);
        }
        return $data;
    }

    public function indexs($field_name,$time1,$time2){

        $data = self::select(
            'a.sys_order_no',
            'a.insured_name as insured_name',
            'a.coverage',
            'a.created_at as create_time',

            'b.clause_main as species',
            'b.inform_emails as email',

            'c.departure as departure',
            'c.stopovers as stopovers',
            'c.destination as destination',
            'c.ship_time as start_time',
            'c.goods_name',
            'c.goods_amount',
            'c.pack_type',
            'c.vehicle_license_no as vehicle_license_no',
            'c.deductible',
            'c.specials'
            )
            ->join('by_product_rates as b','b.product_code','a.product_code')
            ->join('by_policy_cargos as c','c.policy_id','a.id')
            ->where('a.'.$field_name,'<',$time1)
            ->where('a.'.$field_name,'>',$time2)
            ->where('a.status',1)
            ->get();
        if($data){
            foreach ($data as $k=>$v){
                $data[$k]->email = explode(';',$data[$k]->email);
            }
        }
        return $data;
    }
}
