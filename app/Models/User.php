<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use SoftDeletes;
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'fullname', 'mobile', 'username', 'password',
    ];

    protected $dates = ['deleted_at'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    public function getUsers($request)
    {
        return self::orderBy('id', 'desc')
            ->when($request->input('keywords'), function ($q, $keywords) {
                $q->where('fullname', 'like', $keywords . '%');
                $q->orWhere('mobile', 'like', '%' . $keywords);
            })
            ->paginate($request->input('page_size', 10));
    }

    public function getUserById($id){
        return self::findOrFail($id);
    }
}
