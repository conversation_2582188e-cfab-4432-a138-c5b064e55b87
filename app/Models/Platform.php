<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Tymon\JWTAuth\Contracts\JWTSubject;

class Platform extends Authenticatable implements JWTSubject
{
    use SoftDeletes;
    use Notifiable;

    protected $dates = ['deleted_at'];

    protected $fillable = ['title', 'app_id', 'secret_key', 'status'];

    public function getPlatforms($request)
    {
        return self::orderBy('id', 'desc')
            ->when($request->input('keywords'), function ($q, $keywords) {
                $q->where('app_id', 'like', $keywords . '%');
            })
            ->paginate($request->input('page_size', 10));
    }

    public function getPlatformById($id){
        return self::findOrFail($id);
    }

    public function getStatus()
    {
        switch ($this->attributes['status']) {
            case '1':
                return '禁用';
                break;
            case '2':
                return '启用';
                break;
        }
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }
}
