<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Message extends Model
{
    use SoftDeletes;

    protected $dates = ['deleted_at'];

    protected $fillable = [
        'platform_id',
        'message_id',
        'product_id',
        'mode',
        'order_no',
        'apply_no',
        'policy_no',
        'done_at',
        'is_locked',
        'status',
        'error_num',
        'handle_num',
        'is_download',
        'insure_callback',
        'is_entry'
    ];

    public function getMessages($request)
    {
        return self::orderBy('id', 'desc')
            ->when($request->input('keywords'), function ($q, $keywords) {
                $q->where('order_no', 'like', $keywords . '%');
                $q->orWhere('apply_no', 'like', $keywords . '%');
                $q->orWhere('policy_no', 'like', $keywords . '%');
            })
            ->when($request->input('mode'), function ($q, $mode) {
                $q->where('mode',$mode);
            })
            ->when($request->input('status'), function ($q, $status) {
                $q->where('status','=',$status);
            })
            ->with(['platform', 'product'])
            ->paginate($request->input('page_size', 10));
    }

    public function getMessageById($id)
    {
        return self::with(['attaches', 'notifies'])->findOrFail($id);
    }

    public function getStatus()
    {
        switch ($this->attributes['status']) {
            case '0':
                return '未处理'; //报文尚未提交到保险公司
                break;
            case '1':
                return '已提交'; //已有提交到保险公司动作，但是不知道是否成功
                break;
            case '2':
                return '已完成'; //提交到保险公司，并返回了保单号
                break;
            case '-1':
                return '已作废'; //作废保单
                break;
        }
    }

    public function platform()
    {
        return $this->belongsTo(Platform::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function attaches()
    {
        return $this->hasOne(MessageAttach::class);
    }

    public function notifies()
    {
        return $this->hasMany(Notify::class);
    }

    public function count()
    {
        $data = [];
        $data['untreat'] =  self::where('status',0)
            ->count();
        $data['submit'] =  self::where('status',1)
            ->count();
        $mailDelivery = new MailDelivery();
        $data['mailToFail'] = $mailDelivery->where('status',3)->count();
        $data['mailInSend'] = $mailDelivery->where('status',1)->count();
        return $data;
    }

    public function jsonForMart()
    {
        return is_null(json_decode($this->attributes['insure_callback'])) ? $this->attributes['insure_callback'] : json_encode(json_decode($this->attributes['insure_callback']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    public function getDownloadStatus()
    {
        switch ($this->attributes['is_download']) {
            case '0':
                return '未下载';
                break;
            case '1':
                return '未知';
                break;
            case '2':
                return '已完成';
                break;
            default:
                return '未知';
                break;
        }
    }

}
