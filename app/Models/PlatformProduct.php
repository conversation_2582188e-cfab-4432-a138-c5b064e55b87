<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PlatformProduct extends Model
{
    protected $fillable = ['platform_id', 'product_id', 'status'];

    public function getStatus()
    {
        switch ($this->attributes['status']) {
            case '1':
                return '禁用';
                break;
            case '2':
                return '启用';
                break;
        }
        return '未知';
    }

    public function createProduct($platform_id,$data)
    {
        $new = [];
        for($i=0;$i<count($data['product']);$i++){
            $result = self::where([['platform_id','=',$platform_id],['product_id','=',$data['product'][$i]]])->first();
            if($result){
                unset($data['product'][$i]);
                continue;
            }
            array_push($new,array('platform_id'=>$platform_id,'product_id'=>$data['product'][$i],'created_at'=>date('Y-m-d H:i:s'),'updated_at'=>date('Y-m-d H:i:s')));
        }
        return self::insert($new);
    }

    public function getProductsByPlatformId($platform_id)
    {
        return self::where('platform_id',$platform_id)
            ->get();
    }


}
