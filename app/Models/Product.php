<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use SoftDeletes;

    protected $dates = ['deleted_at'];

    protected $fillable = ['product_code', 'title','account','mode','config','status'];

    public function getProducts($request)
    {
        return self::orderBy('id', 'desc')
            ->when($request->input('keywords'), function ($q, $keywords) {
                $q->where('title', 'like', $keywords . '%')
                ->orWhere('account',  'like',  '%'.$keywords . '%')
                ->orWhere('product_code','=',$keywords);
            })
            ->when($request->input('mode'), function ($q, $mode) {
                $q->where('mode',$mode);
            })
            ->with(['platformProduct'])
            ->paginate($request->input('page_size', 10));
    }

    public function getActiveProducts($request)
    {
        return self::orderBy('id', 'desc')
            ->when($request->input('mode'), function ($q, $keywords) {
                $q->where('mode',$keywords);
            })
            ->when($request->input('status'), function ($q, $keywords) {
                $q->where('status',$keywords);
            })
            ->with(['platformProduct'])
            ->paginate($request->input('page_size', 10));
    }

    public function getProductById($id){
        return self::findOrFail($id);
    }

    public function getStatus()
    {
        switch ($this->attributes['status']) {
            case '1':
                return '禁用';
                break;
            case '2':
                return '启用';
                break;
        }
    }

    public function getMode()
    {
        switch ($this->attributes['mode']) {
            case 'AUTO_PICC':
                return '人保自动';
                break;
            case 'AUTO_PINGAN':
                return '平安自动';
                break;
            case 'AUTO_PINGAN_INTL':
                return '平安自动(国际)';
                break;
            case 'AUTO_CPIC':
                return '太平洋自动(国内)';
                break;
            case 'AUTO_CPIC_INTL':
                return '太平洋自动(国际)';
                break;
            case 'AUTO_SUNSHINE':
                return '阳光自动';
                break;
            case 'AUTO_GUOREN':
                return '国任自动';
                break;
            case 'AUTO_BDYF':
                return '保代云服自动';
                break;
            case 'AUTO_TEST':
                return '测试产品';
                break;
        }
    }

    public function platformProduct()
    {
        return $this->hasMany(PlatformProduct::class,'product_id');
    }


}
