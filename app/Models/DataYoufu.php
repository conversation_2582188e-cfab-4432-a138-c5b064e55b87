<?php

namespace App\Models;

use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class DataYoufu extends Model
{
    protected $connection = 'mysql_yf';
    protected $table = "cs_ins_form as a";
    protected $primaryKey = 'policy_id';
    public $timestamps = false;

    public function index($field_name,$order_no){

        $data = self::select(
            'a.SerialNo',
            DB::raw('FROM_UNIXTIME(a.DateTime,"%Y-%m-%d %H:%i:%S") AS create_time'),
            'a.InsuredName as insured_name',
            'a.FromAddress as departure',
            'a.TransAddress as stopovers',
            'a.ToAddress as destination',
            DB::raw('FROM_UNIXTIME(a.StartDate,"%Y-%m-%d %H:%i:%S") AS start_time'),
            'a.CargoInfo as goods_name',
            'a.TransNum as vehicle_license_no',
            'a.Coverage as coverage',
            'b.type_name as species',
            'b.info_email as email',
            'b.deductible',
            'b.specials'
        )
            ->join('cs_ins_type as b','b.product_id','a.ProductCode')
            ->where('a.'.$field_name,$order_no)
            ->first();
        if($data){
            $data['email'] = explode(';',$data['email']);
            $data->goods_amount = '';
            $data->pack_type = '';
            $data->coverage = $data->coverage*10000;
        }
        return $data;
    }

    public function indexs($field_name,$time1,$time2){

        $data = self::select(
            'a.SerialNo',
            DB::raw('FROM_UNIXTIME(DateTime,"%Y-%m-%d %H:%i:%S") AS create_time'),
            'a.InsuredName as insured_name',
            'a.FromAddress as departure',
            'a.TransAddress as stopovers',
            'a.ToAddress as destination',
            DB::raw('FROM_UNIXTIME(a.StartDate,"%Y-%m-%d %H:%i:%S") AS start_time'),
            'a.CargoInfo as goods_name',
            'a.TransNum as vehicle_license_no',
            'a.Coverage as coverage',
            'b.type_name as species',
            'b.info_email as email',
            'b.deductible',
            'b.specials'
        )
            ->join('cs_ins_type as b','b.product_id','a.ProductCode')
            ->where('a.'.$field_name,'<',$time1)
            ->where('a.'.$field_name,'>',$time2)
            ->whereIn('a.Status',[0,1])
            ->get();
        if($data){
            foreach ($data as $k=>$v){
                $data[$k]->email = explode(';',$data[$k]->email);
                $data[$k]->goods_amount = '';
                $data[$k]->pack_type = '';
                $data[$k]->coverage = $data[$k]->coverage*10000;
            }
        }
        return $data;
    }
}
