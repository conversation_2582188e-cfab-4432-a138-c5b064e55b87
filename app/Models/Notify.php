<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Notify extends Model
{
    protected $fillable = ['message_id', 'content', 'url', 'done_at', 'callback', 'status','order_no', 'error_num'];

    public function getNotifies($request)
    {
        return self::orderBy('id', 'desc')
            ->when($request->input('keywords'), function ($q, $keywords) {
                $q->where('url', 'like', $keywords . '%')
                ->orWhere('order_no', $keywords);
            })
            ->with('message')
            ->paginate($request->input('page_size', 10));
    }

    public function getStatus()
    {
        switch ($this->attributes['status']) {
            case '0':
                return '未处理'; //平台提交后未生效
                break;
            case '1':
                return '待发送'; //保单已生效，但未发送到平台端
                break;
            case '2':
                return '已发送'; //已推送到平台端
                break;
            case '3':
                return '发送失败'; //未推送到平台端
                break;
            case '4':
                return '待发送'; //中意雇主待发送状态
                break;
        }
    }

    public function getNotify($id)
    {
        return self::findOrFail($id);
    }

    public function message()
    {
        return $this->belongsTo(Message::class);
    }
}
