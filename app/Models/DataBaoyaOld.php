<?php

namespace App\Models;

use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class DataBaoyaOld extends Model
{
    protected $connection = 'mysql_lby';
    protected $table = "ald_policy_cargo as a";
    public $timestamps = false;

    public function index($field_name,$order_no){

        $data = self::select(
            'a.sys_order_no',
            DB::raw('FROM_UNIXTIME(a.create_time,"%Y-%m-%d %H:%i:%S") AS create_time'),
            'a.insured_name',
            'a.departure as departure',
            'a.stopovers as stopovers',
            'a.destination as destination',
            DB::raw('FROM_UNIXTIME(a.start_time,"%Y-%m-%d %H:%i:%S") AS start_time'),
            'a.goods_name',
            'a.goods_amount',
            'a.pack_type',
            'a.car_license_no as vehicle_license_no',
            'a.coverage',
            'b.product_name as species',
            'b.info_email as email',
            'b.deductible',
            'b.specials'
        )
            ->join('ald_product_cargo as b','b.product_code','a.product_code')
            ->where('a.'.$field_name,$order_no)
            ->first();
        if($data){
            $data['email'] = explode(';',$data['email']);
        }
        return $data;
    }

    public function indexs($field_name,$time1,$time2){

        $data = self::select(
            'a.sys_order_no',
            'a.create_time',
            'a.insured_name',
            'a.departure as departure',
            'a.stopovers as stopovers',
            'a.destination as destination',
            DB::raw('FROM_UNIXTIME(start_time,"%Y-%m-%d %H:%i:%S") AS start_time'),
            'a.goods_name',
            'a.goods_amount',
            'a.pack_type',
            'a.car_license_no as vehicle_license_no',
            'a.coverage',
            'b.product_name as species',
            'b.info_email as email',
            'b.deductible',
            'b.specials'
        )
            ->join('ald_product_cargo as b','b.product_code','a.product_code')
            ->where('a.'.$field_name,'<',$time1)
            ->where('a.'.$field_name,'>',$time2)
            ->where('a.status',0)
            ->get();
        if($data){
            foreach ($data as $k=>$v){
                $data[$k]->email = explode(';',$data[$k]->email);
            }
        }
        return $data;
    }
}
