<?php

namespace App\Models;

use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class DataBaoyaNew extends Model
{
    protected $connection = 'mysql_by_new';
    protected $table = "policies as policy";
    public $timestamps = false;


    public function index($field, $order_no){

        $data = self::select(
            'policy.type',
            'policy.order_no as sys_order_no',
            'policy.insured as insured_name',
            'policy.coverage',
            'policy.created_at as create_time',

            'cargo.main_clause as species',
            'account.config->report_email_recipients as email',

            'cargo.departure as departure',
            'cargo.transmit as stopovers',
            'cargo.destination as destination',
            'cargo.shipping_date as start_time',
            'cargo.goods_name',
            'cargo.goods_amount',
            'pack.name as pack_type',
            'cargo.transport_no as vehicle_license_no',
            'cargo.deductible',
            'cargo.special as specials',
            'currency.rate as currency_rate'
            )
            ->join('policy_cargos as cargo', 'cargo.policy_id', 'policy.id')
            ->join('packing_methods as pack', 'pack.id', 'cargo.packing_method_id')
            ->join('products as product', 'product.id', 'policy.product_id')
            ->join('company_branch_accounts as account', 'account.id', 'product.company_branch_account_id')
            ->join('product_additionals as addition', 'addition.product_id', 'product.id')
            ->leftJoin('currencies as currency', 'currency.id', 'cargo.invoice_currency_id')
            ->where('policy.'.$field, $order_no)
            ->first();
        if($data){
            $data['email'] = explode(';', trim($data['email'], '"'));
            if($data['type'] === 2){
                $data['coverage'] = round(bcmul($data['coverage'], $data['currency_rate'], 5), 2);
            }
            $data['coverage'] = round(bcdiv($data['coverage'], 100, 5), 2);
        }
        return $data;
    }

    public function indexs($field, $time1, $time2){

        $data = self::select(
            'policy.order_no as sys_order_no',
            'policy.insured as insured_name',
            'policy.coverage',
            'policy.created_at as create_time',

            'cargo.main_clause as species',
            'account.config->report_email_recipients as email',

            'cargo.departure as departure',
            'cargo.transmit as stopovers',
            'cargo.destination as destination',
            'cargo.shipping_date as start_time',
            'cargo.goods_name',
            'cargo.goods_amount',
            'pack.name as pack_type',
            'cargo.transport_no as vehicle_license_no',
            'cargo.deductible',
            'cargo.special as specials'
        )
            ->join('policy_cargos as cargo', 'cargo.policy_id', 'policy.id')
            ->join('packing_methods as pack', 'pack.id', 'cargo.packing_method_id')
            ->join('products as product', 'product.id', 'policy.product_id')
            ->join('company_branch_accounts as account', 'account.id', 'product.company_branch_account_id')
            ->join('product_additionals as addition', 'addition.product_id', 'product.id')
            ->where('policy.'. $field, '<', $time1)
            ->where('policy.'. $field, '>', $time2)
            ->whereIn('policy.status', [1,2])
            ->get();
        if($data){
            foreach ($data as $k=>$v){
                $data[$k]->email = explode(';', $data[$k]->email);
            }
        }
        return $data;
    }
}
