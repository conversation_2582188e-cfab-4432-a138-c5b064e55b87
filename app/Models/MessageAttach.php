<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MessageAttach extends Model
{
    protected $fillable = ['message_id', 'source', 'content', 'callback'];

    public function jsonForMart()
    {
        if(is_null(json_decode($this->attributes['callback']))){
            return $this->attributes['callback'];
        }
        return json_encode(json_decode($this->attributes['callback']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
}
