<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MailDelivery extends Model
{
    protected $table = 'mail_delivery';
    public $timestamps = false;

    public function index($request,$status){
        return self::when($status, function ($query,$status) {
                $query->where('status',$status);
            })
            ->with([
                'platform:*'
            ])
            ->orderBy('id', 'desc')
            ->paginate($request->input('page_size', 10));
    }

    public function platform()
    {
        return $this->hasOne(Platform::class,'id','platform_id');
    }


    public function getStatus()
    {
        switch ($this->attributes['status']) {
            case '1':
                return '正在发送';
                break;
            case '2':
                return '已发送';
                break;
            case '3':
                return '发送失败';
                break;
        }
    }
}
