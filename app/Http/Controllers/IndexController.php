<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class IndexController extends Controller
{
    public function getData(){
        $formData = [
            'holderName' => '福建中保在线网络科技有限公司（ts）',
            'holderAddr' => '',
            'recognizeeAddr' => '',
            'documentType' => '其他',
            'recognizeeIdenty' => '',
            'recognizeePhone' => '',
            'recognizeeOrg' => '',
            'recognizeeName' => '测试',
            'invNo' => '测试'.time(),
            'goodsName' => '测试',
            'weights' => '',
            'goodsTypeID' => '624',
            'quantity' => '测试',
            'pack' => '',
            'transportTypeID' => '5',
            'transport' => '测试',
            'transportNo' => '',
            'fromLoc' => '测试',
            'viaLoc' => '',
            'toLoc' => '测试1',
            'departureDate' => '2019-09-14',
            'departureTime' => '21',
            'glausesID' => '6',
            'additive' => '',
            'insuredAmount' => '100',
            'ratio' => '0.100',
            'premium' => '0.01',
            'deductible' => '1.易碎品：每次事故绝对免赔为损失金额的20%或3000元，以高为准；2.普通货物：每次事故绝对免赔额为损失金额的10%或1000元，以高为准；3.火灾、爆炸：每次事故绝对免赔额为损失金额的20%，运输路线涉及新疆地区，火灾事故免赔额30%。',
            'effDate' => '2019-09-14',
            'effTime' => '21',
            'remark' => '1.装载货物的运输工具（车牌号）或运单号必须与保单中一致；2. 保险责任以保单启运时间为准，但对启运后提交的投保单或批改，若已经发生的保险事故，保险人均不负责赔偿；3.保险标的价值根据出险时的市场价值确定，投保人自行录入单价、货值不做为理赔依据；4.运输路线涉及西藏地区的货物，不在承保范围内；5.除物流公司及其法定代表人外，投保人不得将驾驶员、车主等对货物无保险利益的人员列为被保险人；6. 因违反安全运输的规定导致的保险事故，保险人有权拒绝承担赔偿责任。',
            'endTypeID' => '40',
            'postalModeId' => '57',
            'invHead' => '',
            'extUsrNo' => '',
            'shipCName' => '',
            'shipEName' => '',
            'fleetNo' => '',
            'itemNo' => '0',
            'stepHull' => '无船级',
            'shipFlag' => '',
            'associate' => '',
            'makeYearMonth' => '2019-09-14',
            'countryCode' => '',
            'makeFactory' => '',
            'templateDesc' => '',
            'consigneeInfo' => '',
            'neijian' => '',
            'contactPerson' => '',
            'contactTel' => '',
            'postalCode' => '',
            'postalAddr' => '',
            'glauses' => '国内水路、陆路货运基本险',
            'packQty' => '测试',
            'way' => '',
            'ifPackage' => '',
            'contractPerson' => '',
            'contractTel' => '',
            'postalCode' => '',
            'postalAddr' => '',
            'ratio' => '',
            'additiveNo' => '',
            'invRefNo' => '测试'.time(),
            'policyNo' => '',
            'policyNoLong' => '',
            'insuranceID' => '',
            'endCurrencyID' => '1',
            'currencyID' => '1',
            'policyNoRemark' => '',
            'changeNo' => '',
            'extUsrNo' => '',
            'mianID' => '',
            'mianDesc' => '',
            'contractNo' => '',
            'insuranceID' => '1',
            'inDelayApplay' => '',
            'agreenumStartDate' => '2018-08-27',
            'policyNoHead' => '61010500',
            'myfileFileName' => ''            
        ];
        
        exit(json_encode($formData));
    }
}
