<?php

namespace App\Http\Controllers\Workbench;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Notify;

class NotifiesController extends Controller
{
    protected $notifyModel;

    public function __construct(Notify $notifyModel)
    {
        $this->notifyModel = $notifyModel;
    }

    //通知列表
    public function index(Request $request)
    {
        $notifies = $this->notifyModel->getNotifies($request);
        return view('workbench.notifies.index', compact('notifies', 'request'));
    }

    //删除通知
    public function delete($id){
        $notify = $this->notifyModel->getNotifieById($id);
        $notify->delete();
        return back()->with('success', '通知删除成功');
    }

    //更新通知
    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'title' => 'required|max:255',
        ]);
        
        $data = $request->except('_token');
        $this->notifyModel->where('id', $id)->update($data);

        return back()->with('success', '通知更新成功');
    }

    public function reNotifyOne($id)
    {
        $notify = $this->notifyModel->getNotify($id);
        $status = 1;
        if($notify->message->mode == 'API_GROUP_ZY'){
            $status = 4;
        }
        $this->notifyModel->where('id',$id)->update(['status'=>$status, 'error_num' => 0]);
        return back()->with('success', '重新发送成功');
    }

    public function reNotify($messageId)
    {
        $this->notifyModel->where('message_id',$messageId)->update(['status'=>1, 'error_num' => 0]);
        return back()->with('success', '重新通知成功');
    }

    public function show($id)
    {
        $notify = $this->notifyModel->getNotify($id);
        return view('workbench.notifies.show',compact('notify'));
    }

    public function updateNotify($id, Request $request)
    {
        $this->validate($request, [
            'content' => 'required',
        ],[
            'content.required' => '内容不能为空',
        ]);
        $data = $request->only('content');
        $this->notifyModel->where('id',$id)->update(['content'=>$data['content']]);
        return back()->with('success', '修改成功');
    }
}
