<?php

namespace App\Http\Controllers\Workbench;

use App\Models\MessageAttach;
use App\Models\Notify;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Message;

class MessagesController extends Controller
{
    protected $messageModel;

    public function __construct(Message $messageModel)
    {
        $this->messageModel = $messageModel;
    }

    //报文列表
    public function index(Request $request)
    {
        $messages = $this->messageModel->getMessages($request);
        return view('workbench.messages.index', compact('messages', 'request'));
    }

    //报文详细
    public function show($id)
    {
        $message = $this->messageModel->getMessageById($id);
        return view('workbench.messages.show', compact('message'));
    }

    //删除报文
    public function delete($id){
        $message = $this->messageModel->getMessageById($id);
        $message->delete();
        return redirect('workbench/messages')->with('success', '报文删除成功');
    }

    //新建报文
    public function create(){
        $pageSetting = array(
            'action' => 'create',
            'name' => '添加',
            'url' => url('workbench/messages/store')
        );
        return view('workbench.messages.message_form', compact('pageSetting'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|max:255',
            'account' => 'required',
            'mode' => 'required',
            'status' => 'required',
        ],[],[
            'account' => '账户',
            'mode' => '投保方式',
            'status' => '状态'
        ]);

        if ($this->messageModel->where('account', '=', $request->account)->where('mode', '=', $request->mode)->exists()) {
            return back()->with('danger', '该报文已存在');
        }
        
        $data = $request->except('_token');
        $data['message_code'] = date('Ymd') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
        $this->messageModel->create($data);

        return redirect('workbench/messages')->with('success', '报文添加成功');
    }

    //复制物品
    public function duplicate($id)
    {
        $pageSetting = array(
            'action' => 'duplicate',
            'name' => '复制',
            'url' => url('workbench/messages/store')
        );
        $message = $this->messageModel->getMessageById($id);
        return view('workbench.messages.message_form', compact('message', 'pageSetting'));
    }

    //更新报文
    public function edit($id){
        $pageSetting = array(
            'action' => 'edit',
            'name' => '修改',
            'url' => url('workbench/messages/update', $id)
        );
        $message = $this->messageModel->getMessageById($id);
        return view('workbench.messages.message_form', compact('message', 'pageSetting'));
    }

    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'title' => 'required|max:255',
            'account' => 'required',
            'mode' => 'required',
            'status' => 'required',
        ],[],[
            'account' => '账户',
            'mode' => '投保方式',
            'status' => '状态'
        ]);
        
        $data = $request->except('_token');
        $this->messageModel->where('id', $id)->update($data);

        return back()->with('success', '报文更新成功');
    }

    public function resubmit($id, Notify $notify)
    {
        $this->messageModel->where('id',$id)->update(['status' => 0,'is_locked' => 0, 'is_entry' => 0]);
        $notify->where('message_id',$id)->update(['status'=>0, 'error_num' => 0]);
        return back()->with('success', '重新提交成功');
    }

    public function messageModify($message_id,Request $request, MessageAttach $messageAttach)
    {
        $this->validate($request, [
            'content' => 'required',
        ],[
            'content.required' => '内容不能为空',
        ]);
        $data = $request->only('content');
        $messageAttach->where('message_id',$message_id)->update(['content'=>$data['content']]);
        return back()->with('success', '修改成功');
    }

    public function drop($id)
    {
        $this->messageModel->where('id',$id)->update(['status'=> -1]);
        return back()->with('success', '作废成功');
    }

    public function enterSuccess($id, Request $request, Notify $notify)
    {
        $data = $this->validate($request, [
            'policy_no' => 'required',
        ],[
            'policy_no.required' => '保单号不能为空',
        ]);
        $data['status'] = 2;
        $this->messageModel->where('id',$id)->update($data);
        $notify->where('message_id',$id)->update(['status'=>1, 'error_num' => 0]);
        return back()->with('success', '修改成功');
    }

}
