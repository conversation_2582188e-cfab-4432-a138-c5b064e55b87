<?php

namespace App\Http\Controllers\Workbench;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\User;

class UsersController extends Controller
{
    protected $userModel;

    public function __construct(User $userModel)
    {
        $this->userModel = $userModel;
    }

    //管理员列表
    public function index(Request $request)
    {
        $users = $this->userModel->getUsers($request);
        return view('workbench.users.index', compact('users', 'request'));
    }

    //删除管理员
    public function delete($id){
        $user = $this->userModel->getUserById($id);
        $user->delete();
        return back()->with('success', '管理员删除成功');
    }

    //新建管理员
    public function store(Request $request)
    {
        $this->validate($request, [
            'username' => 'required|unique:users|max:255',
            'password' => 'required|string|min:6',
            'fullname' => 'required',
            'mobile' => 'required',
        ],[],[
            'fullname' => '姓名'
        ]);
        
        $data = $request->except('_token');
        $data['password'] = bcrypt($data['password']);
        $this->userModel->create($data);

        return back()->with('success', '管理员添加成功');
    }

    //更新管理员
    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'fullname' => 'required',
        ],[],[
            'fullname' => '姓名'
        ]);
        $data = $request->except('_token');
        if(!empty($data['password'])){
            $data['password'] = bcrypt($data['password']);
        }else{
            unset($data['password']);
        }
        $this->userModel->where('id', $id)->update($data);

        return back()->with('success', '管理员更新成功');
    }
}
