<?php

namespace App\Http\Controllers\Workbench;

use App\Models\PlatformProduct;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Platform;

class PlatformsController extends Controller
{
    protected $platformModel;

    public function __construct(Platform $platformModel)
    {
        $this->platformModel = $platformModel;
    }

    //管理员列表
    public function index(Request $request)
    {
        $platforms = $this->platformModel->getPlatforms($request);
        return view('workbench.platforms.index', compact('platforms', 'request'));
    }

    //删除管理员
    public function delete($id){
        $platform = $this->platformModel->getPlatformById($id);
        $platform->delete();
        return back()->with('success', '管理员删除成功');
    }

    //新建管理员
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|unique:platforms|max:255',
        ]);
        
        $data = $request->except('_token');
        $data['app_id'] = 'INS'.time();
        $data['secret_key'] = sha1($data['app_id'], FALSE);
        $data['status'] = 0;
        $this->platformModel->create($data);

        return back()->with('success', '管理员添加成功');
    }

    //更新管理员
    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'title' => 'required|max:255',
        ]);
        
        $data = $request->except('_token');
        $this->platformModel->where('id', $id)->update($data);

        return back()->with('success', '管理员更新成功');
    }

    public function distribution($platform_id, Product $product, Request $request)
    {
        $products = $product->getActiveProducts($request);
        return view('workbench.platforms.distribution',compact('products','platform_id'));
    }

    public function doDistribution($platform_id,$product_id,PlatformProduct $platformProduct)
    {
        $result = $platformProduct->where([['platform_id','=',$platform_id],['product_id','=',$product_id]])->first();
        if($result){
            return back()->with('danger','该产品已分配至本平台,请勿重复分配');
        }else{
            $platformProduct->create(['platform_id'=>$platform_id,'product_id'=>$product_id]);
            return back()->with('success','产品分配成功');
        }
    }

    public function cancel($platform_id,$product_id,PlatformProduct $platformProduct)
    {
        $platformProduct->where([['platform_id','=',$platform_id],['product_id','=',$product_id]])->delete();
        return back()->with('success','已取消该产品');
    }
}
