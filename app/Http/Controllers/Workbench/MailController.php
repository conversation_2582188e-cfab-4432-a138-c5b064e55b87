<?php

namespace App\Http\Controllers\Workbench;

use App\Models\MailDelivery;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class MailController extends Controller
{

    //邮件发送列表
    public function index(Request $request, MailDelivery $mailDelivery)
    {
        $status =  $request->status;
        $mails =  $mailDelivery->index($request,$status);
        return view('workbench.mail.index',[
            'mails'=>$mails,
            'status'=>$status
        ]);
    }
}
