<?php

namespace App\Http\Controllers\Workbench;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Product;

class ProductsController extends Controller
{
    protected $productModel;

    public function __construct(Product $productModel)
    {
        $this->productModel = $productModel;
    }

    //产品列表
    public function index(Request $request)
    {
        $products = $this->productModel->getProducts($request);
        return view('workbench.products.index', compact('products', 'request'));
    }

    //删除产品
    public function delete($id){
        $product = $this->productModel->getProductById($id);
        $product->delete();
        return redirect('workbench/products')->with('success', '产品删除成功');
    }

    //新建产品
    public function create(){
        $pageSetting = array(
            'action' => 'create',
            'name' => '添加',
            'url' => url('workbench/products/store')
        );
        return view('workbench.products.product_form', compact('pageSetting'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required|max:255',
            'account' => 'required',
            'mode' => 'required',
            'status' => 'required',
        ],[],[
            'account' => '账户',
            'mode' => '投保方式',
            'status' => '状态'
        ]);

        if ($this->productModel->where('account', '=', $request->account)->where('mode', '=', $request->mode)->exists()) {
            return back()->with('warning', '该产品已存在');
        }
        
        $data = $request->except('_token');
        $data['product_code'] = date('Ymd') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
        $this->productModel->create($data);

        return redirect('workbench/products')->with('success', '产品添加成功');
    }

    //复制物品
    public function duplicate($id)
    {
        $pageSetting = array(
            'action' => 'duplicate',
            'name' => '复制',
            'url' => url('workbench/products/store')
        );
        $product = $this->productModel->getProductById($id);
        return view('workbench.products.product_form', compact('product', 'pageSetting'));
    }

    //更新产品
    public function edit($id){
        $pageSetting = array(
            'action' => 'edit',
            'name' => '修改',
            'url' => url('workbench/products/update', $id)
        );
        $product = $this->productModel->getProductById($id);
        return view('workbench.products.product_form', compact('product', 'pageSetting'));
    }

    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'title' => 'required|max:255',
            'account' => 'required',
            'mode' => 'required',
            'status' => 'required',
        ],[],[
            'account' => '账户',
            'mode' => '投保方式',
            'status' => '状态'
        ]);
        
        $data = $request->except('_token');
        $this->productModel->where('id', $id)->update($data);

        return back()->with('success', '产品更新成功');
    }
}
