<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CPICIntlController extends Controller
{
    public function approval($policy, $product, $setting, $platform)
    {

        if($platform['id'] == '6'){
            $messageData = $this->getYunJiData($policy, $setting);
        }
        if($platform['id'] == '5'){
            $messageData = $this->getBaoyaData($policy, $setting);
        }

        $data = array(
            "config" => array(
                "account" => $setting[0],
                "password" => $setting[1]
            ),
            "message" => array(
                "insCommonInfoDto" => array(
                    "publicPolicyId"=> "",
                    "procotolNo"=> $messageData['procotolNo'], //预保协议号
                    "isProtocol"=> "0",     //是否按协议条件投保 0为否 1为是
                    "applyNo"=> "",
                    "policyNo"=> "",
                    "applyEndorseNo"=> "",
                    "endorseNo"=> "",
                    "status"=> "",
                    "classesType"=> "2",    //投保类型 1为国内 2为进出口
                    "classesCode"=> $messageData['classesCode'],     //投保类型代码 出口=>******** 进口=>********
                    "unitCode"=> $messageData['unitCode'],     //与账号绑定
                    "deptGroupCode"=> "",
                    "effectDate"=> $messageData['effectDate'],    // 签单日期
                    "userNo"=> "",  //自定义查询编码
                    "applyDate"=> "",
                    "operateDate"=> "",
                    "comments"=> "",    // 投保说明
                    "flightAreaCode"=> $messageData['flightAreaCode'],  //目的港国家
                    "claimAgent"=> "",     //理赔代理地编号
                    "claimAgentCode"=> "",  //理赔代理地编码
                    "creditNo"=> "",    //信用证号
                    "priceCond"=> "3",  //价格条件  默认为3不做修改
                    "invAmount"=> $messageData['invAmount'],    //发票金额
                    "incRate"=> $messageData['incRate'],   //加成比例
                    "claimCurrencyCode"=> $messageData['currencyCode'],     //赔偿币种 (与投保币种相同)
                    "claimPayPlace"=> $messageData['claimPayPlace']  //赔款地点
                ),
                "applicationInfoDto" => array(
                    "name"=> $messageData['holderName']
                ),
                "insurantInfoDto" => array(
                    "name"=> $messageData['recognizeeName']
                ),
                "cargoInfoDto" => array(
                    "mark"=> $messageData['mark'],  //标记,发票号码,运单号
                    "quantity"=> $messageData['quantity'], //包装及数量
                    "cargoName"=> $messageData['cargoName'], //货物明细
                    "packCode"=> $messageData['packCode'], //包装方式
                    "cargoType"=> $messageData['cargoType'], //货物类型
                    "kindName"=> $messageData['kindName'], // 运输工具名称
                    "voyNo"=> $messageData['voyNo'], //航次
                    "kind"=> $messageData['kind'], //运输方式
                    "startPort"=> $messageData['startPort'], //起运地
                    "endPort"=> $messageData['endPort'], //目的地
                    "transPort"=> $messageData['transPort'], //中转地
                    "sailDate"=> $messageData['sailDate'] //起运日期
                ),
                "clauseInfoDto" => array(
                    "mainItemCode"=> $messageData['mainItemCode'],  //条款代码
                    "mainItemName"=> $messageData['mainItemName'],  //条款名称
                    "mainItemContent"=> $messageData['mainItemContent'], //条款内容  将运吉宝系统条款内容直接放在此处
                    "addItemCode"=> "", // 附加险代码
                    "addItemName"=> "" //附加险名称
                ),
                "feeInfoDto" => array(
                    "currencyCode"=> $messageData['currencyCode'], // 投保币种
                    "amount"=> $messageData['amount'], // 保额
                    "premium"=> $messageData['premium'], //保费
                    "rate"=> $messageData['rate'] / 10, //费率
                    "limitAmount"=> $messageData['limitAmount'],  //最高保额
                    "minRate"=> $messageData['minRate'], //最低费率
                    "exchange"=> "", // 汇率
                    "fCurrencyCode"=> $messageData['currencyCode'] //保费币种 与投保币种相同
                ),
                "specialtermInfoDto" => array(
                    "specialize" => "" //特别约定
                ),
                "dectInfoDto" => array(
                    "franchise"=>"other terms & conditions are equivalent to the updated Open Policy." // 免赔
                ),
                "extendedInfoDto" => array(
                    "billType"=> "0", //发票类型 0=>普票 1=>专票
                    "bussLicence"=> $messageData['bussLicence'], // 投保人纳税人识别号
                    "invHead"=> $messageData['invHead'], // 开票抬头
                    "invHeadLicence"=> $messageData['invHeadLicence'], //开票人纳税人识别号
                    "tel"=> "" // 手机号
                ),
                "sellInfoDTO" => array(  // 销售或代理信息 默认不变
                    "salerType1"=> "1",
                    "salerName1"=> "",
                    "salerCert1"=> "",
                    "salerType2"=> "",
                    "salerName2"=> "",
                    "salerCert2"=> "",
                    "salerType3"=> "",
                    "salerName3"=> "",
                    "salerCert3"=> ""
                ),
            ),
            "settings"=>$setting
        );

        foreach ($data['message']['cargoInfoDto'] as &$val) {
            $val = str_replace(array("\t"), " ", $val);
            $val = str_replace("\r\n", "\\n", $val);
        }

        return $data;
    }

    public function getYunJiData($policy, $setting)
    {
        $procotolNo = array(
            'SHGGHY' => 'A|3010100|*********|0',
            'QLJJ' => 'A|3080100|*********-19001P000067|COPXIM190024',
            'SHGH' => 'A|5010100|C20190382P000140|COPGUZ190302',
        );

        $classesCode = array(
            'import' => '********', //进口
            'export' => '********',  //出口
            'overseas' => '12040300',  //境外
        );

        $unitCode = array(
            'SHGGHY' => '3010100',
            'QLJJ' => '3080100',
            'SHGH' => '5010100',
        );

        $rateArr = array(  //费率信息  费率 最高保额 最低保费
            'SHGGHY' => ['0.12','5000000','5'],
            'QLJJ' => ['0.2','1000000','5'],
            'SHGH' => ['0.12','3000000','5'],
        );

        $minRate = $rateArr[$setting[0]][0];
        $limitAmount = $rateArr[$setting[0]][1];
        $minPremium = $rateArr[$setting[0]][2];

        if($policy['rate'] != ''){
            $rate = $policy['rate'];
        }else{
            $rate = $minRate;
        }
        $premium = ($policy['amount'] * $rate) / 1000;
        if($premium < $minPremium){
            $premium = $minPremium;
        }

        $currencyCode = array(
            '1' => '01',
            '2' => '02',
            '3' => '07',
            '4' => '03',
            '5' => '04',
            '6' => '12',
            '7' => '06',
            '8' => '09',
            '9' => '08',
            '10' => '22',
            '11' => '14',
            '12' => '33',
            '13' => '16',
            '15' => '25',
        );

        $kind = array(
            '2' => '1',
            '1' => '1',
            '3' => '5',
            '5' => '3',
        );

//        $cargoTypeArr = array(
//            'ZXTBBSNS00020004' => '0702',
//            'ZXTBBSNS00020008' => '1602',
//            'ZXTBBSNS00020010' => '0510',
//            'ZXTBBSNS00020011' => '0913',
//            'ZXTBBSNS00020012' => '0309',
//            'ZXTBBSNS00020013' => '0405',
//            'ZXTBBSNS00020014' => '0309',
//            'ZXTBBSNS00020015' => '1106',
//            'ZXTBBSNS00020016' => '0309',
//            'ZXTBBSNS00020017' => '0301',
//            'ZXTBBSNS00020018' => '0504',
//            'ZXTBBSNS00020019' => '0513',
//            'ZXTBBSNS00020020' => '0702',
//        );
//        $cargoType = $cargoTypeArr[$policy['goods_type']];
//        $comments = '';
//        if ($cargoType == '') {
//            $cargoType = '0309';
//            $comments = 'Special goods:'.$policy['comments'];
//        }

        //主险
        $mainInsuranceCodes = array(
            '8'  => ['01ALL ','海运一切险'], //海运一切险
            '9'  => ['01WPA','水渍险'], //水渍险
            '10' => ['01FPA','平安险'], //平安险
            '11' => ['11A ','ICC（A）'], // ICC（A）
            '12' => ['11B','ICC（B）'], //ICC（B）
            '13' => ['11C','ICC（C）'],  //ICC（C）
            '14' => ['01FROZ','海运冷藏险'],  //海运冷藏险
            '15' => ['01FALL','海运冷藏一切险'], //海运冷藏一切险
            '1'  => ['03ALL','空运一切险'], //空运一切险
            '2'  => ['03AIR','空运险'],  //空运险
            '19' => ['02ALL','陆运一切险'], //陆运一切险
            '20' => ['02OVE','陆运险'],  //陆运险
            '5'  => ['02FOVE','陆运冷藏险'],  //陆运冷藏险
            '7'  => ['01ALL ','海运一切险'], //（保险公司系统内无此条款）
            '21' => ['01ALL ','海运一切险'] //（保险公司系统内无此条款
        );

        $mainClauseArr = array(
            '01ALL '=>'海运一切险',
            '01WPA'=>'水渍险',
            '01FPA'=>'平安险',
            '11A '=>'ICC（A）',
            '11B'=>'ICC（B）',
            '11C'=>'ICC（C）',
            '01FROZ'=>'海运冷藏险',
            '01FALL'=>'海运冷藏一切险',
            '03ALL'=>'空运一切险',
            '03AIR'=>'空运险',
            '02ALL'=>'陆运一切险',
            '02OVE'=>'陆运险',
            '02FOVE'=>'陆运冷藏险',
        );
//        $mainItem = $mainInsuranceCodes[$policy['main_insurance']];

//        $tsbz = explode('|', $policy['is_tsbz']);
//        $franchise = '';
//        $__franchise = '';
//        if ($tsbz[0] == 1) {
//            $franchise .= 'deductible for fragile goods:5% of the sum insured of fragile goods each and every loss;';
//        }
//        if ($tsbz[1] == 1) {
//            $__franchise .= '目的地（目的港）无法检测维修;';
//        }
//        if ($tsbz[2] == 1) {
//            $__franchise .= '特殊运输要求（如防震、防尘、防倾斜等）;';
//        }
//        if ($tsbz[3] == 1) {
//            $franchise .= 'UNPACKED CARGO EXCL.RUSTING,SCRAPING;';
//        }
//        if ($tsbz[4] == 1) {
//            $franchise .= 'Deductible:1 % OF THE SUM INSURED EACH;';
//        }
//
//        if ($policy['transportation_type'] == 2) {
//            $__franchise .= '非集装箱水运;';
//        }

        $startDate = date('Y-m-d H',$policy['departureDate']);
        $effectDate = date("Y-m-d", strtotime("-1 day", strtotime($startDate)));

        $endCountry = explode('-', $policy['toCountry']);

        $data = array(
            'procotolNo' => $procotolNo[$setting[0]],                  //预保协议号
            'classesCode' => $classesCode[$policy['insureType']],          //投保类型 进口 出口 境外
            'unitCode' => $unitCode[$setting[0]],                      //唯一编码 与协议绑定

            'flightAreaCode' => $endCountry[0],                                     //目的港国家

            //货物信息
            'mark' => $policy['mark'],                                           //标记,发票号码,运单号
            'quantity' => $policy['quantity'],                                  //包装及数量
            'cargoName' => $policy['goodsName'],                                   //货物明细
            'packCode' => $policy['packType'],                                                //包装方式
            'cargoType' => $policy['goodsType'],                                              //货物类型
            'kindName' => $policy['transportNo'],                                      //运输工具名称
            'voyNo' => $policy['transportNo'],                                         //航次
            'kind' => $policy['transportType'],                        //运输方式
            "startPort"=> $policy['fromLoc'],                                 //起运地
            "transPort"=> $policy['viaLoc'],                                  //中转地
            "endPort"=> $policy['toLoc'],                                //目的地
            'effectDate' => $effectDate,                                            //签单日期
            'sailDate' => $startDate,                                               //起运日期

            //条款信息
            "mainItemCode"=> $policy['mainClause'],                                          //条款代码
            "mainItemName"=> $mainClauseArr[$policy['mainClause']],                                          //条款名称
            "mainItemContent"=> $policy['clause'],                                  //条款内容
            'comments' => $policy['comments'],                                                //投保说明
            //保险信息
            'invAmount' => $policy['invAmount'],                                 //发票金额
            'amount' => $policy['amount'],                                        //保额
            'rate' => $rate,                                                        //费率
            'premium' => round($premium, 2),                                                  //保费
            'minRate' => $minRate,                                                        //最低费率
            'limitAmount' => $limitAmount,                                          //最高保额
            'currencyCode' => $policy['invCurrency'],                //发票币种
            'incRate' => $policy['incRate'],                     //加成比例
            'claimPayPlace' => $policy['compensateLoc'],                          //赔款地点

            'bussLicence'	  => '91310230MA1JTG0AXD',                              //投保人纳税人识别号
            'invHead'		  => '上海广晖货运代理有限公司',                          //开票抬头
            'invHeadLicence'  => '91310230MA1JTG0AXD',                              //开票人纳税人识别号
        );
        if($policy['isInvoice'] == 1){
            $data['bussLicence'] = '91310230MA1JTG0AXD';
            $data['invHead'] = $policy['invHead'];
            $data['invHeadLicence'] = $policy['invHeadLicence'];
            $data['rate'] = $policy['rate'] * 1000;
        }

        return $data;
    }

    public function getBaoyaData($policy, $setting)
    {
        $procotolNo = array(
            'SHGGHY' => 'A|3010100|*********|0',
            'QLJJ' => 'A|3080100|*********-19001P000067|COPXIM190024',
            'SHGH' => 'A|5010100|C20190382P000140|COPGUZ190302',
            'QLBX2' => 'A|3010100|*********-21001|0',
            'CAGWUX210005' => 'A|3020300|C20210014P000012|CAGWUX210005',
        );

        $classesCode = array(
            'import' => '********', //进口
            'export' => '********',  //出口
            'overseas' => '12040300',  //境外
        );

        $unitCode = array(
            'SHGGHY' => '3010100',
            'QLJJ' => '3080100',
            'SHGH' => '5010100',
            'QLBX2' => '3010100',
            'CAGWUX210005' => '3020300',
        );

        $rateArr = array(  //费率信息  费率 最高保额 最低保费
            'SHGGHY' => ['0.12','5000000','5'],
            'QLJJ' => ['0.2','1000000','5'],
            'SHGH' => ['0.12','3000000','5'],
            'QLBX2' => ['0.3','5000000','5'],
            'CAGWUX210005' => ['0.2','5000000','5'],
        );

        $minRate = $rateArr[$setting[0]][0];
        $limitAmount = $rateArr[$setting[0]][1];
        $minPremium = $rateArr[$setting[0]][2];

        if($policy['rate'] != ''){
            $rate = $policy['rate'];
        }else{
            $rate = $minRate;
        }
        $premium = ($policy['amount'] * $rate) / 10000;
        if($premium < $minPremium){
            $premium = $minPremium;
        }

        //主险
        $mainInsuranceCodes = array(
            '3'  => ['01ALL ','海运一切险'], //海运一切险
            '4'  => ['01ALL ','海运一切险'], //海运一切险
            '12'  => ['01ALL ','海运一切险'], //海运一切险
            '5'  => ['01WPA','水渍险'], //水渍险
            '13'  => ['01WPA','水渍险'], //水渍险
            '6' => ['01FPA','平安险'], //平安险
            '14' => ['01FPA','平安险'], //平安险
            '7' => ['11A ','ICC（A）'], // ICC（A）
            '15' => ['11A ','ICC（A）'], // ICC（A）
            '8' => ['11B','ICC（B）'], //ICC（B）
            '16' => ['11B','ICC（B）'], //ICC（B）
            '9' => ['11C','ICC（C）'],  //ICC（C）
            '17' => ['11C','ICC（C）'],  //ICC（C）
            '10' => ['01FROZ','海运冷藏险'],  //海运冷藏险
            '11' => ['01FALL','海运冷藏一切险'], //海运冷藏一切险
            '1'  => ['03ALL','空运一切险'], //空运一切险
            '2'  => ['03AIR','空运险'],  //空运险
            '18' => ['02ALL','陆运一切险'], //陆运一切险
            '19' => ['02OVE','陆运险'],  //陆运险
            '20'  => ['02FOVE','陆运冷藏险'],  //陆运冷藏险
        );

        $currencyCodeArr = array(
            '美元' => '02',
            '人民币' => '01',
            '英镑' => '06',
            '日元' => '04',
            '港币' => '03',
            '欧元' => '07',
            '澳大利亚元' => '12',
            '加拿大元' => '09',
            '新加坡元' => '08',
            '马来西亚林吉特币' => '22',
            '瑞士法郎' => '14',
            '韩元' => '33',
            '瑞典克朗' => '16',
            '新西兰元' => '25',
        );

        $startDate = date('Y-m-d',$policy['departureDate']);
        $effectDate = date("Y-m-d", strtotime("-1 day", strtotime($startDate)));
        $endCountry = explode('-', $policy['toCountry']);

        $transportTypeArr = array(
            '水路运输' => '1',
            '航空运输' => '5',
            '公路运输' => '4',
            '铁路运输' => '3',
        );

        $goodsTypeSmallArr = array(
            '纺织原料及纺织制品'=> '0405',
            '机器设备及其零件、附件'=> '0702',
            '纸质品'=> '0309',
            '贱金属及其制品、五金类'=> '0513',
            '食品'=> '0216',
            '化学工业及其相关工业产品'=> '0912',
            '塑料及其制品;橡胶及其制品'=> '0309',
            '木制品、木材'=> '1106',
            '仪器、乐器、医疗设备及零件、附件（非精密仪器）'=> '0701',
            '杂项制品'=> '0309',
            '电脑/平板电脑、手机等电子产品'=> '0806',

            '水果'=> '0216',
            '蔬菜'=> '0216',
            '鲜花'=> '0216',
            '其他鲜活'=> '0216',

            '玻璃及玻璃制品'=> '0301',
            '大理石、瓷砖、石材及其制品'=> '0301',
            '陶瓷制品'=> '0301',
            '太阳能电池板'=> '0301',
            '其他易碎品'=> '0301',

            '新车'=> '0707',
            '二手车'=> '0707',

            '冷藏食品、农副产品'=> '0216',
            '冷藏水产品'=> '0216',
            '其他冷藏品'=> '0216',

            '9类危险品'=> '0912',
            '8类危险品'=> '0913',
            '6类危险品'=> '0913',
            '5类危险品'=> '0912',
            '4类危险品'=> '0912',
            '3类危险品'=> '0913',
            '2类危险品'=> '0914',

            '煤、炭'=> '0507',
            '矿石、矿粉、矿砂等'=> '0505',
            '其他矿产资源类'=> '0510',

            '对运输有防震动、防倾斜、防尘等特殊要求的仪器'=> '0809',
            '目的地国家无法维修的仪器'=> '0809',
            '单件货物保额超过RMB200万元的仪器'=> '0809',
        );

        $packTypeArr = array(
            '裸装' => '05',
            '散装' => '04',
            '纸箱' => '01',
            '木箱' => '01',
            '捆包' => '08',
            '袋装' => '02',
            '篓装' => '01',
            '托盘' => '03',
            '桶装' => '06',
            '罐装' => '07',
        );

        $data = array(
            // 客户信息
            'holderName' => $policy['holderName'],
            'recognizeeName' => $policy['recognizeeName'],

            'procotolNo' => $procotolNo[$setting[0]],                  //预保协议号
            'classesCode' => $classesCode[$policy['insureType']],          //投保类型 进口 出口 境外
            'unitCode' => $unitCode[$setting[0]],                      //唯一编码 与协议绑定

            'flightAreaCode' => $endCountry[0],                                     //目的港国家

            //货物信息
            'mark' => $policy['shippingMark']. "\\n".$policy['invoiceNo']. "\\n".$policy['landBillNo'],                                           //标记,发票号码,运单号
            'quantity' => $policy['goodsAmount'],                                  //包装及数量
            'cargoName' => $policy['goodsName'],                                   //货物明细
            'packCode' => $packTypeArr[$policy['packType']],                                                //包装方式
            'cargoType' => $goodsTypeSmallArr[$policy['goodsType']],                                              //货物类型
            'kindName' => $policy['transportNo'],                                      //运输工具名称
            'voyNo' => $policy['transportNo'],                                         //航次
            'kind' => $transportTypeArr[$policy['transportType']],                        //运输方式
            "startPort"=> $policy['fromLoc'],                                 //起运地
            "transPort"=> $policy['viaLoc'],                                  //中转地
            "endPort"=> $policy['toLoc'],                                //目的地
            'effectDate' => $effectDate,                                            //签单日期
            'sailDate' => $startDate,                                               //起运日期

            //条款信息
            "mainItemCode"=> $mainInsuranceCodes[$policy['mainClause']][0],                                          //条款代码
            "mainItemName"=> $mainInsuranceCodes[$policy['mainClause']][1],                                          //条款名称
            "mainItemContent"=> $policy['clause'],                                  //条款内容
            'comments' => $policy['comments'],                                                //投保说明
            //保险信息
            'invAmount' => $policy['invAmount'],                                 //发票金额
            'amount' => $policy['amount'],                                        //保额
            'rate' => $rate,                                                        //费率
            'premium' => round($premium, 2),                                                  //保费
            'minRate' => $minRate,                                                  //最低费率
            'limitAmount' => $limitAmount,                                          //最高保额
            'currencyCode' => $currencyCodeArr[$policy['invCurrency']],                //发票币种
            'incRate' => $policy['incRate'],                     //加成比例
            'claimPayPlace' => $policy['payLoc'],                          //赔款地点

            'bussLicence'	  => '',                              //投保人纳税人识别号
            'invHead'		  => '',                          //开票抬头
            'invHeadLicence'  => '',                              //开票人纳税人识别号
        );
        if($policy['isInvoice'] == 1){
            $data['bussLicence'] = '91310230MA1JTG0AXD';
            $data['invHead'] = $policy['invHead'];
            $data['invHeadLicence'] = $policy['invHeadLicence'];
            $data['rate'] = $policy['rate'] * 1000;
        }

        return $data;
    }

    public function source()
    {
        $data = array(
            'insureType' => '投保类型',
            'isInvoice' => '是否开发票',
            'mark' => '唛头',
            'quantity' => '包装及数量',
            'goodsName' => '货物明细',
            'goodsType' => '货物类型',
            'packType' => '包装方式',
            'transportNo' => '运输工具名称',
            'transportType' => '运输方式',
            'fromCountry' => '起运国家',
            'toCountry' => '目的地国家',
            'fromLoc' => '起运地(港)',
            'viaLoc' => '中转地(港)',
            'toLoc' => '目的地(港)',
            'departureDate' => '起运日期 (时间戳)',
            'mainClause' => '主险',
            'clause' => '条款内容',
            'comments' => '特殊需求说明',
            'invAmount' => '发票金额',
            'amount' => '保额',
            'rate' => '费率',
            'premium' => '保费',
            'invCurrency' => '发票币种',
            'incRate' => '加成比例',
            'compensateLoc' => '赔付地',
            'invHead' => '发票抬头',
            'invHeadLicence' => '开票人纳税人识别号',
        );
    }
}
