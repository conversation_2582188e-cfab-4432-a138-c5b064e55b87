<?php

namespace App\Http\Controllers\Api;

class CPICController
{

    public function approval($policy, $product, $settings)
    {
        $setting = explode("|#|", $settings[0]);
        $startDate  = date('Y-m-d', $policy['departureDate']);
        $DateMinute = '00';

        $effDate   = date('Y-m-d');
        $effHours  = date('H');
        if (strtotime($startDate) <= strtotime(date('Y-m-d'))) {
            $startDate  = $effDate;
            $DateHours  = $effHours + 1;
            if (date('i') > 50) {
                $DateHours += 1;
            }
            if ($DateHours >= '24') {
                $startDate = date('Y-m-d', strtotime("$effDate+1day"));
                $DateHours = '00';
            }
            if ($DateHours < 10) {
                $DateHours = '0' . $DateHours;
            }
        } else {
            $DateHours  = '00';
        }

        $messageData = $this->getMessage($policy, $setting, $settings);

        $data = array(
            "config" => array(
                "account" => $setting[0],
                "password" => $setting[1]
            ),
            "message" => array(
                "insCommonInfoDto" => array(
                    "publicPolicyId" => "",
                    "procotolNo" => $messageData['procotolNo'],  # 预保协议号 [固定]
                    "isProtocol" => "1",  # 按协议条件投保 [固定]
                    "applyNo" => "",
                    "policyNo" => "",
                    "applyEndorseNo" => "",
                    "endorseNo" => "",
                    "status" => "",
                    "classesType" => "1",  # 按协议条件投保 [固定]
                    "classesCode" => "********",  # 险种 [固定]
                    "unitCode" => $messageData['unitCode'],  # [固定]
                    "deptGroupCode" => "",
                    "effectDate" => $startDate,  # 签单日期 [计算为当前日期]
                    "userNo" => "",
                    "applyDate" => "",
                    "operateDate" => "",
                    "comments" => $messageData['comments'],  # 备注 [固定]
                ),
                "applicationInfoDto" => array(
                    "name" => $messageData['holderName']  # 投保人
                ),
                "insurantInfoDto" => array(
                    "name" => $messageData['recognizeeName']  # 被保人
                ),
                "cargoInfoDto" => array(
                    "mark" => $messageData['mark'],  # 发票号
                    "quantity" => $messageData['quantity'],  # 包装和数量
                    "cargoName" => $messageData['cargoName'],  # 货物明细
                    "packCode" => $messageData['packCode'],  # 包装
                    "cargoType" => $messageData['cargoType'],  # 货物类型
                    "kindName" => $messageData['kindName'],  # 运输工具
                    "voyNo" => $messageData['voyNo'],  # 航次/车牌号
                    "kind" => $messageData['kind'],  # 运输方式
                    "startPort" => $messageData['startPort'],  # 运输路线：开始地
                    "endPort" => $messageData['endPort'],  # 运输路线：目的地
                    "transPort" => $messageData['transPort'],  # 运输路线：途径
                    "sailDate" => $startDate,  # 起运日期：年月日
                    "sailDateHours" => $DateHours,  # 起运日期：时
                    "saliDateMinute" => $DateMinute  # 起运日期：分
                ),
                "clauseInfoDto" => array(  # 主条款
                    "mainItemCode" => $messageData['mainItemCode'],  # settings[2][6]
                    "mainItemName" => $messageData['mainItemName']  # settings[2][7]
                ),
                "feeInfoDto" => array(
                    "currencyCode" => "01",  # 投保币种 [固定]
                    "amount" => $messageData['amount'],  # 保险金额 message.amount
                    "premium" => round($messageData['premium'], 2),  # 保费 message.amount * (0.150 / 1000)
                    "rate" => $messageData['rate'],  # 费率 settings[2][5]
                    "limitAmount" => $messageData['limitAmount'],  # [固定]
                    "minRate" => $messageData['minRate'],  # [固定]
                    "exchange" => "1"  # [固定]
                ),
                "specialtermInfoDto" => array(  #
                    "specialize" => $messageData['specialize']
                ),
                "dectInfoDto" => array(  # 固定
                    "franchise" => $messageData['franchise']
                ),
                "extendedInfoDto" => array(
                    "cpxh" => "",
                    "carvinCode" => "",
                    "policy_type" => "",
                    "billType" => $messageData['billType'],
                    "bussLicence" => $messageData['bussLicence'],
                    "invHead" => $messageData['invHead'],
                    "invHeadLicence" => $messageData['invHeadLicence'],
                    "tel" => ''
                ),
                "sellInfoDTO" => array(
                    "salerType1" => "",
                    "salerName1" => "",
                    "salerCert1" => "",
                    "salerType2" => "",
                    "salerName2" => "",
                    "salerCert2" => "",
                    "salerType3" => "",
                    "salerName3" => "",
                    "salerCert3" => ""
                )
            ),
            "settings" => $setting
        );

        if ($setting[0] == 'CDYF') {
            $data['message']['tCargoCoverage'] = json_decode($messageData['tCargoCoverage'], true);
            $data['message']['extendedInfoDto']['item19'] = '0,1,2,3';
//            $data['config'] = [
//                "account" => 'CDYF|074659EC5C6E885CA1B4DE199B9A0D9604EEFDC02F80D3B6DE02B7AAB02C19BBC4349F96D23473D445701DA29EBA395E',
//                "password" => 'Youfu@888|45391EDA92C277103A211856675325309EED9C4D3BB312A1C0ECFB1604F852449992F32AE9C3CD8DE133AFAE29944137'
//            ];
        }

        foreach ($data['message']['cargoInfoDto'] as &$val) {
            $val = str_replace(array("\t"), " ", $val);
            $val = str_replace("\r\n", "\\n", $val);
        }
        foreach ($data['message']['dectInfoDto'] as &$val) {
            $val = str_replace(array("\t"), " ", $val);
            $val = str_replace("\r\n", "\\n", $val);
        }

        return $data;
    }

    public function getMessage($policy, $setting, $settings)
    {
        //        0707^成品车^二手车^05^裸装^0.23‰^JB^国内水陆路基本险^^ASCN
        $insSetting = explode('^', $setting[2]);
        $cargoType = $insSetting[0]; // 货物类别代码
        $packCode = $insSetting[3]; // 包装方式代码
        $mainItemCode = $insSetting[6]; // 条款代码
        $mainItemName = $insSetting[7]; // 条款名称
        $procotolNo = array(
            'SHTB' => 'A|3010200|C20180144-19001P000431|CAGHYX190032',
            'YRWL' => 'A|3010100|C20190295|0',
            'SHGH3' => 'A|3010100|C20200410P000187|COPSHH200411',
            'COPCHQ210033' => 'A|6020100|C20210059P000006|COPCHQ210033',
            'CAGWUX210005' => 'A|3020300|C20210014P000012|CAGWUX210005',
            'COPCHQ220028' => 'A|6020100|C20210059-22001|0',
            'CDYF' => 'C|3010100|CSHHHYX2024P000473|0|CSHHHYX2024Q000452',
        );

        $unitCode = array(
            'SHTB' => '3010200',
            'YRWL' => '3010100',
            'SHGH3' => '3010100',
            'COPCHQ210033' => '6020100',
            'CAGWUX210005' => '3020300',
            'COPCHQ220028' => '6020100',
            'CDYF' => '3010100',
        );

        $franchise = array(
            'SHTB' => '本保单其他承保条件同协议。',
            'YRWL' => '本保单其他承保条件同协议; 每次事故绝对免赔额为人民币1000或损失金额的10%，两者取高;',
            'SHGH3' => '本保单其他承保条件同协议。',
            'COPCHQ210033' => $settings[1],
            'CAGWUX210005' => $settings[1],
            'COPCHQ220028' => $settings[1],
            'CDYF' => "每次事故的综合免赔为绝对免赔额2000人民币或损失金额的10%，两者以高者为准 ，包括全损。\r\n火灾、翻车事故每次事故的综合免赔为绝对免赔额2000人民币或损失金额的15%，两者以高者为准 ，包括全损。\r\n针对酒类（单瓶价格低于100元/瓶）的综合免赔为绝对免赔额2000人民币或损失金额的10%，两者以高者为准 ，包括全损。\r\n针对易碎品的综合免赔为保额金额的3% ，包括全损。",
        );

        $rateArr = array(  //费率信息  最低费率(minRate) 最高保额(limitAmount) 最低保费(minPremium)
            'SHTB' => ['0.5', '3000000', '50'],
            'YRWL' => ['0.15', '500000', '3'],
            'SHGH3' => ['0.12', '5000000', '15'],
            'COPCHQ210033' => ['0.20', '5000000', '15'],
            'CAGWUX210005' => ['0.20', '5000000', '15'],
            'COPCHQ220028' => ['0.20', '5000000', '15'],
        );
        $cdyfRate = ['CDYF' => ['0.15', '3000000', '15']];
        if ($insSetting[6] == 'JB' && $insSetting[0] == '0309') {
            $cdyfRate = ['CDYF' => ['0.12', '3000000', '15']];
        }
        $rateArr = array_merge($rateArr, $cdyfRate);

        $minRate = $rateArr[$setting[0]][0];
        $limitAmount = $rateArr[$setting[0]][1];
        $minPremium = $rateArr[$setting[0]][2];

        if ($policy['ratio'] != '') {
            $rate = $policy['ratio'] / 10;
        } else {
            $rate = $minRate;
        }
        $premium = ($policy['insuredAmount'] * $rate) / 1000;
        if ($premium < $minPremium) {
            $premium = $minPremium;
        }

        $packTypeArr = array(
            '裸装' => '05',
            '散装' => '04',
            '纸箱' => '01',
            '木箱' => '01',
            '捆包' => '08',
            '袋装' => '02',
            '篓装' => '01',
            '托盘' => '03',
            '桶装' => '06',
            '罐装' => '07',
        );

        $transportTypeArr = array(
            '3' => '1',
            '4' => '5',
            '5' => '4',
            '6' => '3',
        );

        $data = array(
            'procotolNo' => $procotolNo[$setting[0]],                  //预保协议号
            'unitCode' => $unitCode[$setting[0]],                      //唯一编码 与协议绑定
            'franchise' => $franchise[$setting[0]],                      //免赔
            'specialize' => '',                      //特约
            'comments' => '本保单为相关预约保险单的有效组成部分，两者如有冲突，以预约保险单为准。',                      //备注
            //客户信息
            'holderName' => $policy['holderName'],
            'recognizeeName' => $policy['recognizeeName'],

            //货物信息
            'mark' => $policy['invNo'] . '/' . $policy['freightNo'],               //标记,发票号码,运单号
            'quantity' => $policy['quantity'],                                  //包装及数量
            'cargoName' => $policy['goodsName'],                                   //货物明细
            'packCode' => $packCode,                                                //包装方式
            'cargoType' => $cargoType,                                              //货物类型
            'kindName' => $policy['transport'],                                      //运输工具名称
            'voyNo' => "",                                                          //航次
            'kind' => $transportTypeArr[$policy['transportTypeID']],                        //运输方式
            "startPort" => $policy['fromLoc'],                                 //起运地
            "transPort" => $policy['viaLoc'],                                  //中转地
            "endPort" => $policy['toLoc'],                                //目的地

            //保险信息
            'mainItemCode' => $mainItemCode,                                        //条款代码
            'mainItemName' => $mainItemName,                                        //条款名称
            'amount' => $policy['insuredAmount'],                                        //保额
            'rate' => $rate,                                                        //费率
            'minRate' => $minRate,                                                        //费率
            'premium' => $premium,                                                  //保费
            'limitAmount' => $limitAmount,                                          //最高保额
            'billType' => '0',                                          //发票类型
            'invHead' => '',                                          //发票抬头
            'bussLicence' => '',                                          //投保纳税人识别号
            'invHeadLicence' => '',                                          //开票人纳税人识别号
            'invHeadLicence' => '',                                          //开票人纳税人识别号
        );

        if ($setting[0] == 'CDYF') {
            $data['holderName'] = '成都优孚世纪信息技术有限公司';
            if ($insSetting[6] == 'JB') {
                if ($insSetting[0] == '0309') {
                    $data['tCargoCoverage'] = '{"tCargoCoverageId":{"auditType":"C","unitCode":"3010100","applyNo":"CSHHHYX2024P000473","applyEndorseNo":"0","coverageNo":2},"classesCode":"********","flightAreaCode":"0","rate":0.000120,"detailLimit":3000000.0000,"startPlace":null,"endPlace":null,"tCargoCoverageCargos":[{"id":400475,"code":"0309","name":"轻工品类（非易碎）","cibscode":"0309"},{"id":400476,"code":"0701","name":"设备仪器类（整机）","cibscode":"0701"}],"tCargoCoverageMains":[{"id":214913,"code":"HY000063","name":null,"cibscode":null}],"tCargoCoveragePacks":[{"id":297632,"code":"104","name":"裸装","cibscode":"05"},{"id":297630,"code":"101","name":"箱装","cibscode":"01"},{"id":297631,"code":"103","name":"托盘","cibscode":"03"}],"tCargoCoverageKinds":[{"id":378435,"code":"4","name":"公路","cibscode":"4"}],"tCargoCoverageClauses":[{"id":276994,"code":"HY000063.HYCC00118","name":"国内水陆路基本险","cibscode":"JB"}],"classesName":"公路货运险"}';
                }
                if ($insSetting[0] == '0301') {
                    $data['tCargoCoverage'] = '{"tCargoCoverageId":{"auditType":"C","unitCode":"3010100","applyNo":"CSHHHYX2024P000473","applyEndorseNo":"0","coverageNo":4},"classesCode":"********","flightAreaCode":"0","rate":0.000150,"detailLimit":3000000.0000,"startPlace":null,"endPlace":null,"tCargoCoverageCargos":[{"id":400478,"code":"0301","name":"玻璃、陶瓷、搪瓷等易碎品类","cibscode":"0301"}],"tCargoCoverageMains":[{"id":214915,"code":"HY000063","name":null,"cibscode":null}],"tCargoCoveragePacks":[{"id":297634,"code":"101","name":"箱装","cibscode":"01"}],"tCargoCoverageKinds":[{"id":378437,"code":"4","name":"公路","cibscode":"4"}],"tCargoCoverageClauses":[{"id":276996,"code":"HY000063.HYCC00118","name":"国内水陆路基本险","cibscode":"JB"}],"classesName":"公路货运险"}';
                }
            }
            if ($insSetting[6] == 'ZH') {
                $data['tCargoCoverage'] = '{"tCargoCoverageId":{"auditType":"C","unitCode":"3010100","applyNo":"CSHHHYX2024P000473","applyEndorseNo":"0","coverageNo":1},"classesCode":"********","flightAreaCode":"0","rate":0.000150,"detailLimit":3000000.0000,"startPlace":null,"endPlace":null,"tCargoCoverageCargos":[{"id":400473,"code":"0810","name":"民用电子产品","cibscode":"0810"},{"id":400471,"code":"0701","name":"设备仪器类（整机）","cibscode":"0701"},{"id":400460,"code":"0514","name":"不锈钢及其制品","cibscode":"0514"},{"id":400463,"code":"0309","name":"轻工品类（非易碎）","cibscode":"0309"},{"id":400464,"code":"0310","name":"塑料制品","cibscode":"0310"},{"id":400469,"code":"0513","name":"钢材、钢管、铸铁类","cibscode":"0513"},{"id":400468,"code":"0711","name":"医疗设备","cibscode":"0711"},{"id":400472,"code":"0702","name":"机器设备类（配件）","cibscode":"0702"},{"id":400466,"code":"0812","name":"电子产品配件","cibscode":"0812"},{"id":400467,"code":"0705","name":"电力设备","cibscode":"0705"},{"id":400470,"code":"0207","name":"糖类","cibscode":"0207"},{"id":400474,"code":"0504","name":"五金类（除钢材类外）","cibscode":"0504"},{"id":400461,"code":"0311","name":"橡胶制品","cibscode":"0311"},{"id":400462,"code":"0405","name":"纺织品","cibscode":"0405"},{"id":400465,"code":"0806","name":"电脑、手提（除大型终端）","cibscode":"0806"},{"id":400459,"code":"0803","name":"手机","cibscode":"0803"}],"tCargoCoverageMains":[{"id":214912,"code":"HY000063","name":null,"cibscode":null}],"tCargoCoveragePacks":[{"id":297625,"code":"104","name":"裸装","cibscode":"05"},{"id":297627,"code":"103","name":"托盘","cibscode":"03"},{"id":297626,"code":"107","name":"盘卷包装","cibscode":"08"},{"id":297629,"code":"101","name":"箱装","cibscode":"01"},{"id":297628,"code":"105","name":"桶装","cibscode":"06"},{"id":297624,"code":"102","name":"袋装","cibscode":"02"}],"tCargoCoverageKinds":[{"id":378434,"code":"4","name":"公路","cibscode":"4"}],"tCargoCoverageClauses":[{"id":276993,"code":"HY000063.HYCC00119","name":"国内水陆路综合险","cibscode":"ZH"}],"classesName":"公路货运险"}';
            }

            $data['billType'] = $policy['invType'] == '1' ? '0' : '1';
            $data['invHead'] = $policy['invHead'];
            $data['bussLicence'] = $policy['bussLicence'];
            $data['invHeadLicence'] = $policy['invHeadLicence'];
            $data['specialize'] = "本保单其他承保条件同协议;\\n1.对于食品、饮料、药品（无温控要求）或其他有外包装的日用品，在保险事故仅导致外包装损失，内包装及商品本身无损失的情况下，本保单仅就受损的外包装承担赔偿责任；在保险事故导致部分损失时，本保单仅就受损部分的保险损失承担保险责任，不承担整箱、整件货物被拒收或无法销售带来的损失；\\n2、承载保险货物车辆运输途中未发生交通事故，保险人不承担串味所致损失的赔偿责任。若运输货物为钢材以及相关制品,且货物裸装，则锈损为除外责任；若需运输食品, 则腐烂变质为除外责任；\\n3、所有标的发生保险责任内的事故损失时，均以修复为第一赔偿方式；\\n4、如被保险人违反交通安全运输规定或相关装载运输规定的，保险人有权拒赔；\\n5、若保险标的（货物）已运抵目的地，而未及时提货，保险人对因此造成的损失不承担赔偿责任；\\n8、对未生成保单号，或起运/投保前已发生事故的保险标的（货物），保险人不承担赔偿责任；\\n9、机械设备必须有木箱包围及底部托盘、金属固定架等防震防撞外包装，裸装、纸壳包装、塑料泡沫外包装的机械设备只承保火灾爆炸、交通事故、上下装卸的损失；\\n10、当承保货物为易碎品时，保险人仅承担因交通事故和火灾、爆炸导致的标的损失;\\n11、出险时，如运输车辆为重型低平板半挂车，且行驶证标注“仅可用于运送不可拆解物体”，如实际运输货物为普通货物，在满足安全装载规定的情况下，每次事故绝对免赔率加扣10%。";
        }

        return $data;
    }
}
