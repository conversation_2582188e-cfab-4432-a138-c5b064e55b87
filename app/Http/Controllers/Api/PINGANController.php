<?php

namespace App\Http\Controllers\Api;

class PINGANController
{

    public function approval($policy, $settings)
    {
        $setting = explode("|#|", $settings[0]);

        $policy = $this->prepareDepartureDate($policy);

        $policy = $this->prepareHolderData($setting[0], $policy);

        $policy = $this->prepareClientData($setting[0], $policy);

        $policy = $this->prepareCargoData($setting[0], $policy);

        $policy = $this->preparePremiumData($policy);

        $policy = $this->prepareAccountData($setting[0], $policy);

        $customData = $this->prepareCustomData($setting[0], $settings, $policy);
        $customContent = $customData['customContent'];
        $fixedSpecial = $customData['fixedSpecial'];

        $policy['quantity'] .= empty(trim($customContent)) ? '' : "\n" . $customContent;

        $policy['customContent'] = '';
        if ($setting[0] != 'CGZYJJ00002') {
            $policy['fixedSpecial'] = $fixedSpecial;

        }

        // 添加免赔特约
        $policy['deductible'] = $settings[1];
        $policy['special'] = $settings[2];

        if (!empty($setting[5])) {
            $policy['additionClause'] = [$setting[5]];
        }

        if ($setting[0] == 'CGZYJJ00002') {
            //            $policy['goodsType'] = '16';
        }

        $data = [
            "config" => [
                "account" => $setting[0],
                "password" => $setting[1]
            ],
            "message" => $policy,
            "settings" => $setting
        ];

        foreach ($data['message'] as &$value) {
            if ($value === ' ') {
                $value = '';
            }
            $value = str_replace(["\t"], "", $value);
            $value = preg_replace('/\p{Z}/u', ' ', $value);
        }

        return $data;
    }

    /**
     * 处理起运时间
     *
     * @param $policy
     * @return mixed
     */
    protected function prepareDepartureDate($policy)
    {
        if (date('Y-m-d', $policy['departureDate']) == date('Y-m-d') && date('H', $policy['departureDate']) == '00') {
            $policy['departureDate'] = time();
        }
        $startDate = date('Y-m-d H', $policy['departureDate']);
        $start = date('Y-m-d', $policy['departureDate']);
        //        if (strtotime($start) <= strtotime(date('Y-m-d'))) {
//            $startDate = date('Y-m-d H');
//        }
        $policy['departureDate'] = $startDate;

        return $policy;
    }

    /**
     * 处理货物信息
     *
     * @param $account
     * @param $policy
     * @return mixed
     */
    protected function prepareCargoData($account, $policy)
    {
        if ($policy['pack'] == $policy['quantity']) {
            $policy['pack'] = '-';
        }
        //提交到autoins的发票号是用/拼接的,当发票号为空时,不能让它为/
        if ($policy['invNo'] == '/') {
            $policy['invNo'] = '';
        }
        if ($account == 'CGZZYFSD00001') {
            $policy['goodsType'] = '20';
            $policy['pack'] = '其他形状包装';
        }

        $policy['goodsTypeSmall'] = $this->getGoodsType($policy['goodsType']);

        return $policy;
    }

    /**
     * 处理保额/保费数据
     *
     * @param $policy
     * @return mixed
     */
    protected function preparePremiumData($policy)
    {
        $policy['insuredAmount'] = round($policy['insuredAmount'], 2);

        return $policy;
    }

    /**
     * 准备投保人联系方式/地址未填时默认数据
     *
     * @param $account
     * @param $policy
     * @return mixed
     */
    protected function prepareHolderData($account, $policy)
    {
        $policy['holderLinkMan'] = $policy['holderName'];
        if ($this->isYRKJAccount($account)) {
            if (empty($policy['holderPhone']) || (strlen($policy['holderPhone']) < 5)) {
                $policy['holderPhone'] = '***********';
            }
            if (empty($policy['holderAddr']) || (strlen($policy['holderAddr']) < 18)) {
                $policy['holderAddr'] = '上海市虹口区周家嘴路1010号';
            }
            if (empty($policy['recognizeeAddr']) || (strlen($policy['recognizeeAddr']) < 18)) {
                $policy['recognizeeAddr'] = '上海市虹口区周家嘴路1010号';
            }
            $policy['holderLinkMan'] = '王先生';
            if ($account == 'CGGZYNYZK200001') {
                //                $policy['holderName'] = '云南运杰保网络科技有限公司';
            }
        }


        if ($account == 'CGSHZGDYA00002') {
            if ($policy['holderName'] == '') {
                $policy['holderName'] = '曹金凤';
            }
            if ($policy['holderPhone'] == '' || (strlen($policy['holderPhone']) < 5)) {
                $policy['holderPhone'] = '***********';
            }
            if ($policy['holderAddr'] == '' || (strlen($policy['holderAddr']) < 18)) {
                $policy['holderAddr'] = '东莞市南城区袁屋边车站北路恒正大厦5楼';
            }
        }

        if ($account == 'CGZYJJ00002') {
            if ($policy['holderName'] == '') {
                $policy['holderName'] = '王先生';
            }
            if ($policy['holderPhone'] == '' || (strlen($policy['holderPhone']) < 5)) {
                $policy['holderPhone'] = '***********';
            }
            if ($policy['holderAddr'] == '' || (strlen($policy['holderAddr']) < 18)) {
                $policy['holderAddr'] = '上海市常熟路8号';
            }
            $policy['holderLinkMan'] = '王先生';
        }

        if (in_array($account, ['CGWQHY00011', 'CGWQHYKJ00001', 'CGWQMYZH00001'])) {
            if ($policy['holderName'] == '') {
                $policy['holderName'] = '陈先生';
            }
            if ($policy['holderPhone'] == '' || (strlen($policy['holderPhone']) < 5)) {
                $policy['holderPhone'] = '***********';
            }
            if ($policy['holderAddr'] == '' || (strlen($policy['holderAddr']) < 18)) {
                $policy['holderAddr'] = '新疆乌鲁木齐经济技术开发区（头屯河区）卫星路477号商住小区写字楼705室';
            }
            if (empty($policy['recognizeeAddr']) || (strlen($policy['recognizeeAddr']) < 18)) {
                $policy['recognizeeAddr'] = '新疆乌鲁木齐经济技术开发区（头屯河区）卫星路477号商住小区写字楼705室';
            }
            $policy['holderLinkMan'] = '陈先生';
        }

        if ($account == 'CGZZYFSD00001') {
            // 投保人信息
            $policy['holderName'] = '四川优孚时代信息技术有限公司';
            $policy['holderCustomerType'] = '0';
            $policy['holderIdenty'] = '91510105MA6AYWJC9R';
            $policy['holderLinkMan'] = '刘女士';
            $policy['holderPhone'] = '***********';
            $policy['holderAddr'] = '上海市常熟路8号';

            // 被保人信息
            $policy['recognizeeCustomerType'] = $this->isIdCard($policy['recognizeeIdenty']) ? '1' : '0';

        }

        return $policy;
    }

    /**
     * 获取小类
     *
     * @param $bigType
     * @return mixed
     */
    protected function getGoodsType($bigType)
    {
        $typeArr = array(
            '01' => '0105',
            '02' => '1001',
            '03' => '1501',
            '04' => '1601',
            '05' => '2501',
            '06' => '2801',
            '07' => '3901',
            '08' => '4101',
            '09' => '4401',
            '10' => '4701',
            '11' => '5001',
            '12' => '6401',
            '15' => '7201',
            '16' => '8412',
            '17' => '8601',
            '18' => '9001',
            '20' => '9401',
        );

        isset($typeArr[$bigType]) ? $result = $typeArr[$bigType] : $result = $typeArr['20'];
        return $result;
    }

    /**
     * 预处理自定义其他内容信息
     * 
     * @param mixed $account
     * @param mixed $settings
     * @param mixed $policy
     * @return array
     */
    protected function prepareCustomData($account, $settings, $policy)
    {
        if ($this->isYRKJAccount($account)) {
            $deductible = $settings[1];
            $special = $settings[2];
            $fixedSpecial = [];

            switch ($account) {
                case 'CGSHZQLJJ00001': // 深圳平安-运吉宝
                case 'CGSHZHMBX00002': // 深圳平安-保呀
                    $special = '';
                    $deductible = '';
                    break;
                case 'CGZYJJ00002': // 上海平安
                    if ($policy['subject'] == 'NORMAL') {
                        $special = '';
                        //                    $deductible = '';

                    }
                    if ($policy['goodsType'] == '10') {
                        //                    $deductible .= "\n每次事故绝对免赔为保额的3%";
                    }
                    //                    $deductible .= "\n无包装或简易包装的纸张及其制品：每次事故绝对免赔额为保额的3%；家具类：每次事故绝对免赔额为RMB2000或损失金额的5%，取高者。";
                    break;
                case 'CGGZQLBXJJ00001': // 广东平安
                    if ($policy['subject'] != 'MANUAL') {
                        $special = '';
                        $deductible = '';
                    }
                    break;
                case 'CGGZZJGJLY00001': // 平安广东JLY
                    $special = '';
                    $deductible = '';
                    break;
                case 'CGGZLJGYL00001': // 平安广东-LJ
                    $special = '';
                    $deductible = '';
                    break;
                case 'CGGZHNHYT00001': // 平安广东-华宇通
                    $special = '';
                    $deductible = '';
                    break;
                default:
                    break;
            }

            if ($policy['pack'] == '裸状包装') {
                // if ($account == 'CGZYJJ00002') {
                //     $fixedSpecial[] = "裸装货物特约";
                // } else {
                $deductible .= "\n裸装：本保单不承保由于刮擦，锈损，凹瘪引起的损失。";
                // }
            }

            $customContent = $special . "\n" . $deductible;

            return ['customContent' => $customContent, 'fixedSpecial' => $fixedSpecial];
        } else {
            return ['customContent' => '', 'fixedSpecial' => ''];
        }

    }

    /**
     * 处理平安投被保人证件类型信息
     *
     * @param $policy
     * @param $account
     * @return mixed
     */
    protected function prepareClientData($account, $policy)
    {
        $policy['holderIdType'] = $policy['recognizeeIdType'] = '03';

        if ($policy['holderCustomerType'] == '1') {
            $policy['holderIdType'] = '99';
        }

        if (in_array($account, ['CGHZYRWLKJ00002', 'CGNJYB00013', 'CGKMYJB00002', 'CGFZZF200001', 'CGGZYNYZK200001', 'CGSHZHMBX00002', 'CGSHZQLJJ00001', 'CGGZQLBXJJ00001', 'CGZYJJ00002', 'CGGZLJGYL00001'])) {
            $policy['holderIdType'] = '99';
        }

        if ($policy['recognizeeCustomerType'] == '1') {
            $policy['recognizeeIdType'] = '01';
        }

        if (in_array($account, ['CGGZQLBXJJ00001', 'CGZYJJ00002'])) {
            if (empty($policy['recognizeeIdenty'])) {
                $policy['recognizeeIdenty'] = '91310230MA1K2FMP33';
            }
        }

        return $policy;
    }

    /**
     * 添加不同账号单独处理
     *
     * @param $account
     * @param $policy
     * @return mixed
     */
    protected function prepareAccountData($account, $policy)
    {
        switch ($account) {
            case 'CGGZQLBXJJ00001': // 广东平安
                // $policy['goodsType'] = '20';
                // $policy['goodsTypeSmall'] = '9401';
                // if($policy['goodsType'] == '03'){
                //     $policy['goodsType'] = '03';
                //     $policy['goodsTypeSmall'] = '1501';
                // }
                break;
            default:
                break;
        }
        return $policy;
    }

    /**
     * 是否为赢睿的账号
     *
     * @param $account
     * @return bool
     */
    protected function isYRKJAccount($account)
    {
        if (
            in_array($account, [
                'CGHZYRWLKJ00002',
                'CGNJYB00013', //平安江苏货运险
                'CGZYJJ00002', //平安上海货运险
                'CGSHZQLJJ00001', //平安深圳YJB
                'CGGZQLBXJJ00001', //平安广东-全联
                'CGGZZJGJLY00001', //平安广东-JLY
                'CGKMYJB00002', //平安昆明
                'CGFZZF200001', //平安福建果蔬
                'CGGZYNYZK200001', //平安广东果蔬账号
                'CGSHZHMBX00002', //平安深圳货运险
                'CGGZLJGYL00001', //平安广东-LJ
                'CGGZHNHYT00001', //平安广东-LJ
            ])
        ) {
            return true;
        }

        return false;
    }

    /**
     * 是否为身份证号
     * 
     * @param mixed $code
     * @return bool
     */
    protected function isIdCard($code)
    {
        // 判断是否为 18 位身份证号
        if (strlen($code) == 18) {
            if (!preg_match('/^\d{17}[\dXx]$/', $code)) {
                return false;
            }
            $factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
            $verifyCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
            $sum = 0;
            for ($i = 0; $i < 17; $i++) {
                $sum += intval($code[$i]) * $factor[$i];
            }
            $mod = $sum % 11;
            $verifyCode = strtoupper($code[17]);
            if ($verifyCode === $verifyCodes[$mod]) {
                return true;
            }
        }

        // 判断是否为 15 位身份证号
        if (strlen($code) == 15) {
            if (preg_match('/^\d{15}$/', $code)) {
                return true;
            }
        }

        return false;
    }

}
