<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use JWTAuth;
use App\Http\Controllers\Controller;
use App\Models\Message;
use App\Models\MessageAttach;
use App\Models\Notify;
use App\Models\Product;

class InsureController extends ApiController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function getAuthUser(Request $request)
    {
        $this->validate($request, [
            'token' => 'required'
        ]);

        $user = JWTAuth::authenticate($request->token);

        return response()->json(['user' => $user]);
    }


    // test
    public function create(Request $request)
    {
        $this->validate($request, [
            'token' => 'required'
        ]);

        $platform = JWTAuth::authenticate($request->token);

        is_array($request->message) ? $messages = $request->message : $messages = json_decode($request->message, true);
        is_array($request->notifies) ? $notifies = $request->notifies : $notifies = json_decode($request->notifies, true);
        is_array($request->settings) ? $settings = $request->settings : $settings = json_decode($request->settings, true);

        $ins = $messages;

        $productModel = new Product();
        $product = $productModel->where('product_code', $request->product_code)->first();
        $message['platform_id'] = $platform['id'];
        $message['product_id'] = $product['id'];
        $message['mode'] = $product['mode'];
        $message['order_no'] = $ins['orderNo'];
        $message['done_at'] = now();
        $message['is_locked'] = 0;
        $message['status'] = 0;
        $message['created_at'] = now();

        DB::beginTransaction();
        try {
            // 报文主表
            $messageModel = new Message();
            $messageId = $messageModel->insertGetId($message);

            // 报文附表
            $messageAttach['message_id'] = $messageId;
            //来源报文
            $messageAttach['source'] = jsonFormat(array(
                'token' => $request->token,
                'message' => $ins,
                'settings' => $settings,
                'product_code' => $request->product_code,
                'notifies' => $notifies
            ));

            //整理成保险公司格式数据
            $callback = '';
            $mode = $product['mode'];
            $setting = explode("|#|", $settings[0]);
            $isApi = false;
            switch ($mode) {
                case 'API_SINOSIG';
                    $isApi = true;
                    $approval = new SinosigController();
                    $callback = $approval->approval($ins, $product, $settings);
                    break;
                case 'AUTO_PICC';
                    $approval = new PICCController();
                    $callback = $approval->approval($ins, $product, $settings, $platform);
                    break;
                case 'AUTO_PICC_INTL';
                    $approval = new PICCIntlController();
                    $callback = $approval->approval($ins, $product, $settings, $platform);
                    break;
                case 'AUTO_CPIC';
                    $approval = new CPICController();
                    $callback = $approval->approval($ins, $product, $settings);
                    break;
                case 'AUTO_PINGAN';
                    $approval = new PINGANController();
                    $callback = $approval->approval($ins, $settings);
                    break;
                case 'API_STARR';
                    $approval = new STARRController();
                    $callback = $approval->approval($ins, $product, $setting);
                    break;
                case 'AUTO_CPIC_INTL';
                    $approval = new CPICIntlController();
                    $callback = $approval->approval($ins, $product, $settings, $platform);
                    break;
                case 'AUTO_PINGAN_INTL';
                    $approval = new PINGANIntlController();
                    $callback = $approval->approval($ins, $settings, $platform);
                    break;
                case 'AUTO_BDYF';
                    $approval = new BDYFController();
                    $callback = $approval->approval($ins, $product, $setting);
                    break;
                case 'API_SINOSIG_QZ';
                    $approval = new SINOSIG_QINZHOUController();
                    $callback = $approval->approval($ins, $product, $setting);
                    break;
                case 'API_HUATAI';
                    $isApi = true;
                    $approval = new HUATAIController();
                    $callback = $approval->approval($ins, $product, $settings);
                    break;
                case 'API_GROUP_ZY';
                    $isApi = true;
                    $approval = new GROUP_ZHONGYIController();
                    $callback = $approval->approval($ins, $product, $settings);
                    break;
                case 'AUTO_TEST';
                    break;
            }

            $messageAttach['content'] = json_encode($callback);
            $messageAttach['created_at'] = now();

            $messageAttachModel = new MessageAttach();
            $messageAttachModel->create($messageAttach);

            // 通知地址表

            $notifyModel = new Notify();
            foreach ($notifies as $notifie) {
                $notify['message_id'] = $messageId;
                $notify['content'] = 'free';
                $notify['status'] = 0;
                $notify['order_no'] = $notifie['order_no'];
                $notify['url'] = $notifie['url'];
                $notifyModel->create($notify);
            }

            DB::commit();

            return response()->json('ok');
        } catch (\Exception $exception) {
            Log::error($exception);
            DB::rollBack();
        }
    }

    /**
     * 平台方退回保单
     *
     * @param Request $request
     * @param Message $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendBack(Request $request, Message $message)
    {
        $this->validate($request, [
            'token' => 'required'
        ]);

        $orderNo = $request->input('order_no');
        $message = $message->where('order_no', $orderNo)->orderBy('id', 'desc')->first();
        if(empty($message)){
            return response()->json(['isSuccess' => 'Y', 'errorMessage' => '退回成功']);
        }
        if($message['is_entry'] || $message['policy_no'] != ''){
            return response()->json(['isSuccess' => 'N', 'errorMessage' => '该保单出单中,无法退回']);
        }
        $data = [
            'status' => -1,
            'insure_callback' => '平台方退回-'.$request->input('reason')
        ];
        $message->update($data);

        return response()->json(['isSuccess' => 'Y', 'errorMessage' => '退回成功']);
    }
}
