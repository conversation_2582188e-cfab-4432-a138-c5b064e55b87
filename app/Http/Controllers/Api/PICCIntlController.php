<?php

namespace App\Http\Controllers\Api;

class PICCIntlController
{

    public function approval($policy, $product, $allSettings, $platform)
    {
        // 技术配置  账号 密码 大类 小类 主险 是否添加盗抢(0:否 1:是) 保单号 头
        $setting = explode("|#|",$allSettings[0]);
        $startDate = date('Y-m-d', $policy['departureDate']);

        $effDate = date('Y-m-d');

        // $sys_params = explode('|#|', $product['parameters']);

        $accountData = $this->getAccountData($setting[0]);
        $clauseData = $this->getClauseData($policy['mainClause']);
        $additionIds = array_filter(explode(',', $policy['additionClause']));
        $addiIds = '';
        $addiClause = '';
        foreach ($additionIds as $additionId){
            $addiIds .= $this->getAdditionClause($additionId)[0].',';
            $addiClause .= $this->getAdditionClause($additionId)[1]."\r\n";
        }

        $goodsType = $this->getGoodsType($policy['goodsType']);

        $invCurrency = $this->getCurrency($policy['invCurrency']);

        $data = array(
            "config" => array(
                "account" => $setting[0],
                "password" => $setting[1],
            ),
            "message" => array(
                "holderName"          => $policy['holderName'],  //投保人名称
                "holderAddr"          => $policy['holderAddr'],  //投保人地址
                "recognizeeName"      => $policy['recognizeeName'],  //被保人名称
                "recognizeeAddr"      => $policy['recognizeeAddr'],  //被保人地址
                "invNo"               => $policy['invoiceNo'],    //发票号
                "ladingNo"            => $policy['landBillNo'],    //提/运单号
                "contract"            => '',    //合同号 留空
                "mark"                => $policy['shippingMark'],    //标记 唛头
                "goodsName"           => $policy['goodsName'],  //货物名称
                "quantity"            => $policy['goodsAmount'],   //包装及数量
                "goodsTypeID"         => $setting[3],  //小类
                "pack"                => $this->getPackType($policy['packType']), // 包装
                "rangeID"             => $this->getRangeId($policy['insureType']), // 运输范围
                "transportTypeID"     => $this->getTransportTypeId($policy['transportType']), // 运输方式ID
                "transport"           => $policy['transportNo'],  //运输工具名称 车牌号等
                "fromLoc"             => $policy['fromLoc'],    //起运地
                "viaLoc"              => $policy['viaLoc'],     //中转地
                "toLoc"               => $policy['toLoc'],      //目的地
                "_PortIdSel2"         => '',      //理赔代理地 详情
                "portIdSel"           => '',      //理赔代理
                "itemCategory"        => $clauseData[0],  //条款分类
                "glausesID"           => $clauseData[1],  //主险条款
                "glauses"             => $clauseData[2],
                "additive"            => $addiClause, // 附加条款
                "creditId"            => "", // 信用证号
                "printPolicyNum"      => "2", // 保险单正本份数描述
                // ----------------------------------------------------
                "currencyID"          => $invCurrency[0], // 投保币种
                "endCurrencyID"       => $invCurrency[0], // 结算币种
                "addPropTypeID"       => "1", // 价格条件 不做修改
                "invoiceAmount"       => $policy['invAmount'], // 发票金额
                "addProportion"       => $policy['incRate'], // 加成比例
                "insuredAmount"       => $policy['amount'], // 保额
                "ratio"               => $policy['rate'] / 10,  //平台费率是万分之几,但是自动录单拿到的就只是整数,没有万分之,保险公司单位是千分之,所以需要除以10
                "premium"             => $policy['premium'],  //保费
                "paymentLoc"          => $policy['payLoc'], // 赔付地
                "deductible"          => $allSettings[1],  //免赔
                "effDate"             => $effDate,  //签单时间 年月日
                "effTime"             => '',  //签单时间 小时 进出口为空
                "departureDate"       => $startDate,    //起运时间 年月日
                "departureTime"       => "",    //起运时间 小时进出口为空
                "remark"              => $allSettings[2],  //特约
                "extUsrNo"            => "", // 自定义查询编码 不做修改
                "shipCName"           => "", // 船名(中名) 不做修改
                "shipEName"           => "", // 船名(英名) 不做修改
                "fleetNo"             => "", // 船队编号 不做修改
                "itemNo"              => "0", // 标的序号 不做修改
                "stepHull"            => "无船级", // 船级 不做修改
                "shipFlag"            => "", // 船旗 不做修改
                "associate"           => "",
                "makeYearMonth"       => $startDate, //创建日期
                "countryCode"         => "",
                "makeFactory"         => "",
                "endTypeID"           => "41",  //结算方式  40:逐单结 41:月结 42:其他
                "invHead"             => "",
                "postalModeId"        => "57",  //保单投递方式 写死
                "contactPerson"       => "",
                "contactTel"          => "",
                "postalCode"          => "",
                "postalAddr"          => "",
                "transferRate"        => "", // 运输费率
                "packQty"             => $policy['goodsAmount'],   //包装及数量
                "tagDisplay"          => $policy['shippingMark'], // ?唛头
                "invRefNo"            => $policy['invoiceNo'], // 发票号
                "additiveNo"          => $addiIds,
                "matchingchar"        => "", // 理赔代理
                "policyNo"            => "",
                "policyNoLong"        => "",
                "insuranceID"         => "2",  // ?投保类型 1:国内 2:进出口
                "invoiceNo"           => "", //
                "ifPackage"           => "", //
                "policyNoRemark"      => "",
                "changeNo"            => "",
                "contractNo"          => "",
                "agentLocID"          => "",  //
                "portID"              => "",  //
                "templateDesc"        => "",
                "enDelayApplay"       => "",
                "agreenumStartDate"   => $accountData['agreenumStartDate'],  // ?签约日期 与账号绑定
                "policyNoHead"        => $setting[4],  // 保单号 头 与账号绑定
                "judge"               => "0",
                "myfileFileName"      => "",

                'holderProvince' => '110000', // 投保人地址-省
                'holderCity' => '110100', // 投保人地址-市
                'holderCounty' => '110101', // 投保人地址-县
                'holderCustomerType' => '0', // 投保人客户类型-固定团体
                'holderOverseas' => '0', // 投保人是否为境外-固定否
                'holderDocumentType' => '统一社会信用代码', // 投保人证件类型-固定统一社会信用代码
                'holderIdenty' => '91310230MA1K2FMP33', // 投保人证件号-固定上海赢睿统一社会信用代码
                'holderStartDateValid' => '2019-01-04', // 投保人证件号有效起始日期-固定上海赢睿统一社会信用代码
                'holderEndDateValid' => '2039-01-03', // 投保人证件号有效结束日期-固定上海赢睿统一社会信用代码
                'holderUnitProperties' => '300', // 投保人单位性质-固定企业单位
                'holderLinkMan' => $policy['holderName'], // 投保人单位联系人-固定投保人名称
                'holderSex' => '', // 投保人性别-固定空
                'holderBirthday' => '', // 投保人出生日期-固定空
                'holderPhone' => '', // 投保人电话-固定空

                'recognizeeProvince' => '110000', // 被保人地址-省
                'recognizeeCity' => '110100', // 被保人地址-市
                'recognizeeCounty' => '110101', // 被保人地址-县
                'recognizeeCustomerType' => '0', // 被保人客户类型-固定团体
                'recognizeeOverseas' => '0', // 被保人是否为境外-固定否
                'documentType' => '统一社会信用代码', // 被保人证件类型-固定统一社会信用代码
                'recognizeeIdenty' => '123456789111213140', // 被保人证件号-固定上海赢睿统一社会信用代码
                'recognizeeStartDateValid' => '2019-01-04', // 被保人证件号有效起始日期-固定上海赢睿统一社会信用代码
                'recognizeeEndDateValid' => '2039-01-03', // 被保人证件号有效结束日期-固定上海赢睿统一社会信用代码
                'recognizeeUnitProperties' => '300', // 被保人单位性质-固定企业单位
                'recognizeeLinkMan' => $policy['recognizeeName'], // 被保人单位联系人-固定被保人名称
                'recognizeeSex' => '', // 被保人性别-固定空
                'recognizeeBirthday' => '', // 被保人出生日期-固定空
                'recognizeePhone' => '', // 被保人电话-固定空
            ),
            "settings"=>$setting
        );

        $data['message']['deductible'] = $allSettings[1];
        $data['message']['remark'] = $allSettings[2];

        if (empty($data['message']['holderName'])) {
            $data['message']['holderName'] = $data['message']['recognizeeName'];
            $data['message']['holderAddr'] = $data['message']['recognizeeAddr'];
        }

        foreach ($data as $key => &$val) {
            $val = str_replace(array("\t", null), "", $val);
        }
        return $data;
    }

    public function getAccountData($account)
    {
        $data = array(
            '4403A00210' => [
                'agreenumStartDate' => '2020-03-12',
            ],

        );

        return $data[$account];
    }

    public function getClauseData($clauseId)
    {
        $clauseData = array(
            '1' => ['1', '217', 'COVERING AIR TRANSPORTATION ALL RISKS AS PER AIR TRANSPORTATION CARGO  INSURANCE CLAUSES (2018) OF THE PICC PROPERTY AND CASUALTY COMPANY  LIMITED. '], // 航空一切险
            '2' => ['1', '216', 'COVERING AIR TRANSPORTATION RISKS AS PER AIR TRANSPORTATION CARGO  INSURANCE CLAUSES (2018) OF THE PICC PROPERTY AND CASUALTY COMPANY  LIMITED. '], // 航空险
            '3' => ['2', '107', 'COVERING RISKS AS PER INSTITUTE CARGO CLAUSES (AIR) (EXCLUDING SENDINGS BY POST) DATED 1/1/82. '], // ICC(AIR)不含邮包

            '4' => ['1', '215', 'COVERING ALL RISKS AS PER OCEAN MARINE CARGO CLAUSES (2018) OF THE  PICC PROPERTY AND CASUALTY COMPANY LIMITED. '], // 海运一切险
            '5' => ['1', '210', 'COVERING WITH AVERAGE AS PER OCEAN MARINE CARGO CLAUSES (2018) OF THE  PICC PROPERTY AND CASUALTY COMPANY LIMITED. '], // 水渍险
            '6' => ['1', '212', 'COVERING F.P.A. AS PER OCEAN MARINE CARGO CLAUSES (2018) OF THE PICC  PROPERTY AND CASUALTY COMPANY LIMITED. '], // 平安险
            '7' => ['2', '104', 'COVERING MARINE RISKS AS PER INSTITUTE CARGO CLAUSES (A)  DATED 1/1/82. '], // ICC(A)
            '8' => ['2', '105', 'COVERING MARINE RISKS AS PER INSTITUTE CARGO CLAUSES (B)  DATED 1/1/82 '], // ICC(B)
            '9' => ['2', '106', 'COVERING MARINE RISKS AS PER INSTITUTE CARGO CLAUSES (C)  DATED 1/1/82. '], // ICC(C)
            '10' => ['1', '197', 'COVERING RISKS FOR FROZEN PRODUCTS AS PER OCEAN MARINE INSURANCE  CLAUSES (FROZEN PRODUCTS) (2009) OF THE PICC PROPERTY AND CASUALTY  COMPANY LIMITED. '], // 冷藏险
            '11' => ['1', '137', 'COVERING ALL RISKS FOR FROZEN PRODUCTS AS PER OCEAN MARINE INSURANCE  CLAUSES (FROZEN PRODUCTS) (2009) OF THE PICC PROPERTY AND CASUALTY  COMPANY LIMITED. '], // 冷藏一切险

            '12' => ['1', '215', 'COVERING ALL RISKS AS PER OCEAN MARINE CARGO CLAUSES (2018) OF THE  PICC PROPERTY AND CASUALTY COMPANY LIMITED. '], // 海运险
            '13' => ['1', '210', 'COVERING WITH AVERAGE AS PER OCEAN MARINE CARGO CLAUSES (2018) OF THE  PICC PROPERTY AND CASUALTY COMPANY LIMITED. '], //
            '14' => ['1', '212', 'COVERING F.P.A. AS PER OCEAN MARINE CARGO CLAUSES (2018) OF THE PICC  PROPERTY AND CASUALTY COMPANY LIMITED. '], // 平安险
            '15' => ['2', '104', 'COVERING MARINE RISKS AS PER INSTITUTE CARGO CLAUSES (A)  DATED 1/1/82. '], // ICC(A)
            '16' => ['2', '105', 'COVERING MARINE RISKS AS PER INSTITUTE CARGO CLAUSES (B)  DATED 1/1/82 '], // ICC(B)
            '17' => ['2', '106', 'COVERING MARINE RISKS AS PER INSTITUTE CARGO CLAUSES (C)  DATED 1/1/82. '], // ICC(C)

            '18' => ['1', '222', 'COVERING OVERLAND TRANSPORTATION ALL RISKS AS PER OVERLAND  TRANSPORTATION CARGO INSURANCE CLAUSES (TRAIN, TRUCK) (2018) OF THE  PICC PROPERTY AND CASUALTY COMPANY LIMITED. '], // 陆运一切险
            '19' => ['1', '223', 'COVERING OVERLAND TRANSPORTATION RISKS AS PER OVERLAND TRANSPORTATION  CARGO INSURANCE CLAUSES (TRAIN, TRUCK) (2018) OF THE PICC PROPERTY  AND CASUALTY COMPANY LIMITED. '], // 陆运险
            '20' => ['1', '198', 'COVERING RISKS FOR FROZEN PRODUCTS AS PER OVERLAND TRANSPORTATION  CARGO INSURANCE CLAUSES (FROZEN PRODUCTS) (2009) OF THE PICC  PROPERTY AND CASUALTY COMPANY LIMITED.'], // 陆运冷藏险
        );

        return $clauseData[$clauseId];
    }

    public function getAdditionClause($clauseId)
    {
        $additionClause = array(
            '1' => ['89', 'INCLUDING WAR RISKS AS PER AIR TRANSPORTATION CARGO WAR RISKS CLAUSES (2009) OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED.'],
            '2' => ['98', 'INCLUDING STRIKE RISKS AS PER CARGO STRIKE CLAUSE (2009) OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '3' => ['0', '0'],
            '4' => ['206', 'INCLUDING WAR RISKS AS PER INSTITUTE WAR CLAUSES (AIR CARGO) (EXCLUDING SENDINGS BY POST) DATED 1/1/82'],
            '5' => ['209', 'INCLUDING STRIKES RISKS AS PER INSTITUTE STRIKES CLAUSES (AIR CARGO) DATED 1/1/82'],
            '6' => ['88', 'INCLUDING WAR RISKS AS PER OCEAN MARINE CARGO WAR RISKS CLAUSES (2009) OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED.'],
            '7' => ['98', 'INCLUDING STRIKE RISKS AS PER CARGO STRIKE CLAUSE (2009) OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '8' => ['100', 'INCLUDING RISKS AS PER THEFT, PILFERAGE AND NON-DELIVERY CLAUSES(INSURED VALUE)(2009) OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '9' => ['101', 'INCLUDING RISKS AS PER FRESH WATER & /OR RAIN DAMAGE CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '10' => ['102', 'INCLUDING RISKS AS PER SHORTAGE CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '11' => ['103', 'INCLUDING RISKS AS PER INTERMIXTURE & CONTAMINATION CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '12' => ['104', 'INCLUDING RISKS AS PER LEAKAGE CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '13' => ['105', 'INCLUDING RISKS AS PER CLASH & BREAKAGE CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '14' => ['106', 'INCLUDING RISK AS PER TAINT OF ODOUR CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '15' => ['107', 'INCLUDING RISKS AS PER SWEAT & HEATING CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '16' => ['108', 'INCLUDING RISK AS PER HOOK DAMAGE CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '17' => ['109', 'INCLUDING RISKS AS PER BREAKAGE OF PACKING CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '18' => ['110', 'INCLUDING RISK AS PER RUST CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
            '19' => ['203', 'INCLUDING WAR RISKS AS PER INSTITUTE WAR CLAUSES (CARGO) DATED 1/1/82'],
            '20' => ['208', 'INCLUDING STRIKES RISKS AS PER INSTITUTE STRIKES CLAUSES (CARGO) DATED 1/1/82'],
            '21' => ['113', 'INCLUDING RISKS AS PER ON DECK CLAUSE（2009）OF THE PICC PROPERTY AND CASUALTY COMPANY LIMITED'],
        );

        return $additionClause[$clauseId];
    }

    public function getRangeId($insureType)
    {
        $data = [
            'export' => '16', // 出口远洋
            'import' => '15', // 进口
        ];

        return $data[$insureType];
    }

    public function getGoodsType($goodsType)
    {


        $goodsTypeArr = array(
            '纺织原料及纺织制品'=> ['轻纺、文体、日用品、工艺品','804'],
            '机器设备及其零件、附件'=> ['电器、机械、运输工具、设备类','849'],
            '纸质品'=> ['木、木浆及木制品；纸、纸浆、纸板及其制品','880'],
            '贱金属及其制品、五金类'=> ['金属原料、矿产及建材类、玻璃及其制品','741'],
            '食品'=> ['粮食、食品、果蔬、饮料、烟草及饲料','624'],
            '化学工业及其相关工业产品'=> ['石油、化工原料及其制品','745'],
            '塑料及其制品;橡胶及其制品'=> ['轻纺、文体、日用品、工艺品','804'],
            '木制品、木材'=> ['木、木浆及木制品；纸、纸浆、纸板及其制品','880'],
            '仪器、乐器、医疗设备及零件、附件（非精密仪器）'=> ['电器、机械、运输工具、设备类','849'],
            '杂项制品'=> ['其它','899'],
            '电脑/平板电脑、手机等电子产品'=> ['轻纺、文体、日用品、工艺品','804'],

            '水果'=> ['粮食、食品、果蔬、饮料、烟草及饲料','624'],
            '蔬菜'=> ['粮食、食品、果蔬、饮料、烟草及饲料','624'],
            '鲜花'=> ['粮食、食品、果蔬、饮料、烟草及饲料','624'],
            '其他鲜活'=> ['粮食、食品、果蔬、饮料、烟草及饲料','624'],

            '玻璃及玻璃制品'=> ['金属原料、矿产及建材类、玻璃及其制品','738'],
            '大理石、瓷砖、石材及其制品'=> ['金属原料、矿产及建材类、玻璃及其制品','738'],
            '陶瓷制品'=> ['金属原料、矿产及建材类、玻璃及其制品','738'],
            '太阳能电池板'=> ['金属原料、矿产及建材类、玻璃及其制品','738'],
            '其他易碎品'=> ['金属原料、矿产及建材类、玻璃及其制品','738'],

            '新车'=> ['电器、机械、运输工具、设备类','849'],
            '二手车'=> ['电器、机械、运输工具、设备类','849'],

            '冷藏食品、农副产品'=> ['粮食、食品、果蔬、饮料、烟草及饲料','624'],
            '冷藏水产品'=> ['粮食、食品、果蔬、饮料、烟草及饲料','624'],
            '其他冷藏品'=> ['其它','899'],

            '9类危险品'=> ['石油、化工原料及其制品','745'],
            '8类危险品'=> ['石油、化工原料及其制品','745'],
            '6类危险品'=> ['石油、化工原料及其制品','745'],
            '5类危险品'=> ['石油、化工原料及其制品','745'],
            '4类危险品'=> ['石油、化工原料及其制品','745'],
            '3类危险品'=> ['石油、化工原料及其制品','745'],
            '2类危险品'=> ['石油、化工原料及其制品','745'],

            '煤、炭'=> ['金属原料、矿产及建材类、玻璃及其制品','738'],
            '矿石、矿粉、矿砂等'=> ['金属原料、矿产及建材类、玻璃及其制品','738'],
            '其他矿产资源类'=> ['金属原料、矿产及建材类、玻璃及其制品','738'],

            '对运输有防震动、防倾斜、防尘等特殊要求的仪器'=> ['电器、机械、运输工具、设备类','849'],
            '目的地国家无法维修的仪器'=> ['电器、机械、运输工具、设备类','849'],
            '单件货物保额超过RMB200万元的仪器'=> ['电器、机械、运输工具、设备类','849'],

            '农作物、粮食类' => ['粮食、食品、果蔬、饮料、烟草及饲料','624'],

            '超长、超宽、超重' => ['电器、机械、运输工具、设备类','849'],
            '特殊的绑扎固定及保持重心的平衡' => ['电器、机械、运输工具、设备类','849'],

            '电脑/平板电脑、手机等电子产品' => ['轻纺、文体、日用品、工艺品','804'],
        );

        isset($goodsTypeArr[$goodsType]) ? $result = $goodsTypeArr[$goodsType] : $result = $goodsTypeArr['杂项制品'];
        return $result;
    }

    public function getPackType($packType)
    {
        $packTypeArr = array(
            '裸装' => '',
            '散装' => '',
            '纸箱' => '纸箱',
            '木箱' => '木箱',
            '捆包' => '包',
            '袋装' => '袋',
            '篓装' => '篓',
            '托盘' => '托盘',
            '桶装' => '桶',
            '罐装' => '罐',
        );
        isset($packTypeArr[$packType]) ? $result = $packTypeArr[$packType] : $result = $packTypeArr['裸装'];
        return $result;
    }

    public function getTransportTypeId($transportType)
    {
        $transportTypeArr = array(
            '水路运输' => '3',
            '航空运输' => '4',
            '公路运输' => '5',
            '铁路运输' => '6',
        );

        return $transportTypeArr[$transportType];
    }

    public function getCurrency($currency)
    {
        $data = [
            '美元' => ['2', 'USD'],
            '人民币' => ['1', 'CNY'],
            '英镑' => ['6', 'GBP'],
            '日元' => ['4', 'JPY'],
            '港币' => ['7', 'HKD'],
            '欧元' => ['3', 'EUR'],
            '澳大利亚元' => ['12', 'AUD'],
            '加拿大元' => ['9', 'CAD'],
            '新加坡元' => ['8', 'SGD'],
            '马来西亚林吉特币' => ['22', 'MYR'],
            '瑞士法郎' => ['14', 'CHF'],
            '韩元' => ['28', 'KRW'],
            '瑞典克朗' => ['16', 'SEK'],
            '新西兰元' => ['24', 'NZD'],
        ];

        return $data[$currency];
    }
}
