<?php

namespace App\Http\Controllers\Api;

use App\Models\GuorenCity;

class STARRController
{

    public function approval($ins, $product, $settings)
    {

        $Sex = 'F';
        if ($ins['recognizeeIdenty'] != '' && strlen($ins['recognizeeIdenty']) > 15) {
            // 获取生日、性别
            $idcard = $ins['recognizeeIdenty'];
            $birth = strlen($idcard) == 15 ? ('19' . substr($idcard, 6, 6)) : substr($idcard, 6, 8);
            $sex = substr($idcard, (strlen($idcard) == 15 ? -2 : -1), 1) % 2 ? '1' : '0'; //1为男 2为女

            if ($sex == 1) {
                $Sex = 'M';
            }
        }

        //被保人及证件类型
        if (strstr($ins['recognizeeName'], '公司')) {
            $PartType  = 'QY';
            $CardType = '104';
            $birth = '';
        } else {
            $PartType  = 'GR';
            $CardType = '1';
        }


        // 起运地、目的地
        $StartName = explode('|||', $ins['fromLoc'])[0];
        $ToName    = explode('|||', $ins['toLoc'])[0];

        if (empty($ins['holderName'])) {
            $ins['holderName']  = $ins['recognizeeName'];
            $ins['holderPhone'] = $ins['recognizeePhone'];
        }

        // 系统参数
        // $sys_params = explode('|#|', $settings[0]);

        $PartnerCode = "YOUFU";
        $PartnerKey = "km2a0f13c9dkb8";
        $TransCode = $this->create_guid();
        $Key = strtoupper(md5($PartnerCode . $PartnerKey . $TransCode));

        $data = array(
            "config" => array(
                "account" => '',
                "password" => '',
            ),
            "message" => array(
                "AllPrem" => $ins['premium'],  //所有保单加总保费，不可空，出单的时候需要进行检查. 
                "ProductCode" => 60021, //产品编号，东瑞给出 60021蔬果险，费率万2.2
                "TransCode" => $TransCode, //每次报文的唯一码，建议采用Guid生成，全球唯一（当此报文进入处理中，我方后台会对这个唯一码进行锁定，以免重复出单），不可空
                "PolicyList" => array(
                    array(
                        "DurationType" => "M", //保障单位，D代表日，Y代表年，S代表一次保障，不可空
                        "SA" => $ins['insuredAmount'],  //保额，单位元，可为空（有的险种不需要保额）
                        "PolicyHolder" => array( //投保人
                            "CardNumber" => $ins['recognizeeIdenty'],  //证件号码，不可空
                            "CardType" => $CardType,  //证件类型：(1-居民身份证 2-军人证 3-护照 4-出生证 5-异常身份证 6-回乡证 7- 居民户口薄 8- 军官证)，默认为1
                            "Name" => $ins['holderName'],  //姓名，不可空
                            "MobileNum" => $ins['holderPhone'], //手机号码，不可为空
                            "ContactMobile" => $ins['holderPhone'], //企业手机号码，投保人为企业不能为空
                            "PartType" => $PartType, // 组织类型，GR=>个人，QY=>企业
                            "Birthday" => $birth,  //投保人为个人时不能为空
                            "Sex" => $Sex, //M男，F女,默认女
                            "Email" => "<EMAIL>" //邮箱
                        ),
                        "AllPrem" => $ins['premium'], //单个保单保费，不可空。ALLPrem=SA*费率
                        "StartTime" => date('Y-m-d H:i:s', $ins['departureDate'] + 24 * 60 * 60), //保单起保日期，格式为：yyyy-MM-dd HH:mm:ss，后续时间可以省略，默认为00:00:00，不可空，一般是T+1，但有的险种会有特别，需要提前沟通
                        "Duration" => 1, //保障单位数，与DurationType相结合使用，在这个Demo中，指2天，既保险开始时间是：2017-02-28 00:00:00，结束日期是：2017-02-29 23:59:59，不可空
                        "ExtData" => array( //特别配置
                            "CargoItem_Description" => $ins['goodsName'], //保险货运项目
                            "CargoPackageAndCount_Description" => $ins['quantity'], //包装及数量
                            "CargoTransportWay_Description" => "3", //运输方式，1水运，2航空，3公路，4铁路，5邮包，6联运
                            "VehiclePlateNo" => $ins['transport'], //车牌号
                            "CargoTransportStartDate" => date('Y-m-d', $ins['departureDate']), //起运时间
                            "CargoStartSite_Name" => $StartName, //起运地名称
                            "CargoTargetSite_Name" => $ToName, //目的地名称
                            "WayBillNo" => $ins['orderNo'] . '/' . $ins['invNo'] //运单号
                        ),
                        "Insured" => array( //被保人
                            array(
                                "CardNumber" => $ins['recognizeeIdenty'],  //证件号码，不可空
                                "CardType" => "1", //证件类型：(1-居民身份证 2-军人证 3-护照 4-出生证 5-异常身份证 6-回乡证 7- 居民户口薄 8- 军官证)，默认为1
                                "Sex" => $Sex, //M男，F女,默认女
                                "PartType" => $PartType, //组织类型，GR=>个人，QY=>企业
                                "Name" => $ins['recognizeeName'], //姓名，不可空
                                "PhRelToIns" => "5" //投保人与被保人的关系，5：本人，3：父母，0：无关或者不确定，2：子女，1：配偶，6：其他，不可空
                            )
                        )
                    )
                ),
                "OperationType" => "006", //操作类型，若由东瑞处理支付，则为001，若直接出单则为006，不可空
                "PartnerCode" => $PartnerCode, //合作伙伴代码，由东瑞进行配置，并给到合作伙伴，原则上测试环境与生产环境保持一致，不可空
                "Date" => date('Y-m-d H:i:s', time()), //合作伙伴方生成该报文的时间，不可空
                "Key" => $Key, //通过公式MD5(PartnerCode+PartnerKey+TransCode)得出，其中PartnerKey需要东瑞给到合作伙伴，生产和测试会不一样，MD5方式为GBK码.
                "PartnerKey" => $PartnerKey,
                "PayTreatmentMethod" => "2" //支付方式，1代表见费，2代表非见费
            )
        );


        /* foreach ($data as &$val) {
            $val = str_replace(array("\r\n", "\r", "\n", "\t"), "", $val);
        } */

        return $data;
    }

    function create_guid(){
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $hyphen = chr(45); // "-"
        $uuid = substr($charid, 0, 8) . $hyphen
            . substr($charid, 8, 4) . $hyphen
            . substr($charid, 12, 4) . $hyphen
            . substr($charid, 16, 4) . $hyphen
            . substr($charid, 20, 12) ;
        return $uuid;
    }

    
}
