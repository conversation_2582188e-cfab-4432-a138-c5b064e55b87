<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use JWTAuth;
use <PERSON>mon\JWTAuth\Exceptions\JWTException;
use App\Models\Platform;

class AuthController extends ApiController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function login(Request $request)
    {
        $credentials['app_id'] = $request->input('app_id');
        $credentials['secret_key'] = $request->input('secret_key');
        $jwt_token = null;
        
        $platform = Platform::where($credentials)->first();
        if(!$platform){
            return response()->json([
                'success' => false,
                'message' => '接口未开通或用户不存在',
            ], 401);
        }

        try {
            $jwt_token = JWTAuth::fromUser($platform);
            return response()->json([
                'success' => true,
                'token' => $jwt_token,
            ]);
        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => '服务器内部错误',
            ], 500);
        }
    }

    public function logout(Request $request)
    {
        $this->validate($request, [
            'token' => 'required'
        ]);

        try {
            JWTAuth::invalidate($request->token);

            return response()->json([
                'success' => true,
                'message' => 'User logged out successfully'
            ]);
        } catch (JWTException $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Sorry, the user cannot be logged out'
            ], 500);
        }
    }

    public function getAuthUser(Request $request)
    {
        $this->validate($request, [
            'token' => 'required'
        ]);

        $user = JWTAuth::authenticate($request->token);

        return response()->json(['user' => $user]);
    }
}
