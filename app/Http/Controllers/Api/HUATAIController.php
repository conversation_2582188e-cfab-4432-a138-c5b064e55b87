<?php

namespace App\Http\Controllers\Api;

class HUATAIController
{

    public function approval($policy, $product, $settings)
    {
        // 技术配置 用户名 密码 key 险种缩写 [附加盗抢]
        $sysParams = explode('|#|', $settings[0]);
        $params = json_decode($product['config'], true);

        $transportType = $this->getTransportType($policy['transportTypeID'], $policy['loadType']);
        $goodsType = $this->getGoodsType($policy['goodsType']);
        $clause = $this->getRdrCde($sysParams[3]);
        $recognizeePhone = $policy['recognizeePhone'] ? $policy['recognizeePhone'] : 18926805333;

        $xmlstr = '<?xml version="1.0" encoding="UTF-8"?>
                <!--无投保申请直接出单-->
                <Policy>
                    <General>
                        <!--公共信息-->
                        <IssueTime>' . date('Y-m-d\TH:i:s') . '</IssueTime>
                        <!--*出单时间 FORMAT YYYY-MM-DDTHH24:MI:SS-->
                        <SerialNumber>' . $policy['orderNo'] . '</SerialNumber>
                        <!--流水号-->
                        <InsurancePolicy></InsurancePolicy>
                        <!--*保单号 默认空值由我方返回保单号-->
                        <InsuranceCode>3601</InsuranceCode><!--参数: 险种清单-->
                        <!--*险种代码 写死-->
                        <InsuranceName>国内水路、陆路货物运输保险</InsuranceName><!--参数: 险种清单-->
                        <!--*险种名称 写死-->
                        <EffectivTime>' . date('Y-m-d', $policy['departureDate']) . '</EffectivTime>
                        <!--*保险起期=起运时间-->
                        <TerminalTime>' . date('Y-m-d', strtotime('+30 days', $policy['departureDate'])) . '</TerminalTime>
                        <!--*保险止期-->
                        <Copy>1</Copy>
                        <!--*份数-->
                        <SignTM>' . date('Y-m-d') . '</SignTM>
                        <!--*签单时间-->
                    </General>
                    <Freight>
                        <Sign>' . $policy['freightNo'] . '</Sign>
                        <!--*货物标记-->
                        <PackAndQuantity>' . $policy['pack'] . ';'. $policy['quantity'] . '</PackAndQuantity>
                        <!--*包装及数量-->
                        <FregihtItem>' . $policy['goodsName'] . '</FregihtItem>
                        <!-- *货物项目-->
                        <InvoiceNumber>' . $policy['invNo'] . '</InvoiceNumber>
                        <!--发票号 -->
                        <BillNumber>' . $policy['freightNo'] . '</BillNumber>
                        <!--*提单号 -->
                        <FreightType>' . $goodsType[0] . '</FreightType><!--参数: 货物类型SX0014-->
                        <!--*货物类型（编码） -->
                        <FreightDetail>' . $goodsType[1] . '</FreightDetail><!--参数: 货物类型SX0014-->
                        <!--*二级货物明细（编码） -->
                        <InvoiceMoney>' . $policy['insuredAmount'] . '</InvoiceMoney>
                        <!-- *发票金额-->
                        <InvoiceBonus>1</InvoiceBonus>
                        <!--*加成 -->
                        <Amt>' . $policy['insuredAmount'] . '</Amt>
                        <!--*保险金额 -->
                        <AmtCurrency>01</AmtCurrency><!--参数：102 -->
                        <!--*保险金额币种 （编码）-->
                        <ExchangeRate>1</ExchangeRate>
                        <!-- *汇率-->
                        <ChargeRate>' . $policy['ratio'] / 10 . '</ChargeRate>
                        <!--* 费率‰-->
                        <Premium>' . $policy['premium'] . '</Premium>
                        <!--*保险费 -->
                        <PremiumCurrency>01</PremiumCurrency>
                        <!--默认01 RMB(01:人民币;02:港币;03:美元;04-英镑;12-欧元)-->
                        <!--*保险费币种 （编码）-->
                        <PremiumPrit>02</PremiumPrit><!--参数：保费打印SX0019 -->
                        <!--* 01 显示保费 02 按约定 03 已支付-->
                        <!--* 保费打印-->
                        <TransportType>' . $transportType[0] . '</TransportType><!--参数: 运输方式SX0015-->
                        <!-- *运输方式（编码）-->
                        <TransportDetail>' . $transportType[1] . '</TransportDetail><!--参数: 运输方式SX0015-->
                        <!-- *运输方式明细（编码）-->
                        <TrafficNumber>' . $policy['transport'] . '</TrafficNumber>
                        <!--*船名航班车号 -->
                        <FlightsCheduled></FlightsCheduled>
                        <!-- 航次-->
                        <BuildYear></BuildYear>
                        <!-- 建造年份-->
                        <FromCountry>HTC01</FromCountry><!--参数: 检查人国家地区HTC560130-->
                        <!--*起运地国家（编码） -->
                        <FromArea>' . $policy['fromLoc'] . '</FromArea>
                        <!--*起运地 -->
                        <PassPort>' . $policy['viaLoc'] . '</PassPort>
                        <!--途径港 -->
                        <ToContry>HTC01</ToContry><!--参数: 检查人国家地区HTC560130-->
                        <!--*目的地国家 （编码）-->
                        <ToArea>' . $policy['toLoc'] . '</ToArea>
                        <!--*目的地-->
                        <SurveyAdrID>501422495713</SurveyAdrID><!--参数: 检查人清单-->
                        <!--*查勘地址地址编码 广东分公司的查勘编码 写死-->
                        <SurveyAdr>17/F,Block B,Center Plaza 161 Linhexi Av.,Tianhe District, Guangzhou TEL:4006095509 FAX: 020-87567201</SurveyAdr><!--参数: 检查人清单-->
                        <!--查勘地址内容-->
                        <TrantsTool></TrantsTool>
                        <!--转运工具 -->
                        <StartTM>' . date('Y-m-d\TH:i:s', $policy['departureDate']) . '</StartTM>
                        <!--*起运时间-->
                        <EndTM>' . date('Y-m-d\TH:i:s', strtotime("+30 days", $policy['departureDate'])) . '</EndTM>
                        <!--*预计抵达时间-->
                        <OriginalSum>1</OriginalSum>
                        <!--*正文份数-->
                        <DatePritType>1</DatePritType><!-- 1:中文 2:英文 3:提单号 4:AS PER AWB -->
                        <!--*日期打印方式(编码） -->
                        <Mark></Mark>
                        <!--特别约定 -->
                        <CreditNO></CreditNO>
                        <!--信用证编码-->
                        <CreditNODesc></CreditNODesc>
                        <!--信用证描述-->
                        <TrailerNum></TrailerNum>
                        <!--挂车车牌号-->
                        <PayAddr></PayAddr>
                        <!--赔款偿付地-->
                    </Freight>
                    <InsureRdrs>
                        <!--险别信息 -->
                        '.$this->getRdr($clause, isset($sysParams[4]) ?? '').'
                    </InsureRdrs>
                    <Applicant>
                        <!--投保人信息-->
                        <AppCode></AppCode>
                        <!--投保人编码,固定投保人填写华泰提供的编码，投保人不固定则为空-->
                        <ApplicantName>' . $policy['holderName'] . '</ApplicantName>
                        <!--*投保人名称-->
                        <Gender></Gender>
                        <!-- 性别，个人客户必填, 1：男；2：女；9：未说明 -->
                        <Birthday></Birthday>
                        <!--出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填-->
                        <IDType>99</IDType><!--参数: 证件类型-->
                        <!--*证件类型 注意：机构客户，必选填写 97（税务登记证）-->
                        <ID>' . $policy['holderPhone'] . '</ID>
                        <!--*证件号码-->
                        <Phone></Phone>
                        <!--固定电话-->
                        <Cell></Cell>
                        <!--联系手机-->
                        <Zip></Zip>
                        <!--邮政编码-->
                        <Address></Address>
                        <!--地址-->
                        <Email></Email>
                        <!--Email-->
                        <!--*注意：增值税发票：1、证件类型：97（税务登记证）；2、证件号码：纳税人识别号；3、证件类型、证件号码、开户银行、银行账号、电话号码、地址必填；-->
                        <TaxDeduct>0</TaxDeduct>
                        <!--是否需要增值税专用发票 0：否 1：是-->
                        <AccountBank></AccountBank>
                        <!--开户银行-->
                        <AccountNumber></AccountNumber>
                        <!--银行账号-->
                    </Applicant>
                    <Insured>
                        <!--被保险人信息-->
                        <InsuredName>' . $policy['recognizeeName'] . '</InsuredName>
                        <!--*被保险人名称-->
                        <Gender></Gender>
                        <!-- 性别，个人客户必填, 1：男；2：女；9：未说明 -->
                        <Birthday></Birthday>
                        <!--出生日期，证件类型：居民身份证、军官证、护照、驾驶执照、返乡证、港澳通行证、台湾通行证、中介业务员职业证号、其他证件， 生日必填-->
                        <IDType>99</IDType><!--参数: 证件类型-->
                        <!--*证件类型-->
                        <ID>' . $recognizeePhone . '</ID>
                        <!--*证件号码-->
                        <Phone></Phone>
                        <!--固定电话-->
                        <Cell></Cell>
                        <!--联系手机-->
                        <Zip></Zip>
                        <!--邮政编码-->
                        <Address>' . $policy['recognizeeAddr'] . '</Address>
                        <!--地址-->
                        <Email></Email>
                        <!--Email-->
                    </Insured>
                </Policy>';

        $data = array(
            "config" => array(
                "account" => $params['username'],
                "password" => $params['password']
            ),
            "message" => base64_encode($xmlstr),
            "settings" => $sysParams
        );

        return $data;
    }

    protected function getGoodsType($goodsType)
    {
        $data = array(
            '纺织原料及纺织制品'=> ['SX001411','SX00140065'],
            '机器设备及其零件、附件'=> ['SX001416','SX00140087'],
            '纸质品'=> ['SX001410','SX00140050'],
            '贱金属及其制品、五金类'=> ['SX001415','SX00140083'],
            '食品'=> ['SX001404','SX00140019'],
            '化学工业及其相关工业产品'=> ['SX001406','SX00140040'],
            '塑料及其制品;橡胶及其制品'=> ['SX001407','SX00140041'],
            '木制品、木材'=> ['SX001409','SX00140046'],
            '仪器、乐器、医疗设备及零件、附件（非精密仪器）'=> ['SX001418','SX00140092'],
            '杂项制品'=> ['SX001420','SX00140098'],
            '电脑/平板电脑、手机等电子产品'=> ['SX001416','SX00140101'],

            '水果'=> ['SX001402','SX00140008'],
            '蔬菜'=> ['SX001402','SX00140007'],
            '鲜花'=> ['SX001402','SX00140006'],
            '其他鲜活'=> ['SX001402','SX00140012'],

            '玻璃及玻璃制品'=> ['SX001413','SX00140072'],
            '大理石、瓷砖、石材及其制品'=> ['SX001413','SX00140070'],
            '陶瓷制品'=> ['SX001413','SX00140071'],
            '太阳能电池板'=> ['SX001413','SX00140072'],
            '其他易碎品'=> ['SX001413','SX00140072'],

            '新车'=> ['SX001417','SX00140089'],
            '二手车'=> ['SX001417','SX00140089'],

            '冷藏食品、农副产品'=> ['SX001404','SX00140019'],
            '冷藏水产品'=> ['SX001401','SX00140003'],
            '其他冷藏品'=> ['SX001401','SX00140002'],

            '9类危险品'=> ['SX001406','SX00140040'],
            '8类危险品'=> ['SX001406','SX00140040'],
            '6类危险品'=> ['SX001406','SX00140040'],
            '5类危险品'=> ['SX001406','SX00140040'],
            '4类危险品'=> ['SX001406','SX00140040'],
            '3类危险品'=> ['SX001406','SX00140040'],
            '2类危险品'=> ['SX001406','SX00140040'],

            '煤、炭'=> ['SX001405','SX00140028'],
            '矿石、矿粉、矿砂等'=> ['SX001405','SX00140027'],
            '其他矿产资源类'=> ['SX001405','SX00140027'],

            '对运输有防震动、防倾斜、防尘等特殊要求的仪器'=> ['SX001418','SX00140092'],
            '目的地国家无法维修的仪器'=> ['SX001418','SX00140092'],
            '单件货物保额超过RMB200万元的仪器'=> ['SX001418','SX00140092'],
        );
        return $data[$goodsType];
    }

    protected function getTransportType($transportTypeId, $loadType)
    {
        $data = [
            // 水路
            '3' => [
                '厢式货车' => ['SX001501', '01'],
                '非厢式货车' => ['SX001501', '05'],
            ],
            // 航空
            '4' => [
                '厢式货车' => ['SX001503', '01'],
                '非厢式货车' => ['SX001503', '02'],
            ],
            // 公路
            '5' => [
                '厢式货车' => ['SX001502', '01'],
                '非厢式货车' => ['SX001502', '02'],
            ],
            // 铁路
            '6' => [
                '厢式货车' => ['SX001505', '01'],
                '非厢式货车' => ['SX001505', '02'],
            ],
        ];
        return $data[$transportTypeId][$loadType];
    }

    protected function getRdrCde($clause)
    {
        $data = [
            'JBX' => [
                'SX300211',
                '基本险'
            ],
            'ZHX' => [
                'SX300212',
                '综合险'
            ]
        ];

        return $data[$clause];
    }

    protected function getRdr($clause, $DQX)
    {
        $xml = '
        <InsureRdr>
            <RdrCde>'. $clause[0] .'</RdrCde><!--参数：险种清单合同结构码  -->
            <!--*编码 -->
            <RdrName>'. $clause[1] .'</RdrName>
            <!--*名称 -->
            <RdrDesc></RdrDesc>
            <!--描述 -->
        </InsureRdr>
        ';
        if($DQX){
            $xml.='
            <InsureRdr>
                <RdrCde>SX400069</RdrCde><!--参数：险种清单合同结构码  -->
                <!--*编码 -->
                <RdrName>盗抢险条款</RdrName>
                <!--*名称 -->
                <RdrDesc></RdrDesc>
            <!--描述 -->
            </InsureRdr>
            ';
        }
        return $xml;
    }

}
