<?php

namespace App\Http\Controllers\Api;

class PICCController
{

    public function approval($policy, $product, $settings)
    {
        $policy['departureDate'] = strtotime($policy['departureDate']);
        $startDate = date('Y-m-d', $policy['departureDate']);
        $startTime = date('H', $policy['departureDate']);

        $effDate = date('Y-m-d');
        $effTime = date('H');
        if (strtotime($startDate) <= strtotime(date('Y-m-d'))) {
            $startDate = $effDate;
            $startTime = $effTime;
            $departure = $startDate . ' ' . $startTime . ':00';
            $departure = strtotime("$departure +1 hour");
            $startDate = date('Y-m-d', $departure);
            $startTime = date('H', $departure);
        } else {
            $startTime = '00';
        }

        $sys_params = explode('|#|', $settings[0]);
        $params = json_decode($product['config'], true);

        if($sys_params[4] == 6){
            $glauses = '国内水路、陆路货运基本险';
        }
        if($sys_params[4] == 1){
            $glauses = '国内公路货运保险';
        }

        $data = array(
            "config" => array(
                "account"  => $params['username'],
                "password" => $params['password'],
            ),
            "message" => array(
                "holderAddr"          => $policy['holderAddr'],
                "holderName"          => $policy['holderName'],
                "recognizeeAddr"      => $policy['recognizeeAddr'],
                "recognizeeName"      => $policy['recognizeeName'],
                "invNo"               => $policy['orderNo'] . '/' . $policy['invNo'],
                "goodsName"           => $policy['goodsName'],
                "quantity"            => $policy['quantity'],
                "transport"           => $policy['transport'],
                "fromLoc"             => $policy['fromLoc'],
                "viaLoc"              => $policy['viaLoc'],
                "toLoc"               => $policy['toLoc'],
                "departureDate"       => $startDate,
                "departureTime"       => $startTime,
                "effDate"             => $effDate,
                "effTime"             => $effTime,
                "insuredAmount"       => sprintf('%.2f', floatval($policy['insuredAmount'])),
                "documentType"        => "其他",
                "recognizeeIdenty"    => "",
                "recognizeePhone"     => "",
                "recognizeeOrg"       => "",
                "weights"             => "",                                                        // 重量
                "goodsTypeID"         => $sys_params[3],
                "pack"                => "",
                "transportTypeID"     => "5",                                                       // 运输方式
                "transportNo"         => "",
                "glausesID"           => $sys_params[4],                                            // 主险条款
                "additive"            => "",
                "ratio"               => $policy['ratio'],                                          // 费率
                "premium"             => $policy['premium'],                                        // 保费
                "deductible"          => $settings[1],                                              // 绝对免赔率
                "remark"              => $settings[2],                                              // 特别约定
                "endTypeID"           => "40",
                "postalModeId"        => "57",
                "invHead"             => "",
                "extUsrNo"            => "",
                "shipCName"           => "",
                "shipEName"           => "",
                "fleetNo"             => "",
                "itemNo"              => "0",
                "stepHull"            => "无船级",
                "shipFlag"            => "",
                "associate"           => "",
                "makeYearMonth"       => "2019-09-14",
                "countryCode"         => "",
                "makeFactory"         => "",
                "templateDesc"        => "",
                "consigneeInfo"       => "",
                "neijian"             => "",
                "contactPerson"       => "",
                "contactTel"          => "",
                "postalCode"          => "",
                "postalAddr"          => "",
                "glauses"             => $glauses,
                "packQty"             => $policy['quantity'],
                "way"                 => "",
                "ifPackage"           => "",
                "contractPerson"      => "",
                "contractTel"         => "",
                "additiveNo"          => "",
                "invRefNo"            => $policy['orderNo'] . '/' . $policy['invNo'],
                "policyNo"            => "",
                "policyNoLong"        => "",
                "insuranceID"         => "1",
                "endCurrencyID"       => "1",
                "currencyID"          => "1",
                "policyNoRemark"      => "",
                "changeNo"            => "",
                "mianID"              => "",
                "mianDesc"            => "",
                "contractNo"          => "",
                "inDelayApplay"       => "",
                "agreenumStartDate"   => $params['agreenumStartDate'],
                "policyNoHead"        => $params['policyNoHead'],
                "myfileFileName"      => ""
            )
        );

        if (empty($data['message']['holderName'])) {
            $data['message']['holderName'] = $data['message']['recognizeeName'];
            $data['message']['holderAddr'] = $data['message']['recognizeeAddr'];
        }

        foreach ($data as &$val) {
            $val = str_replace(array("\r\n", "\r", "\n", "\t"), "", $val);
        }
        return $data;
    }
}
