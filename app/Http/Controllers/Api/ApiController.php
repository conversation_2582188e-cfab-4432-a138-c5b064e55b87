<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Platform;

class ApiController extends Controller
{
    function __construct()
    {
        \Config::set('jwt.user', Platform::class);
        \Config::set('auth.providers', ['users' => [
            'driver' => 'eloquent',
            'model' => Platform::class,
        ]]);
    }
}
