<?php

namespace App\Http\Controllers\Api;

class PICCController
{

    public function approval($policy, $product, $allSettings, $platform)
    {
        // 技术配置  账号 密码 大类 小类 主险 是否添加盗抢(0:否 1:是) 保单号 头
        $setting = explode("|#|",$allSettings[0]);
        $startDate = date('Y-m-d', $policy['departureDate']);
        $startTime = date('H', $policy['departureDate']);

        $effDate = date('Y-m-d');
        $effTime = date('H');
        $effMinute = date('i');
        if($effMinute > '50'){
            $effTime += 1;
        }
        if (strtotime($startDate) <= strtotime(date('Y-m-d'))) {
            $startDate = $effDate;
            $startTime = $effTime;
            $departure = $startDate . ' ' . $startTime . ':00';
            $departure = strtotime("$departure +1 hour");
            $startDate = date('Y-m-d', $departure);
            $startTime = date('H', $departure);
        } else {
            $startTime = '00';
        }

        $startTime = $startDate > date('Y-m-d') ? '00' : $effTime;

        // $sys_params = explode('|#|', $product['parameters']);
        $params = json_decode($product['config'], true);

        if($platform['id'] == 5){
            $invNo = $policy['invNo'] . ' / ' . $policy['freightNo'];
        }else{
            $invNo = $policy['orderNo'] . ' / ' . $policy['invNo'];
        }

        $accountData = $this->getAccountData($setting[0]);
        $clauseName = $this->getClauseName($setting[4]);
        $additionClause = $this->getAdditionClause($setting[5]);

        $data = array(
            "config" => array(
                "account" => $params['username'],
                "password" => $params['password'],
            ),
            "message" => array(
                "holderAddr"          => $policy['holderAddr'],  //投保人地址
                "holderName"          => $policy['holderName'],  //投保人名称
                "recognizeeAddr"      => $policy['recognizeeAddr'],  //被保人地址
                "recognizeeName"      => $policy['recognizeeName'],  //被保人名称
                "invNo"               => $invNo,    //发票号
                "goodsName"           => $policy['goodsName'],  //货物名称
                "quantity"            => $policy['quantity'],   //包装及数量
                "transport"           => $policy['transport'],  //运输工具名称 车牌号等
                "fromLoc"             => $policy['fromLoc'],    //起运地
                "viaLoc"              => $policy['viaLoc'],     //中转地
                "toLoc"               => $policy['toLoc'],      //目的地
                "departureDate"       => $startDate,    //起运时间 年月日
                "departureTime"       => $startTime,    //起运时间 小时
                "effDate"             => $effDate,  //签单时间 年月日
                "effTime"             => $effTime,  //签单时间 小时
                "insuredAmount"       => sprintf('%.2f', floatval($policy['insuredAmount'])),
//                "documentType" => "其他",  // 被保险人证件类型 写死
//                "recognizeeIdenty" => "",
//                "recognizeePhone" => "",
                "recognizeeOrg" => "",
                "weights" => "",
                "goodsTypeID" => $setting[3],  //小类
                "pack" => "",
                "transportTypeID" => isset($policy['transportTypeID']) ? $policy['transportTypeID'] : "5",
                "transportNo" => "",
                "glausesID" => $setting[4],  //主险条款
                "additive" => $additionClause, // 附加险内容
                "ratio" => $policy['ratio'] / 10,  //平台费率是万分之几,但是自动录单拿到的就只是整数,没有万分之,保险公司单位是千分之,所以需要除以10
                "premium" => $policy['premium'],  //保费
                "deductible" => $allSettings[1],  //免赔
                "remark" => $allSettings[2],  //特约
                "endTypeID" => "40",  //结算方式 写死
                "postalModeId" => "57",  //保单投递方式 写死
                "invHead" => "",
                "extUsrNo" => "",
                "shipCName" => "",
                "shipEName" => "",
                "fleetNo" => "",
                "itemNo" => "0", // 标的序号 写死
                "stepHull" => "无船级", //船级 写死
                "shipFlag" => "",
                "associate" => "",
                "makeYearMonth" => $startDate, //创建日期
                "countryCode" => "",
                "makeFactory" => "",
                "templateDesc" => "",
                "consigneeInfo" => "",
                "neijian" => "",
                "contactPerson" => "",
                "contactTel" => "",
                "postalCode" => "",
                "postalAddr" => "",
                "glauses" => $clauseName,
                "packQty" => $policy['quantity'],   //包装及数量
                "way" => "",
                "ifPackage" => "",
                "contractPerson" => "",
                "contractTel" => "",
                "additiveNo" => $setting[5].',', // 附加险
                "invRefNo" => $invNo,
                "policyNo" => "",
                "policyNoLong" => "",
                "insuranceID" => "1",  // ?投保类型 1:国内 2:进出口
                "endCurrencyID" => "1", //结算币种 写死 1:人民币
                "currencyID" => "1",  //投保币种 写死 1:人民币
                "policyNoRemark" => "",
                "changeNo" => "",
                "mianID" => "",
                "mianDesc" => "",
                "contractNo" => "",
                "inDelayApplay" => "",
                "agreenumStartDate" => $accountData['agreenumStartDate'],  // ?签约日期 与账号绑定
                "policyNoHead" => $setting[6],  // 保单号 头 与账号绑定
                "myfileFileName" => "",
            ),
            "settings"=>$setting
        );

        $clientData = $this->getClientData($policy, $platform);
        if($policy['holderName'] == $policy['recognizeeName']){
            $clientData['recognizeeIdenty'] = $clientData['holderIdenty'];
        }
        $data['message'] = array_merge($data['message'], $clientData);

        $data['message']['deductible'] = $allSettings[1];
        $data['message']['remark'] = $allSettings[2];

        if (empty($data['message']['holderName'])) {
            $data['message']['holderName'] = $data['message']['recognizeeName'];
            $data['message']['holderAddr'] = $data['message']['recognizeeAddr'];
        }

        foreach ($data as $key => &$val) {
            $val = str_replace(array("\r\n", "\r", "\n", "\t", null), "", $val);
        }
        return $data;
    }

    public function getAccountData($account)
    {
        $data = array(
            'yrwl02' => [
                'agreenumStartDate' => '2019-05-27',
            ],
            'yrwl01' => [
                'agreenumStartDate' => '2020-05-22',
            ],
            'cdwh1' => [
                'agreenumStartDate' => '2019-10-18',
            ],
            'cdwh2' => [
                'agreenumStartDate' => '2019-10-18',
            ],
            'cdwh3' => [
                'agreenumStartDate' => '2019-10-18',
            ],
            'wanheng' => [
                'agreenumStartDate' => '2020-05-04',
            ],
            'muzixing0001' => [
                'agreenumStartDate' => '2019-09-24',
            ],
            'muzixing02' => [
                'agreenumStartDate' => '2019-09-20',
            ],
            'muzixing03' => [
                'agreenumStartDate' => '2019-09-20',
            ],
            'muzixing4' => [
                'agreenumStartDate' => '2019-09-20',
            ],
            'muzixing05' => [
                'agreenumStartDate' => '2019-09-20',
            ],
            'muzixing06' => [
                'agreenumStartDate' => '2019-09-20',
            ],
            'quanqiutong01' => [
                'agreenumStartDate' => '2019-09-24',
            ],
            'quanqiutong2' => [
                'agreenumStartDate' => '2019-09-24',
            ],
            'sxdh' => [
                'agreenumStartDate' => '2020-07-14',
            ],
            'BAOCANGWANG02' => [
                'agreenumStartDate' => '2020-07-17',
            ],
            'BAOCANGWANG04' => [
                'agreenumStartDate' => '2020-10-16',
            ],
            'YNYJB2' => [
                'agreenumStartDate' => '2020-12-01',
            ],
            '4403A00210' => [
                'agreenumStartDate' => '2020-03-12',
            ],
            '210314yjb02' => [
                'agreenumStartDate' => '2021-01-09',
            ],
            'DEJUN' => [
                'agreenumStartDate' => '2022-03-23',
            ],


        );

        return $data[$account];
    }

    public function getClauseName($clauseId)
    {
        $data = array(
            '1' => '国内公路货运保险',
            '3' => '国内航空货运保险',
            '5' => '国内水路、陆路货运综合险',
            '6' => '国内水路、陆路货运基本险',
            '7' => '国内水路货运保险综合保险',
            '8' => '国内水路货运保险基本保险',
            '9' => '国内铁路货运保险综合保险',
            '10' => '国内铁路货运保险基本保险',
            '1000' => '国内鲜活货物运输保险条款（2009版）',
            '1001' => '国内邮（包）件保险条款（2009版）',
            '1002' => '滚装船运输保险条款',
            '1003' => '无车承运人责任保险条款',
            '1004' => '生鲜冷链物流保险条款（注册号：09IT2017000020028）',
        );

        return $data[$clauseId];
    }

    public function getAdditionClause($clauseId)
    {
        $data = [
          '1005' => '国内水路、陆路货物运输保险附加盗窃、抢劫保险条款',
          '1004' => '国内水路、陆路货物运输保险附加盗窃、抢劫保险条款',
          '165' => '公路货物运输保险附加盗窃、抢劫保险条款（2009版）',
          '0' => '',
        ];

        return $data[$clauseId];
    }

    public function getClientData($policy, $platform)
    {
        $data = [];
        if($platform['id'] == 5){
            $data =  [
                'holderProvince' => '110000', // 投保人地址-省
                'holderCity' => '110100', // 投保人地址-市
                'holderCounty' => '110101', // 投保人地址-县
                'holderCustomerType' => '0', // 投保人客户类型-固定团体
                'holderOverseas' => '0', // 投保人是否为境外-固定否
                'holderDocumentType' => '统一社会信用代码', // 投保人证件类型-固定统一社会信用代码
                'holderIdenty' => '91310230MA1K2FMP33', // 投保人证件号-固定上海赢睿统一社会信用代码
                'holderStartDateValid' => '2019-01-04', // 投保人证件号有效起始日期-固定上海赢睿统一社会信用代码
                'holderEndDateValid' => '2039-01-03', // 投保人证件号有效结束日期-固定上海赢睿统一社会信用代码
                'holderUnitProperties' => '300', // 投保人单位性质-固定企业单位
                'holderLinkMan' => $policy['holderName'], // 投保人单位联系人-固定投保人名称
                'holderSex' => '', // 投保人性别-固定空
                'holderBirthday' => '', // 投保人出生日期-固定空
                'holderPhone' => '', // 投保人电话-固定空

                'recognizeeProvince' => '110000', // 被保人地址-省
                'recognizeeCity' => '110100', // 被保人地址-市
                'recognizeeCounty' => '110101', // 被保人地址-县
                'recognizeeCustomerType' => '0', // 被保人客户类型-固定团体
                'recognizeeOverseas' => '0', // 被保人是否为境外-固定否
                'documentType' => '统一社会信用代码', // 被保人证件类型-固定统一社会信用代码
                'recognizeeIdenty' => '91310115MA1H90HR24', // 被保人证件号-固定上海赢睿统一社会信用代码
                'recognizeeStartDateValid' => '2019-01-04', // 被保人证件号有效起始日期-固定上海赢睿统一社会信用代码
                'recognizeeEndDateValid' => '2039-01-03', // 被保人证件号有效结束日期-固定上海赢睿统一社会信用代码
                'recognizeeUnitProperties' => '300', // 被保人单位性质-固定企业单位
                'recognizeeLinkMan' => $policy['recognizeeName'], // 被保人单位联系人-固定被保人名称
                'recognizeeSex' => '', // 被保人性别-固定空
                'recognizeeBirthday' => '', // 被保人出生日期-固定空
                'recognizeePhone' => '', // 被保人电话-固定空
            ];
        }
        return $data;
    }
}
