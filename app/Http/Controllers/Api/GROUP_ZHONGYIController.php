<?php

namespace App\Http\Controllers\Api;

use Illuminate\Support\Facades\Log;

class GROUP_ZHONGYIController
{

    public function approval($policy, $product, $settings)
    {
        Log::debug('GROUP_ZY:', $policy);
        $setting = explode("|#|",$settings[0]);
        $params = json_decode($product['config'], true);
        $method = $policy['flag'];
        $message = $this->$method($policy, $setting);

        $data = array(
            "config" => array(
                "account" => $params['username'],
                "password" => $params['password'],
            ),
            "flag" => $policy['flag'],
            "message" => $message,
            "settings"=>$setting
        );

       return $data;
    }

    /**
     * 投保报文整理
     *
     * @param $policy
     * @param $setting
     * @return array
     */
    protected function insure($policy, $setting)
    {
        $body = array(
            "orderNo" => $policy['orderNo'],
            "username" => $setting[0],
            "password" => $setting[1],
            "application" => [
                "name" => $policy['applicantName'], //投保人名称
                "idType" => $policy['applicantIdType'], //投保人证件类型
                "idNo" => $policy['applicantIdNo'],  //投保人证件号
                "mobile" => $policy['applicantMobile'],
                "majorClassCode" => '006', //职业大类
                "jobCode" => '624', //职业编码
                "address" => "",
                "email" => "",
            ],
            "insureds" => [
                [
                    "seqNo" => 1,
                    "name" => $policy['insuredName'], //被保人名称
                    "idType" => $policy['insuredIdType'], //被保人证件类型
                    "idNo" => $policy['insuredIdNo'], //被保人证件号
                    "mobile" => $policy['insuredMobile'],
                    "majorClassCode" => '006', //职业大类
                    "jobCode" => '624', //职业编码
                    "address" => "",
                    "email" => "",
                    "relationship" => '601001' // 与投保人关系类型编码
                ]
            ],
            "insurePlan" => [
                "productCode" => $policy['productCode'], //产品代码
                "planCode" => $policy['planCode'], // 方案代码
                "effectTime" => date('Y-m-d', strtotime($policy['effectTime'])), //起保时间
                "expiryTime" => date('Y-m-d', strtotime($policy['expiryTime'])), //终保时间
                "yearFlag" => 1, // 整年投保标识
                "operationTime" => date('Y-m-d'), //保险售出时间 (当前时间)
                "sumPremium" => $policy['sumPremium'] //总保费
            ],
            "extAttrs" => [
                "provinceCode" => $policy['provinceCode'], //省级代码
                "provinceName" => $policy['provinceName'], //省名称
                "cityCode" => $policy['cityCode'], //市级代码
                "cityName" => $policy['cityName'], //市名称
                "activeAddress" => $policy['activeAddress'],
                "itemNo" => 1, // ?
                "insuredEmployeSumNo" => "" // ?
            ]
        );
        foreach ($policy['employers'] as $key => $value){
            $body['employers'][] = [
                'serialNo' => $value['serialNo'], // 序号
                'employeeName' => $value['employeeName'], // 姓名
                'identifyType' => "01", // 雇员证件类型
                'identifyNumber' => $value['identifyNumber'], // 雇员证件号码
                'gender' => $value['gender'], // 雇员性别
                'birthday' => date('Y-m-d',strtotime(substr($value['identifyNumber'], 6, 8))), // 生日
                'occupationCode' => $value['jobCode'], // 雇员职业
                'jobCode' => $value['jobCode'], // 工种编码
                'jobGrade' => $value['jobGrade'], // 职业类别
                'majorClassCode' => $value['majorClassCode'], // 职业大类
                'nation' => '',
                'wages' => '',
                'remark' => '',
            ];
        }
        $data = array(
            'head' => [
                'sign' => md5(str_replace(["\r", "\n", "\t"], "", json_encode($body, JSON_UNESCAPED_UNICODE)).$setting[2]),
            ],
            'body' => base64_encode(json_encode($body, JSON_UNESCAPED_UNICODE))
        );
        return $data;
    }

    /**
     * 核保报文整理
     *
     * @param $policy
     * @param $setting
     * @return array
     */
    protected function underwrite($policy, $setting)
    {
        $body = array(
            "userName" => $setting[0],
            "password" => $setting[1],
            "orderNo" => $policy['orderNo'],
            "proposalNo" => [
                ''
            ]
        );
        $data = array(
            'head' => [
                'sign' => md5(str_replace(["\r", "\n", "\t"], "", json_encode($body)).$setting[2]),
            ],
            'body' => $body
        );
        return $data;
    }

    /**
     * 人员批改报文整理
     *
     * @param $policy
     * @param $setting
     * @return array
     */
    protected function modify($policy, $setting)
    {
        $body = array(
            "userName" => $setting[0],
            "password" => $setting[1],
            "orderNo" => $policy['orderNo'],
            "policyNo" => $policy['policyNo'],
            "endorType" => "14",
            "correctEffectDate" => date('Y-m-d', strtotime('+1 day')),
            "sumPremium" => $policy['sumPremium'],
        );
//        if ($body['sumPremium'] < 0) $body['sumPremium'] = "0";
        foreach ($policy['employers'] as $key => $value){
            $body['employers'][] = [
                'serialNo' => $value['serialNo'], // 序号
                'employeeName' => $value['employeeName'], // 姓名
                'identifyType' => "01", // 雇员证件类型
                'identifyNumber' => $value['identifyNumber'], // 雇员证件号码
                'gender' => $value['gender'], // 雇员性别
                'birthday' => date('Y-m-d',strtotime(substr($value['identifyNumber'], 6, 8))), // 生日
                'occupationCode' => $value['jobCode'], // 雇员职业
                'jobCode' => $value['jobCode'], // 工种编码
                'jobGrade' => $value['jobGrade'], // 职业类别
                'majorClassCode' => $value['majorClassCode'], // 职业大类
                'correctEffectDate' => date('Y-m-d', strtotime('+1 day')), // 批改时间
                'correctType' => $value['correctType'], // 批改类型
                'nation' => '',
                'wages' => '',
                'remark' => '',
            ];
        }
        $data = array(
            'head' => [
                'sign' => md5(str_replace(["\r", "\n", "\t"], "", json_encode($body, JSON_UNESCAPED_UNICODE)).$setting[2]),
            ],
            'body' => base64_encode(json_encode($body, JSON_UNESCAPED_UNICODE))
        );
        return $data;
    }

    /**
     * 退保报文整理
     *
     * @param $policy
     * @param $setting
     * @return array
     */
    protected function cancel($policy, $setting)
    {
        $body = array(
            "userName" => $setting[0],
            "password" => $setting[1],
            "orderNo" => $policy['orderNo'],
            "policyNo" => $policy['policyNo'],
            "endorType" =>"19" // 退保 19
        );
        $data = array(
            'head' => [
                'sign' => md5(str_replace(["\r", "\n", "\t"], "", json_encode($body, JSON_UNESCAPED_UNICODE)).$setting[2]),
            ],
            'body' => base64_encode(json_encode($body, JSON_UNESCAPED_UNICODE))
        );
        return $data;
    }

}
