<?php

namespace App\Http\Controllers\Api;

class BDYFController
{

    public function approval($policy, $product, $setting)
    {
        // 账号 密码 产品标识
        $params = json_decode($product['config'], true);
        $messageData = $this->getMessage($policy, $setting);

        $data = array(
            "config" => array(
                "account" => $params['username'],
                "password" => $params['password'],
            ),
            "message" => array(
                "action" => 'toubao',
                "bx_area" => '0',
                "bx_company" => $messageData['bx_company'],  //保险公司标识                人保: renbao  平安: pingan  太平洋: taipingyang
                "bx_company_logo" => $messageData['bx_company_logo'],  //保险公司logo地址
                "bx_type" => $messageData['bx_type'],  //
                "bx_product_tag" => $messageData['bx_product_tag'],  //产品标识
                "bx_hwtype" => $messageData['bx_hwtype'],  //投保类型                普货: puhuo   鲜活: xianhuo 商品车: shangpinche
                "myRates" => $messageData['myRates'],  //费率
                "bx_product_allname" => $messageData['bx_product_allname'],  //产品名称
                "bx_product_hwtype" => $messageData['bx_product_hwtype'],  //产品名称
                "bx_truename" => $messageData['bx_truename'],  //被保人名称
                "bx_tel" => $messageData['bx_tel'],  //被保人联系方式
                "bx_cardnum" => $messageData['bx_cardnum'],  //被保人证件号
                "bx_address" => $messageData['bx_address'],  //被保人地址
                "bx_chenum" => $messageData['bx_chenum'],  //车牌号
                "bx_hetongnum" => $messageData['bx_hetongnum'],  //合同发票号
                "bx_starttime" => $messageData['bx_starttime'],  //起运日期 年月日
                "bx_starthour" => $messageData['bx_starthour'],  //起运日期 时
                "transType" => $messageData['transType'],  //运输方式
                "loadType" => $messageData['loadType'],  //装载方式
                "bx_jsy" => '',  //驾驶员姓名
                "bx_jsysj" => '',  //驾驶员手机号
                "bx_jsysfz" => '',  //驾驶员身份证号
                "bx_xsnum" => '',  //行驶证号
                "bx_yynum" => '',  //营运证号
                "bx_fdjnum" => '',  //发动机号
                "bx_cjnum" => '',  //车架号
                "goodsType" => '百货零担',  //货物分类
                "bx_huowu" => $messageData['bx_huowu'],  //货物名称
                "bx_baozhuang" => $messageData['bx_baozhuang'],  //包装及数量
                "bx_huowunum" => $messageData['bx_huowunum'],  //货物重量
                "bx_jine" => $messageData['bx_jine'],  //保险金额（万元）
                "bx_startadd" => $messageData['bx_startadd'],  //起运地
                "bx_endadd" => $messageData['bx_endadd'],  //目的地
                "bx_midadd" => $messageData['bx_midadd'],  //中转地
                "bx_tstype" => '',  //特殊类型 西藏:xz
                "bx_rates" => $messageData['bx_rates'],  //费率
                "bx_money" => $messageData['bx_money'],  //保费
                "bx_moneyget" => '1',  //
            ),
            "settings" => $setting
        );

        return $data;
    }

    public function getMessage($policy, $setting)
    {
        $starDate = date('Y-m-d',$policy['departureDate']);
        $hour = date('H',strtotime('+1 hour'));

        $productData = array( //产品信息
            'pingan_puhuo_jb_zhengzhou' => array( //郑州平安普货（限额300万）(基本险)
                'productName' => '郑州平安普货（西藏除外 剔除罐头、木材、原棉，限额300万）(基本险)',
                'company' => 'pingan',
                'companyLogo' => 'images/tb16.png',
                'insureType' => 'puhuo', //投保类型                普货: puhuo   鲜活: xianhuo 商品车: shangpinche
                'clauseType' => 'jb',  // 险种 字段名:bx_type
                'rate' => '1.2',
            ),
            'pingan_puhuo_zh_zhengzhou' => array( //郑州平安普货（剔除罐头木材 原棉 不保裸装和薄膜包装）西藏除外，限额300万(综合险)
                'productName' => '郑州平安普货（剔除罐头木材 原棉 不保裸装和薄膜包装）西藏除外，限额300万(综合险)',
                'company' => 'pingan',
                'companyLogo' => 'images/tb16.png',
                'insureType' => 'puhuo',
                'clauseType' => 'zh',
                'rate' => '1.2',
            ),
            'pingan_shangpinche_jb_' => array( //平安商品车( 限额200万)(基本险)
                'productName' => '平安商品车( 限额200万)(基本险)',
                'company' => 'pingan',
                'companyLogo' => 'images/tb16.png',
                'insureType' => 'shangpinche',
                'clauseType' => 'jb',
                'rate' => '1.5',
            ),
            'taipingyang_puhuo_jb_' => array( //上海太平洋普货(基本险)
                'productName' => '上海太平洋普货(基本险)',
                'company' => 'taipingyang',
                'companyLogo' => 'images/tb17.png',
                'insureType' => 'puhuo',
                'clauseType' => 'jb',
                'rate' => '1.2',
            ),
            'pingan_puhuo_jb_nanjing' => array( //测试
                'productName' => '南京平安普货（限额200）(基本险)',
                'company' => 'pingan',
                'companyLogo' => 'images/tb16.png',
                'insureType' => 'puhuo',
                'clauseType' => 'jb',
                'rate' => '1',
            ),
            'taipingyang_xianhuo_lc_yunnan' => array(
                'productName' => '云南太平洋冷藏一切险（青海、西藏、内蒙古除外，被保人为货主 限额100万）(冷藏险)',
                'company' => 'taipingyang',
                'companyLogo' => 'images/tb17.png',
                'insureType' => 'xianhuo',
                'clauseType' => 'lc',
                'rate' => '2.5',
            ),
            'renbao_puhuo_jb_hangzhou' => array(
                'productName' => '杭州人保普货（ 西藏 青海 除外   被保人货主、物流公司、司机  限额200万)(基本险)',
                'company' => 'renbao',
                'companyLogo' => 'images/tb14.png',
                'insureType' => 'puhuo',
                'clauseType' => 'jb',
                'rate' => '1',
            ),
            'renbao_puhuo_zh_hangzhou' => array(
                'productName' => '杭州人保普货（ 西藏青海除外 西藏 青海 除外  剔除纸品， 被保人货主、物流公司、司机  限额200万）(综合险)',
                'company' => 'renbao',
                'companyLogo' => 'images/tb14.png',
                'insureType' => 'puhuo',
                'clauseType' => 'zh',
                'rate' => '1.3',
            )
        );
        $result = $productData[$setting[2]];

        $data = array(
            'bx_company' => $result['company'],
            'bx_company_logo' => $result['companyLogo'],
            'bx_type' => $result['clauseType'],
            'bx_product_tag' => $setting[2],
            'bx_hwtype' => $result['insureType'],
            'myRates' => $result['rate'],
            'bx_product_allname' => $result['productName'],
            'bx_product_hwtype' => $result['insureType'],
            'bx_truename' => $policy['recognizeeName'],
            'bx_tel' => $policy['recognizeePhone'],
            'bx_cardnum' => $policy['recognizeeIdenty'],
            'bx_address' => $policy['recognizeeAddr'],
            'bx_chenum' => $policy['transport'],
            'bx_hetongnum' => $policy['orderNo'] . '/' .$policy['invNo'],
            'bx_starttime' => $starDate,
            'bx_starthour' => $hour,
            'transType' => '汽运',
            'loadType' => '非集装箱',
            'bx_huowu' => $policy['goodsName'],
            'bx_baozhuang' => $policy['pack'],
            'bx_huowunum' => $policy['quantity'],
            'bx_jine' => $policy['insuredAmount'] / 10000,
            'bx_startadd' => $policy['fromLoc'],
            'bx_midadd' => $policy['viaLoc'],
            'bx_endadd' => $policy['toLoc'],
            'bx_rates' => $result['rate'], //使用设置的费率
//            'bx_rates' => $policy['ratio'], //使用提交报文的费率
            'bx_money' => $policy['premium']
        );

        foreach($data as $key => $value){  // 构造表单的提交报文值可以为空,但不能为null,不能有数字
            if(empty($value)){
                $data[$key] = '';
            }
            if(is_numeric($value)){
                $data[$key] = (string)$value;
            }
        }
        return $data;
    }
}
