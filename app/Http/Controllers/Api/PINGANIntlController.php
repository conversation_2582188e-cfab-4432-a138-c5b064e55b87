<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class PINGANIntlController extends Controller
{
    public function approval($policy, $product, $setting, $platform)
    {
        foreach ($policy as $key=>$value){
            $policy[$key] = str_replace(array("\r\n", "\r", "\n", "\t"), "|#|", $value);
        }
        if($platform['id'] == '5'){
            $message = $this->getBaoyaData($policy, $setting);
        }
        if($platform['id'] == '6'){
            $message = $this->getYunJiData($policy, $setting);
        }

        if($setting[0] == 'CGHZYRWLKJ00002' || $setting[0] == 'CGNJYB00013'){
            if ($message['holderLinkman'] == '' || (strlen($message['holderLinkman']) < 5)) {
                $message['holderLinkman'] = '蕴保';
            }

            if ($message['recognizeeLikman'] == '' || (strlen($message['recognizeeLikman']) < 5)) {
                $message['recognizeeLikman'] = '蕴保';
            }

            if ($message['holderTel'] == '' || (strlen($message['holderTel']) < 5)) {
                $message['holderTel'] = '13386136167';
            }

            if ($message['holderAddr'] == '' || (strlen($message['holderAddr']) < 5)) {
                $message['holderAddr'] = '上海市虹口区周家嘴路1010号通利园415室';
            }

            if ($message['recognizeeAddr'] == '' || (strlen($message['recognizeeAddr']) < 5)) {
                $message['recognizeeAddr'] = '上海市虹口区周家嘴路1010号通利园415室';
            }
        }elseif($setting[0] == 'CGSHZGDYA00002'){
            if ($message['holderLinkman'] == '' || (strlen($message['holderLinkman']) < 5)) {
                $message['holderLinkman'] = '曹金凤';
            }

            if ($message['recognizeeLikman'] == '' || (strlen($message['recognizeeLikman']) < 5)) {
                $message['recognizeeLikman'] = '曹金凤';
            }

            if ($message['holderTel'] == '' || (strlen($message['holderTel']) < 5)) {
                $message['holderTel'] = '***********';
            }
            if ($message['holderAddr'] == '' || (strlen($message['holderAddr']) < 5)) {
                $message['holderAddr'] = '东莞市南城区袁屋边车站北路恒正大厦5楼';
            }
            $message['holderLinkMan'] = $message['holderName'];
        }

        $data = array(
            "config" => array(
                "account" => $setting[0],
                "password" => $setting[1]
            ),
            "message" => array(
                'isInvoice' => $message['isInvoice'],    //是否开发票
                'insureType' => $message['insureType'],  //投保类型 中文
                'transportType' => $message['transportType'],    //运输方式 中文
                'mainClause' => $message['mainClause'],
                'additionClause' => $message['additionClause'],
                'otherAddition' => $message['otherAddition'],
                'holderName' => $message['holderName'],
                'holderBussLicence' => $message['holderBussLicence'],
                'holderAddr' => $message['holderAddr'],
                'holderLinkman' => $message['holderLinkman'],
                'holderTel' => $message['holderTel'],
                'holderEmail' => $message['holderEmail'],
                'recognizeeName' => $message['recognizeeName'],
                'recognizeeAddr' => $message['recognizeeAddr'],
                'recognizeeLikman' => $message['recognizeeLikman'],
                'recognizeeTel' => $message['recognizeeTel'],
                'goodsTypeBig' => $message['goodsTypeBig'],
                'goodsTypeSmall' => $message['goodsTypeSmall'],
                'packType' => $message['packType'],
                'cargoType' => $message['cargoType'],
                'quantity' => $message['quantity'],
                'invoiceNo' => $message['invoice'],
                'landBillNo' => $message['landBillNo'],
                'transportNo' => $message['transportNo'],
                'loadType' => $message['loadType'],
                'fromLoc' => $message['fromLoc'],
                'viaLoc' => $message['viaLoc'],
                'toLoc' => $message['toLoc'],
                'payLoc' => $message['payLoc'],
                'isCredit' => $message['isCredit'],
                'clause' => $message['clause'],
                'invoiceAmount' => $message['invoiceAmount'],
                'coverage' => $message['coverage'],
                'invoiceCurrency' => $message['invoiceCurrency'],
                'starCountry' => $message['starCountry'],
                'endCountry' => $message['endCountry'],
                'rate' => $message['rate'],
                'premium' => $message['premium'],
                'isSpecialGoodsType' => $message['isSpecialGoodsType'],
                'isSpecial' => $message['isSpecial'],
                'isOtherClause' => $message['isOtherClause'],
                'otherClause' => $message['otherClause'],
                'departureDate' => $message['departureDate'],
                'timeFormat' => $message['timeFormat'],
                'isPortClause' => $message['isPortClause'],
            ),
            "settings" => $setting
        );

        if($data['message']['isPortClause'] == 1){
            $title = '【'.$policy['orderNo'].'】- 保呀投保邮件报备';
            $mailContent = $this->mailReportContent($policy);
            $insMail = '<EMAIL>';
            $config = config('services.by_mail');
            sys_mail($insMail,'', $title, $mailContent, $config);
        }
        return $data;
    }

    public function getYunJiData($policy, $setting)
    {
        return $policy;
    }

    public function getBaoyaData($policy, $setting)
    {

        $mainClauseArr = array(
            '1' => '航空运输一切险',
            '2' => '航空运输险',
            '3' => 'INSTITUTE CARGO CLAUSES (AIR)',
            '4' => '一切险',
            '5' => '水渍险',
            '6' => '平安险',
            '7' => 'INSTITUTE CARGO CLAUSES (A)1982',
            '8' => 'INSTITUTE CARGO CLAUSES (B)1982',
            '9' => 'INSTITUTE CARGO CLAUSES (C)1982',
            '10' => '冷藏险',
            '11' => '冷藏一切险',
            '12' => '一切险',
            '13' => '水渍险',
            '14' => '平安险',
            '15' => 'INSTITUTE CARGO CLAUSES (A)1982',
            '16' => 'INSTITUTE CARGO CLAUSES (B)1982',
            '17' => 'INSTITUTE CARGO CLAUSES (B)1982',
            '18' => '陆运一切险',
            '19' => '陆运险',
            '20' => '陆运冷藏险',
        );

        $additionClause = array(
            '1' => 'PL0900052',
            '2' => 'PL0900009',
            '3' => 'PL0900021',
            '4' => 'PL0900051',
            '5' => 'PL0900049',
            '6' => 'PL0900008',
            '7' => 'PL0900009',
            '8' => 'PL0900010',
            '9' => 'PL0900015',
            '10' => 'PL0900013',
            '11' => 'PL0900016',
            '12' => 'PL0900012',
            '13' => 'PL0900011',
            '14' => 'PL0900017',
            '15' => 'PL0900018',
            '16' => 'PL0900019',
            '17' => 'PL0900014',
            '18' => 'PL0900020',
            '19' => 'PL0900051',
            '20' => 'PL0900049',
            '21' => 'INCLUDINGON DECK CLAUSE.',
        );

        $otherAddition = '';
        $additionClauseArr = explode(',', $policy['additionClause']);
        array_pop($additionClauseArr);
        $additionClauses = array();
        foreach($additionClauseArr as $key => $value){
            if($value == 21){
                $otherAddition.= $additionClause[$value];
            }else{
                $additionClauses[] =  $additionClause[$value];
            }
        }

        $goodsTypeBigArr = array(
            '纺织原料及纺织制品'=> ['11','5001'],
            '机器设备及其零件、附件'=> ['16','8479'],
            '纸质品'=> ['20','9401'],
            '贱金属及其制品、五金类'=> ['15','7201'],
            '食品'=> ['04','1601'],
            '化学工业及其相关工业产品'=> ['06','2801'],
            '塑料及其制品;橡胶及其制品'=> ['07','3901'],
            '木制品、木材'=> ['09','4401'],
            '仪器、乐器、医疗设备及零件、附件（非精密仪器）'=> ['18','9001'],
            '杂项制品'=> ['20','9401'],
            '电脑/平板电脑、手机等电子产品'=> ['18','9001'],

            '水果'=> ['04','1601'],
            '蔬菜'=> ['04','1601'],
            '鲜花'=> ['04','1601'],
            '其他鲜活'=> ['04','1601'],

            '玻璃及玻璃制品'=> ['13','6801'],
            '大理石、瓷砖、石材及其制品'=> ['13','6801'],
            '陶瓷制品'=> ['13','6801'],
            '太阳能电池板'=> ['13','6801'],
            '其他易碎品'=> ['13','6801'],

            '新车'=> ['17','0000'],
            '二手车'=> ['17','0000'],

            '冷藏食品、农副产品'=> ['04','1601'],
            '冷藏水产品'=> ['04','1601'],
            '其他冷藏品'=> ['04','1601'],

            '9类危险品'=> ['06','2801'],
            '8类危险品'=> ['06','2801'],
            '6类危险品'=> ['06','2801'],
            '5类危险品'=> ['06','2801'],
            '4类危险品'=> ['06','2801'],
            '3类危险品'=> ['06','2801'],
            '2类危险品'=> ['06','2801'],

            '煤、炭'=> ['05','2501'],
            '矿石、矿粉、矿砂等'=> ['05','2501'],
            '其他矿产资源类'=> ['05','2501'],

            '对运输有防震动、防倾斜、防尘等特殊要求的仪器'=> ['18','9001'],
            '目的地国家无法维修的仪器'=> ['18','9001'],
            '单件货物保额超过RMB200万元的仪器'=> ['18','9001'],
        );

        $goodsTypeSmallArr = array(
            '粮食、食品、果蔬、饮料、烟草及饲料'=> '1601',
            '轻纺、文体、日用品、工艺品'=> '5001',
            '医药类'=> '2801',
            '金属原料、矿产及建材类、玻璃及其制品'=> '6801',
            '其它'=> '9613',
            '木、木浆及木制品；纸、纸浆、纸板及其制品'=> '4701',
            '石油、化工原料及其制品'=> '2801',
            '电器、机械、运输工具、设备类'=> '8479',
        );

        $packTypeArr = array(
            '裸装' => '裸状包装',
            '散装' => '散装及托盘包装',
            '纸箱' => '箱状包装',
            '木箱' => '箱状包装',
            '捆包' => '捆包状包装',
            '袋装' => '袋状包装',
            '篓装' => '其他形状包装',
            '托盘' => '散装及托盘包装',
            '桶装' => '桶状包装',
            '罐装' => '其他形状包装',
        );

        $timeFormatArr = array(
            '1'=> $this->getTimeFormat( $policy['departureDate'] ),
            '2'=>'B/L',
        );

        $transportTypeArr = array(
            '水路运输' => '水路',
            '航空运输' => '航空',
            '公路运输' => '公路',
            '铁路运输' => '铁路',
        );

        $startContry = explode('-',$policy['fromCountry'])[1];
        $endContry = explode('-',$policy['toCountry'])[1];

        $portClauseArr = array(
            '阿尔及利亚', '安哥拉', '贝宁', '博茨瓦纳', '布基纳法索', '布隆迪', '喀麦隆', '佛得角', '中非', '乍得', '科摩罗', '科特迪瓦共和国/象牙海岸', '民主刚果共和国', '刚果民主共和国', '吉布提', '埃及', '赤道几内亚', '厄立特里亚', '埃塞俄比亚', '加蓬', '冈比亚', '巴瑟斯特', '加纳', '几内亚', '几内亚比绍', '赤道几内亚共和国', '肯尼亚', '莱索托', '利比里亚', '利比亚', '马达加斯加', '马拉维', '马里', '毛里塔尼亚', '毛里求斯', '摩洛哥,', '莫桑比克', '纳米比亚', '尼日尔', '尼日利亚', '卢旺达', '圣多美和普林西比', '塞内加尔', '塞舌尔', '塞拉利昂', '索马里', '南非', '苏丹', '南苏丹', '斯威士兰', '坦桑尼亚', '多哥', '突尼斯', '乌干达', '佛得角', '赞比亚', '津巴布韦', '百慕大', '墨西哥', '危地马拉', '伯利兹', '洪都拉斯', '尼加拉瓜', '巴拿马', '巴哈马', '特克斯和凯科斯群岛', '开曼群岛', '海地共和国', '多米尼加', '波多黎各', '美属维尔京群岛', '圣基茨(圣克里斯托菲）尼维斯', '安圭拉', '安提瓜', '蒙特塞拉特', '瓜德罗普', '多米尼克国', '马提尼克', '圣卢西亚', '圣文森特', '巴巴多斯', '格林纳达', '特立尼达和多巴哥', '荷属安的列斯', '阿鲁巴', '牙买加', '哥斯达黎加', '英属维尔京群岛', '古巴共和国', '萨尔瓦多', '海地共和国', '哥伦比亚', '委内瑞拉', '圭亚那', '苏里南', '厄瓜多尔', '秘鲁', '巴西', '玻利维亚', '智利', '巴拉圭', '乌拉圭', '阿根廷', '塞尔维亚', '斯洛文尼亚', '克罗地亚', '波黑', '马其顿', '黑山', '波黑', '缅甸', '古巴', '刚果', '伊朗', '伊拉克', '科特迪瓦共和国', '利比里亚', '北朝鲜', '卢旺达', '塞拉利昂', '索马里', '苏丹', '叙利亚', '埃及', '厄立特里亚', '几内亚比绍共和国', '利比亚', '几内亚共和国', '南苏丹', '突尼斯', '俄罗斯', '乌克兰', '摩尔多瓦', '海地', '委内瑞拉', '黎巴嫩', '也门', '尼泊尔', '津巴布韦', '布隆迪', '土耳其', '以色列', '阿拉伯联合酋长国', '沙特阿拉伯', '科威特', '留尼汪'
        );
        $portCluase = '0';
        if($policy['insureType'] == 'export' && in_array($endContry, $portClauseArr)){
            $portCluase = '1';
        }
        if($policy['insureType'] == 'import' && in_array($startContry, $portClauseArr)){
            $portCluase = '1';
        }

        $data = array(
            'isInvoice' => '0',    //是否开发票
            'insureType' => $policy['insureType'],  //投保类型 中文
            'transportType' => $transportTypeArr[$policy['transportType']],    //运输方式 中文
            'mainClause' => $mainClauseArr[$policy['mainClause']],
            'additionClause' => $additionClauses,
            'otherAddition' => $policy['otherClause'],
            'holderName' => $policy['holderName'],
            'holderBussLicence' => '',
            'holderAddr' => $policy['holderAddr'],
            'holderLinkman' => '',
            'holderTel' => $policy['holderTel'],
            'holderEmail' => '',
            'recognizeeName' => $policy['recognizeeName'],
            'recognizeeAddr' => $policy['recognizeeAddr'],
            'recognizeeLikman' => '',
            'recognizeeTel' => $policy['recognizeeTel'],
            'goodsTypeBig' => $goodsTypeBigArr[$policy['goodsType']][0],
            'goodsTypeSmall' => $goodsTypeBigArr[$policy['goodsType']][1],
            'packType' => $packTypeArr[$policy['packType']],
            'cargoType' => $policy['goodsName'],
            'quantity' => $policy['quantity'],
            'invoice' => $policy['invoiceNo'],
            'landBillNo' => $policy['landBillNo'],
            'transportNo' => $policy['transportNo'],
            'loadType' => $policy['loadType'],
            'fromLoc' => $policy['fromLoc'],
            'viaLoc' => $policy['viaLoc'],
            'toLoc' => $policy['toLoc'],
            'payLoc' => $policy['payLoc'],
            'isCredit' => $policy['isCredit'],
            'clause' => $policy['clause'],
            'invoiceAmount' => $policy['invAmount'],
            'coverage' => $policy['amount'],
            'invoiceCurrency' => $policy['invCurrency'],
            'starCountry' => $startContry,
            'endCountry' => $endContry,
            'rate' => $policy['rate'],
            'premium' => $policy['premium'],
            'isSpecialGoodsType' => '0',
            'isSpecial' => '0',
            'isOtherClause' => '0',
            'otherClause' => $policy['otherClause'],
            'departureDate' => date('Y-m-d',$policy['departureDate']),
            'timeFormat' => $timeFormatArr[$policy['timeFormat']],
            'isPortClause' => $portCluase,
        );

        if($data['endCountry'] == '阿拉伯联合酋长国'){
            $data['endCountry'] = '阿联酋';
        }elseif($data['endCountry'] == '台湾省'){
            $data['endCountry'] = '台湾';
        }elseif($data['endCountry'] == '科特迪瓦共和国'){
            $data['endCountry'] = '科特迪瓦';
        }

        if($data['starCountry'] == '阿拉伯联合酋长国'){
            $data['starCountry'] = '阿联酋';
        }elseif($data['starCountry'] == '台湾省'){
            $data['starCountry'] = '台湾';
        }elseif($data['starCountry'] == '科特迪瓦共和国'){
            $data['starCountry'] = '科特迪瓦';
        }

        return $data;
    }

    public function getTimeFormat($date)
    {
        $hours = date('m',$date );
        $format = array(
            '01' => 'Jan',
            '02' => 'Feb',
            '03' => 'Mar',
            '04' => 'Apr',
            '05' => 'May',
            '06' => 'Jun',
            '07' => 'Jul',
            '08' => 'Aug',
            '09' => 'Sep',
            '10' => 'Oct',
            '11' => 'Nov',
            '12' => 'Dec',
        );

        return $format[$hours];
    }

    public function mailReportContent($policy){
        $mail_message  = '<p><b>提交于'.date('Y-m-d H:i', time()).'</b></p>';
        $mail_message .= '<p>--------------------------------------</p>';
//        $mail_message .= '<p><b>投保人：</b>'.$policy['holderName'].'</p>';
//        $mail_message .= '<p><b>投保人电话：</b>'.$policy['holderTel'].'</p>';
//        $mail_message .= '<p><b>投保人地址：</b>'.$policy['holderAddr'].'</p>';
        $mail_message .= '<p><b>被保险人：</b>'.$policy['recognizeeName'].'</p>';
        $mail_message .= '<p><b>被保险人电话：</b>'.$policy['recognizeeTel'].'</p>';
        $mail_message .= '<p><b>被保险人地址：</b>'.$policy['recognizeeAddr'].'</p>';
        $mail_message .= '<p><b>起运地：</b>'.$policy['fromLoc'].'</p>';
        $mail_message .= '<p><b>中转地：</b>'.$policy['viaLoc'].'</p>';
        $mail_message .= '<p><b>目的地：</b>'.$policy['toLoc'].'</p>';
        $mail_message .= '<p><b>起运日期：</b>'.date('Y-m-d', $policy['departureDate']).'</p>';
        $mail_message .= '<p><b>货物名称：</b>'.$policy['goodsName'].'</p>';
        $mail_message .= '<p><b>货物规格：</b>'.$policy['quantity'].'</p>';
        $mail_message .= '<p><b>包装方式：</b>'.$policy['packType'].'</p>';
        $mail_message .= '<p><b>车牌号：</b>'.$policy['transportNo'].'</p>';
        $mail_message .= '<p><b>发票号/运单号：</b>'.$policy['invoiceNo'].'/'.$policy['landBillNo'].'</p>';
        $mail_message .= '<p><b>运输方式：</b>'.$policy['transportType'].'</p>';
        $mail_message .= '<p><b>保额：</b>&yen;'.$policy['amount'].'</p>';
        $mail_message .= '<p><b>币种：</b>'.$policy['invCurrency'].'</p>';
        $mail_message .= '<p><b>流水号：</b>'.$policy['orderNo'].'</p>';
        $mail_message .= '<p>--------------------------------------</p>';
        $mail_message .= '<p><b>条款：</b>'.$policy['clause'].'</p>';
        $mail_message .= '<p>--------------------------------------</p>';
        $mail_message .= '<p></p>';
        $mail_message .= '<p>此邮件由保呀系统自动发送</p>';
        return $mail_message;
    }

}
