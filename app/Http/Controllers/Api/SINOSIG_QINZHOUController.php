<?php

namespace App\Http\Controllers\Api;

class SINOSIG_QINZHOUController
{

    public function approval($policy, $product, $setting)
    {
        $params = json_decode($product['config'], true);
        $config = $this->getConfig($setting);
        $goodsType = $this->getGoodsType($policy['goodsType']);
        $message = [
            "xmlHead" => [
                // 接入类型标识 写死
                "modeFlag" => "1",
                // 操作类型 写死
                "operateType" => "INPUT",
                // 写死 1
                "policyCount" => 1,
                // 用户名 对接时提供 写死
                "sendName" => $config['sendName'],
                // 密码 对接时提供 写死
                "sendPwd" => $config['sendPwd'],
                // 请求序列号 uuid MD5加流水号即可
                "sendSeq" => md5($policy['orderNo']),
                // 请求时间
                "sendTime" => date('Y-m-d H:i:s'),
                // 系统标识 对接时提供 写死
                "sysFlag" => $config['sysFlag'],
                // 交易码 对接时提供 写死
                "transCode" => $config['transCode']
            ],
            "tbPolicyDtoList" => [
                [
                    "tbInsuranceSchema" => [
                        // 业务流水号
                        "importSn" => $policy['orderNo'],
                        // 出单时间
                        "inputDate" => date('Y-m-d H:i:s'),
                        // 投保时间
                        "insurDate" => date('Y-m-d H:i:s'),
                        // 起保日期
                        "insurStartDate" => date('Y-m-d H:i:s', $policy['departureDate']),
                        // 终保日期
                        "insurEndDate" => date('Y-m-d H:i:s', strtotime('+1year',$policy['departureDate'])),
                        // 保险期间
                        "insurPeriod" => 1,
                        // 份数 写死
                        "mult" => "1",
                        // 保额
                        "amount" => $policy['insuredAmount'],
                        // 保费
                        "premium" => $policy['premium'],
                        // 固定值 暂时先传 2
                        "appliType" => "2",
                        // 产品代码
                        "certifyCode" => "SX001",
                        // 险种 固定 0907
                        "mainRiskCode" => "0907",
                        // 投保人名称
                        "holderName" => $policy['holderName'],
                        // 投保人证件号码
                        "holderIdNo" => "1",
                        // 投保人证件类型
                        "holderidType" => "99",
                        // 投保人电话
                        "holderPhone" => $policy['holderPhone'],
                        // 投保人邮箱
                        "holderemail" => "",
                        // 投保人手机
                        "holdermobile" => $policy['holderPhone'],
                        // 投保人地址
                        "holderAddress" => $policy['holderAddr'],
                        // 被保人数量
                        "insuredNumber" => "1",
                        // 被保人名称
                        "insuredName" => $policy['recognizeeName'],
                        // 被保人唯一id
                        "insuredId" => substr(md5($policy['recognizeeName']), 0, 30),
                        // 被保人证件号
                        "insuredIdNo" => "",
                        // 被保人证件类型
                        "insuredidType" => "99",
                        // 被保人邮箱
                        "insuredEmail" => "",
                        // 被保人电话
                        "insuredmobile" => $policy['recognizeePhone'],
                        // 被保人地址
                        "insuredAddress" => $policy['recognizeeAddr'],
                        // 财产所在地
                        "propertyAddress" => "",
                        // 投被保人关系
                        "relationship" => "99",
                        // 写死 2
                        "insureType" => "2"
                    ],
                    "tbRiskList" => [
                        [
                            // 序号 自1开始；步长为1；递增
                            "serialNo" => 1,
                            // 分项条款保额
                            "amount" => $policy['insuredAmount'],
                            // 保额
                            "unitAmount" => $policy['insuredAmount'],
                            // 分项条款责任保费
                            "premium" => $policy['premium'],
                            // 份数
                            "mult" => "1",
                            // 是否计入总保额 Y 计入；N不计入
                            "amtFlag" => "Y",
                            // 费率 （分项保费/分项保额）*1000
                            "benchMarkRate" => $policy['ratio'],
                            // 被保人数量
                            "insuredNumber" => "1",
                            // 责任代码
                            "itemCode" => $goodsType[0],
                            // 责任名称
                            "itemName" => $goodsType[1],
                            // 条款代码
                            "kindCode" => "001",
                            // 条款名称
                            "kindName" => "国内水路、陆路货物运输保险综合险条款",
                            // 主附险标识 1主险，2附加险
                            "mainRiskFlag" => "1",
                            // 险种 写死
                            "riskCode" => "0907",
                            // 责任名称
                            "itemdetailname" => $goodsType[1],
                            // 险种 0907
                            "riskcodesub" => "0907"
                        ]
                    ],
                    "tbMainCargo" => [
                        // 发票金额
                        "invoiceAmount" =>  $policy['insuredAmount'],
                        // 运输方式
                        "conveyance" =>  $this->getTransportType($policy['transportTypeID']),
                        // 运输工具名称及航次
                        "transportModeName" =>  $policy['transport'],
                        // 货物描述
                        "itemDescription" => $policy['goodsName'],
                        "quantity" => $policy['quantity'].' ; '.$policy['pack'],
                        // 标记唛头
                        "marks" => "",
                        // 发票号
                        "invoiceNo" => $policy['invNo'],
//                        运单号
                        "ladingNo" => $policy['freightNo'],
                        // 起运地
                        "startSiteName" => $policy['fromLoc'],
                        // 中转地
                        "viaSiteName" => $policy['viaLoc'],
                        // 北京
                        "endSiteName" => $policy['toLoc'],
                    ],
                    "tbEngageList" => $this->getSpecial()
                ]

            ]
        ];
        $data = array(
            "config" => array(
                "account" => $params['username'],
                "password" => $params['password']
            ),
            "message" => [
                'message' => $message,
                "sign" => $config['sendName'].$config['sendPwd'].$config['sign'],
                "requestMethod" => "YGGXWL",
                "serverName" => "YGGXWL"
            ],
            "settings" => $setting
        );

        return $data;
    }

    protected function getTransportType($transportId)
    {
       $transportType = [
           '3' => '水路运输',
           '4' => '航空运输',
           '5' => '公路运输',
           '6' => '铁路运输',
       ];

        return $transportType[$transportId];
    }

    protected function getSpecial()
    {
        $special = [
            [
                "clauseCode" => "T9999",
                "clauses" => "特别约定：",
                "flag" => "1",
                "lineNo" => "1",
                "riskCode" => "0907",
                "serialNo" => "1",
                "titleFlag" => "0"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "1、任何保险金额超过100万元的运输—需在起运前3个工作日通知保险公司，交由保险公司进行审核，经保险人同意后方能承保；",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "2"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "2、承运人的车辆停在无人看管的场所发生盗抢事故，属除外责任。",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "3"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "3、整车失踪（包括驾驶员、货物及车辆失踪）、失窃、货物神秘失踪属除外责任；",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "4"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "4、不承保由货运承运人及其职员或车辆驾驶人员的故意或不诚实行为导致的任何损失；",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "5"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "5、金属及其制品的生锈、氧化、褪色风险除外；保险人不负责因货物包装破损引起的污染导致的其他货运标的的损失；",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "6"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "6、机械设备和仪器的机械、电子和电器工作紊乱除外；",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "7"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "7、假牌假证或因被保险人与他方的纠纷导致的损失属除外责任；",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "8"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "8、在承运人实际起运前的存储、包装环节发生的损失属除外责任；",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "9"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "9、投保人/被保险人/承运人/在分配、分派、中转仓库存放时间超过7天遭受的损失属除外责任；",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "10"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "10、裸装货物不承保刮擦、锈损、氧化、变色、凹瘪责任。",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "11"
            ],
            [
                "clauseCode" => "T9999",
                "flag" => "1",
                "lineNo" => "1",
                "clauses" => "11、每次事故绝对免赔人民币 1000 元或损失金额的 10%，两者取高者为准； 盗窃、抢劫保险事故：每次事故绝对免赔为损失金额的 20%； 火灾事故：每次事故绝对免赔为损失金额的 20%。",
                "titleFlag" => "1",
                "riskCode" => "0907",
                "serialNo" => "12"
            ],
        ];

        return $special;
    }

    protected function getConfig($sysParams)
    {
        $data = [
            // 用户名 对接时提供 写死
            "sendName" => $sysParams[0],
            // 密码 对接时提供 写死
            "sendPwd" => $sysParams[1],
            "sign" => $sysParams[2],
            // 系统标识 对接时提供 写死
            "sysFlag" => "YGGXWL",
            // 交易码 对接时提供 写死
            "transCode" => "SV001112"
        ];

        return $data;
    }

    protected function getGoodsType($goodsType)
    {
        $data = [
            '纺织原料及纺织制品'=> ['1302','普通货物'],
            '机器设备及其零件、附件'=> ['1302','普通货物'],
            '纸质品'=> ['1302','普通货物'],
            '贱金属及其制品、五金类'=> ['1302','普通货物'],
            '食品'=> ['1302','普通货物'],
            '化学工业及其相关工业产品'=> ['1302','普通货物'],
            '塑料及其制品;橡胶及其制品'=> ['1302','普通货物'],
            '木制品、木材'=> ['1302','普通货物'],
            '仪器、乐器、医疗设备及零件、附件（非精密仪器）'=> ['1302','普通货物'],
            '杂项制品'=> ['1302','普通货物'],
            '电脑/平板电脑、手机等电子产品'=> ['1302','普通货物'],

            '水果'=> ['04','1601'],
            '蔬菜'=> ['04','1601'],
            '鲜花'=> ['04','1601'],
            '其他鲜活'=> ['04','1601'],

            '玻璃及玻璃制品'=> ['0301','玻璃制品'],
            '大理石、瓷砖、石材及其制品'=> ['0301','玻璃制品'],
            '陶瓷制品'=> ['0301','玻璃制品'],
            '太阳能电池板'=> ['0301','玻璃制品'],
            '其他易碎品'=> ['0301','玻璃制品'],

            '新车'=> ['17','0000'],
            '二手车'=> ['17','0000'],

            '冷藏食品、农副产品'=> ['0109','其它粮油食品类'],
            '冷藏水产品'=> ['0109','其它粮油食品类'],
            '其他冷藏品'=> ['0109','其它粮油食品类'],

            '9类危险品'=> ['06','2801'],
            '8类危险品'=> ['06','2801'],
            '6类危险品'=> ['06','2801'],
            '5类危险品'=> ['06','2801'],
            '4类危险品'=> ['06','2801'],
            '3类危险品'=> ['06','2801'],
            '2类危险品'=> ['06','2801'],

            '煤、炭'=> ['05','2501'],
            '矿石、矿粉、矿砂等'=> ['05','2501'],
            '其他矿产资源类'=> ['05','2501'],

            '对运输有防震动、防倾斜、防尘等特殊要求的仪器'=> ['18','9001'],
            '目的地国家无法维修的仪器'=> ['18','9001'],
            '单件货物保额超过RMB200万元的仪器'=> ['18','9001'],
        ];

        return $data[$goodsType];
    }

}
