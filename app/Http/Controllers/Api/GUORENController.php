<?php

namespace App\Http\Controllers\Api;

class GUORENController
{
	public function approval($policy, $product)
    {

        $cargo_info = explode("##", $policy['CargoInfo']);
        if(empty($cargo_info[0])) $cargo_info[0]='百货';
        if(empty($cargo_info[1])) $cargo_info[1]='标准包装';

        //被保人及证件类型
        if(strstr($policy['InsuredName'], '公司')){
            $clntType  = '2';
            $certfType = '71';
        }else{
            $clntType  = '1';
            $certfType = '01';
        }

        $StartName = explode('|||',$policy['FromAddress'])[0];
        $ToName    = explode('|||',$policy['ToAddress'])[0];


        $sys_params = explode('|#|', $product['params']);

        if(empty($policy['ApplicantName'])){
            $policy['ApplicantName']  = $policy['InsuredName'];
            $policy['ApplicantPhone'] = $policy['InsuredPhone'];
        }

        $api_params = array(
            'PACKAGE' => array(

                'HEAD' => array(
                    'SERVE_CODE'                    => 'GR0002',
                    'CHANNEL_CODE'                  => 'CI000017',
                    'INTERFACE_CODE'                => 'II000097',
                    'INTERFACE_USER_CODE'           => 'youfu',
                    'BUSINESS_UUID'                 => $policy['SerialNo'],
                    'REQUEST_TIME'                  => '',
                    'XML_LIST_SUFFIX'               => '',
                    //'INTERFACE_PWD'                 => '$2a$04$XC2pWOMzGfEGI4KL1fdRtOc7WnERGv/lOQZCTUSb0tkxT8SViprIi',//测试
                    'INTERFACE_PWD'                 => '$2a$04$.IbOOMM88z/prZEui/cnheD85qLHBRkh86PlM41VEtnXBek912iM6',//正式
                    'CLIENT_ENCODE'                 => 'utf-8',
                ),
                'BODY' => array(
                    'amt'                           => sprintf('%.2f', floatval($policy['Coverage'])*10000),
                    'premium'                       => $policy['InsFee'],
                    'riskCode'                      => $sys_params[0],
                    'rate'                          => '',
                    'invoiceAmt'                    => $policy['InsFee'],
                    'goodsName'                     => $cargo_info[0],
                    'quantity'                      => $cargo_info[1],
                    'invoiceNo'                     => $policy['Waybill'],
                    'carNo'                         => $policy['TransNum'],
                    'goodsCategoryCode'             => $sys_params[1],
                    'goodsCategoryName'             => $sys_params[3],
                    'voyageStartCode'               => $this->parse_area($StartName),
                    //'voyageStartCode'               => '912087',
                    'voyageStartName'               => $StartName,
                    'voyageToCode'                  => $this->parse_area($ToName),
                    //'voyageToCode'                  => '920093',
                    'voyageToName'                  => $ToName,
                    'riskFlag'                      => $sys_params[2],
                    'applName'                      => $policy['ApplicantName'],
                    'certfType'                     => $certfType,
                    'certfNo'                       => $policy['InsuredIDNum'],
                    'mobile'                        => $policy['ApplicantPhone'],
                    'clntType'                      => $clntType,
                    'email'                         => '<EMAIL>',
                    'zipCode'                       => '610000',
                    'operateSite'                   => 'A03YFHY',
                    'insApplInsrntList'             => array(
                        array(
                            'applName'              => $policy['InsuredName'],
                            'certfType'             => $certfType,
                            'certfNo'               => $policy['InsuredIDNum'],
                            'mobile'                => $policy['InsuredPhone'],
                            'clntType'              => $clntType,
                            'email'                 => '<EMAIL>',
                            'zipCode'               => '610000'
                        )
                    )

                )


            )
        );


        return json_encode($api_params);

	}

    private function parse_area($str){
        $areas = explode('-', $str);
        if(empty($areas[2])){
            $areas[2] = $areas[1];
        }
        if(empty($areas[1])){
            $areas[2] = $areas[0];
        }

        $_city = mb_substr($areas[2],0,2,'utf-8');
        $GuorenCitys = M('GuorenCitys');
        $city = $GuorenCitys->where('city_name LIKE("%'.$_city.'%")')->find();

        if(!empty($city)){
            return $city['city_code'];
        }else{
			$__city = mb_substr($areas[0],0,2,'utf-8');
			$city = $GuorenCitys->where('city_name LIKE("%'.$__city.'%")')->find();
			if(!empty($city)){
				return $city['city_code'];
			}
		}
		
        return '';
    }

}

?>