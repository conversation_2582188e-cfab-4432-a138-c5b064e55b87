<?php

namespace App\Http\Controllers\Api;

class SinosigController
{

    public function approval($policy, $product, $settings)
    {
        $policy['goodsName'] = $this->replace_sign('%', '&#37;', $policy['goodsName']);
        $policy['quantity'] = $this->replace_sign('%', '&#37;', $policy['quantity']);
        $settings[2] = $this->replace_sign('%', '&#37;', $settings[2]);

        $sys_params = explode('|#|', $settings[0]);
        $params = json_decode($product['config'], true);

        $conveyance = $this->getConveyance($sys_params[1]);
        $platformSettings = $this->getProductSettings($product,$policy);

        $xmlstr = '<?xml version="1.0" encoding="UTF-8"?>
        <TransPolicyRequest>
            <!--报文的唯一标识码,uuid，32位,必传-->
            <sendSeq>' . md5($policy['orderNo']) . '</sendSeq>
            <!--报文发送时间,必传-->
            <sendTime>' . date('Y-m-d H:i:s') . '</sendTime>
            <!--系统标识,自定义,必传-->
            <sysFlag>'.$platformSettings['sysFlag'].'</sysFlag>
            <!--操作类型,写死,必传-->
            <operateType>PROPOSAL_INPUT</operateType>
            <!--是否需要转换基础信息代码,true;false,必传-->
            <isTransCode>false</isTransCode>
            <policy>
                <!--险种代码，0904:国内航空货物运输保险  0907:国内水路、陆路货物运输保险  1001：进口货物保险  1002：出口货物保险,必传-->
                <riskCode>' . $sys_params[1] . '</riskCode>
                <!--险类代码，09:国内货物运输  10:进出口货物运输  ,必传-->
                <classCode>09</classCode>
                <!--业务归属机构,必传,写死-->
                <comCode>'.$platformSettings['comCode'].'</comCode>
                <!--预约协议号,必传,写死-->
                <protocolNo>'.$platformSettings['protocolNo'].'</protocolNo>
                <!--投保日期,报文中日期格式统一为:yyyy-MM-ss HH:mm:ss,必传-->
                <proposalDate>' . date('Y-m-d H:i:s') . '</proposalDate>
                <!--录入日期,可为空-->
                <inputDate></inputDate>
                <!--签单日期,可为空-->
                <signDate></signDate>
                <!--起保日期,可为空-->
                <startDate>' . date('Y-m-d', $policy['departureDate']) . '</startDate>
                <!--保单币种代码,人民币为CNY,必传-->
                <policyCurrency>CNY</policyCurrency>
                <!--支付币种代码,必传-->
                <payCurrency>CNY</payCurrency>
                <!--保单币种名称,必传-->
                <policyCurrencyName>人民币</policyCurrencyName>
                <!--支付币种名称,必传-->
                <payCurrencyName>人民币</payCurrencyName>
                <!--总保额,必传-->
                <sumAmount>' . $policy['insuredAmount'] . '</sumAmount>
                <!--总保费,必传-->
                <sumPremium>' . $policy['premium'] . '</sumPremium>
                <!--费率,必传-->
                <rate>' . ($policy['ratio'] / 10) . '</rate>
                <!--总保额折合人民币,必传-->
                <sumAmountRMB>' . $this->parse_coverage_to_num($policy['insuredAmount']) . '</sumAmountRMB>
                <!--总保费折合人民币,必传-->
                <sumPremiumRMB>' . $this->parse_coverage_to_num($policy['premium']) . '</sumPremiumRMB>
                <!--原始总保费,可为空-->
                <originalPremium></originalPremium>
                <!--原始总保额，可为空-->
                <originalAmount></originalAmount>
                <!--保额币种对保费币种汇率,必传-->
                <exchRate>1</exchRate>
                <!--保单正本份数（国内险种默认为1）,必传-->
                <printCount>1</printCount>
                <!--核保方式，0-自核，1人核,必传,默认为0-->
                <underwriteMode>0</underwriteMode>
                <!--审核人,可为空-->
                <auditUser></auditUser>
                <!--核保意见,可为空-->
                <underwriteComment></underwriteComment>
                <!--核保状态,00-核保中01-核保通过02-核保退回,必传,默认为00-->
                <underwriteStatus>'.$platformSettings['underwriteStatus'].'</underwriteStatus>
                <!--核保时间,可为空-->
                <underwriteTime></underwriteTime>
                <!--自核失败原因,可为空-->
                <failReason></failReason>
                <!--备注,可为空-->
                <remark></remark>
                <!--是否批改，0-否，1-是,可为空-->
                <correctFlag></correctFlag>
                <!--批量导入组号,可为空-->
                <batchImportGroup></batchImportGroup>
                <!--状态，01-暂存02-核保中03-核保通过04核保退回,必传,默认01-->
                <validStatus>01</validStatus>
                <!--操作人,即出单员代码,必传,写死-->
                <operateCode>'.$platformSettings['operateCode'].'</operateCode>
                <!--修改时间,可为空-->
                <updateTime></updateTime>
                <applicant>
                    <!--投保人名称,可为空-->
                    <applicantName>' . $policy['holderName'] . '</applicantName>	
                    <!--投保人电话,可为空-->		
                    <phoneNumber></phoneNumber>		
                    <!--投保人联系地址,可为空-->			
                    <applicantAddress>' . $policy['holderAddr'] . '</applicantAddress>
                    <!--标识位用于判断是否从报文获取发票信息 0.否  1.是 可为空，若有值则开票相关节点必传-->		
                    <isDiyMsg></isDiyMsg>
                    <!--纳税人名称 必传-->
                    <taxPayerName></taxPayerName>
                    <!--纳税人类型(1.小规模纳税人 2.一般纳税人 ) 必传-->
                    <taxPayerType></taxPayerType>
                    <!--是否需要开具增值税专用发票(1.是 0.否) 必传-->
                    <vatinvoiceFlag></vatinvoiceFlag>
                    <!--纳税人识别号 专票必传-->
                    <taxPayerNumber></taxPayerNumber>
                    <!--税务登记开户行 专票必传-->
                    <revenueBank></revenueBank>
                    <!--税务登记开户行账号 专票必传-->
                    <revenueAccount></revenueAccount>
                    <!--税务登记公司地址 专票必传-->
                    <revenueAddress></revenueAddress>
                    <!--税务登记公司电话 专票必传-->
                    <revenuePhoneNumber></revenuePhoneNumber>
                    <!--开票方式(0.为空1.明细 2.汇总) 必传-->
                    <invoiceMode></invoiceMode>
                    <!--发票联系人姓名 必传-->
                    <invoiceName></invoiceName>
                    <!--发票联系人电话 必传-->
                    <invoicePhoneNumber></invoicePhoneNumber>
                    <!--发票联系人邮箱 必传-->
                    <invoiceEmail></invoiceEmail>
                </applicant>
                <insured>
                    <!--被保险人名称，最多100字符,必传-->
                    <insuredName>' . $policy['recognizeeName'] . '</insuredName>
                    <!--被保险人类型，1-个人，2-单位,必传-->
                    <insuredFlag>2</insuredFlag>
                    <!--证件号码，可为空-->
                    <identifyNumber>' . $policy['recognizeeIdenty'] . '</identifyNumber>
                    <!--被保险人地址，最多500字符,必传-->
                    <insuredAddress>' . $policy['recognizeeAddr'] . '</insuredAddress>
                    <!--被保险人电话,可为空-->
                    <phoneNumber></phoneNumber>
                </insured>
                <transport>
                    <!--运输方式代码,必传-->
                    <conveyance>' . $sys_params[2] . '</conveyance>
                    <!--运输方式名称,必传-->
                    <conveyanceName>' . $this->transportType($sys_params[2]) . '</conveyanceName>
                    <!--运输工具名称及航次,必传-->
                    <blName>' . $policy['transport'] . '-' . $policy["invNo"] . '</blName>
                    <!--船龄,可为空-->
                    <preserveInfo></preserveInfo>
                    <!--合同号，不能有汉字、全角字符，最多30字符。合同号、发票号和提单号不能全为空-->
                    <contractNo>' . $policy['orderNo'] . '</contractNo>
                    <!--信用证号,国内险种为空-->
                    <creditNo></creditNo>
                    <!--发票号，不能有汉字、全角字符，最多50字符-->
                    <invoiceNo>'.$platformSettings['invoiceNo'].'</invoiceNo>
                    <!--提单号，不能有汉字、全角字符，最多20字符-->
                    <ladingNo>'.$platformSettings['ladingNo'].'</ladingNo>
                    <!--信用证条款,国内险种为空-->
                    <creditClause></creditClause>
                    <!--起运地代码,必传-->
                    <startSite>' . $this->getProvinceNo($policy['fromLoc'], 0) . '</startSite>
                    <!--起运地名称，最多100字符,必传-->
                    <startSiteDetail>' . $this->cut_str($this->convert_address($policy['fromLoc']), 18) . '</startSiteDetail>
                    <!--中转地代码,可为空-->
                    <viaSite></viaSite>
                    <!--中转地名称,最多100字符,可为空-->
                    <viaSiteDetail>' . $this->cut_str($this->convert_address($policy['viaLoc']), 18) . '</viaSiteDetail>
                    <!--目的地代码,必传-->
                    <endSite>' . $this->getProvinceNo($policy['toLoc'], 0) . '</endSite>
                    <!--目的地名称，最多100字符,必传-->
                    <endSiteDetail>' . $this->cut_str($this->convert_address($policy['toLoc']), 18) . '</endSiteDetail>
                    <!--赔付地名称，最多40字符,必传-->
                    <claimSite>'.$platformSettings['claimSite'].'</claimSite>
                    <!--起运日期,必传-->
                    <startTransportDate>' . date('Y-m-d', $policy['departureDate']) . ' 00:00:00</startTransportDate>
                    <!--电子保单上起运日期显示方式标识,0:见提单（AS PER B/L） 1:实际录入的日期,必传-->
                    <startTransportDateFlag>1</startTransportDateFlag>
                    <!--加成系数，最多精确到4位小数,国内险种可为空-->
                    <plusRate></plusRate>
                    <!--特别约定，最多2000字符,必传-->
                    <clauses>' . $settings[2] . '</clauses>
                    <!--理赔代理人代码，国内险种可为空-->
                    <claimAgentCode></claimAgentCode>
                    <!--理赔代理人名称,国内险种可为空-->
                    <claimAgent></claimAgent>
                    <!--发票金额，国内险种可为空-->
                    <invoiceAmount></invoiceAmount>
                    <!--船舶航次，有特殊要求才会用到,一般设置为空值-->
                    <voyageNo></voyageNo>
                    <!--是否添加附页，0：否，1：是-->
                    <isAttachment>1</isAttachment>
                    <!--仓库信息，特殊字段，一般不需要传值，会放在特别约定之后，可自核-->
                    <storageInfo></storageInfo>
                    <!--额外信息，提交后会直接拼接到特别约定之后，用于放置一些字段长度有限制但必须要填写的内容，需人核-->
                    <extendMsg></extendMsg>
                </transport>
                <item>
                    <!--货物类别代码,必传-->
                    <detailType>' . $sys_params[3] . '</detailType>
                    <!--货物类别名称,必传-->
                    <detailName>' . $sys_params[4] . '</detailName>
                    <!--货物描述，最多800字符,必传-->
                    <detailDescribe>' . $this->cut_str($policy['goodsName'], 90) . ';' . $this->cut_str($policy['quantity'], 60) . '</detailDescribe>
                    <!--标的唛头，最多500字符,可为空,具体看客户需要-->
                    <marks>' . $policy['orderNo'] . '</marks>
                    <!--包装方式代码,必传-->
                    <packerCode>'.$platformSettings['packerCode'].'</packerCode>
                    <!--包装方式名称,货物数量、单位,必传-->
                    <packerName>'.$platformSettings['packerName'].'</packerName>
                </item>
                <kindGroup>
                     ' . $this->getKinds($sys_params[1], $sys_params[3], $sys_params[5], isset($sys_params[6]) ? $sys_params[6] : '') . '
                </kindGroup>
            </policy>
        </TransPolicyRequest>';

        $data = array(
            "config" => array(
                "account" => $params['username'],
                "password" => $params['password']
            ),
            "message" => base64_encode($xmlstr),
            "settings" => $sys_params
        );

        return $data;
    }


    private function transportType($code)
    {
        $conveyance = array();
        $conveyance['01'] = '海洋运输';
        $conveyance['02'] = '航空运输';
        $conveyance['03'] = '公路运输';
        $conveyance['04'] = '铁路运输';
        $conveyance['05'] = '邮包运输';
        $conveyance['06'] = '联运';
        $conveyance['08'] = '内河运输';
        $conveyance['09'] = '沿海运输';

        return $conveyance[$code];
    }



    private function getConveyance($code)
    {
        $conveyance = array();
        $conveyance['0907'] = array(
            'riskCode'       => '0907',
            'conveyance'     => '03',
            'conveyanceName' => '公路运输'
        );
        $conveyance['0904'] = array(
            'riskCode'       => '0904',
            'conveyance'     => '02',
            'conveyanceName' => '航空运输'
        );
        return $conveyance[$code];
    }



    private function getKinds($insType, $category, $main, $attach = '')
    {
        $kinds = array();
        //==========航空
        if ($insType == '0904') {
            $kinds['0206'] = array( //已停用
                'detailName' => '蔬菜、瓜果',
                'kind'       => array(
                    '001' => array(
                        'kindCode'     => '001',
                        'kindName'     => '冷藏',
                        'mainFlag'     => 'Y',
                        'rate'         => 0.3,
                        'kindNameElse' => ''
                    )
                )
            );
            $kinds['0301'] = array(
                'detailName' => '玻璃制品',
                'kind'       => array(
                    '001' => array(
                        'kindCode'     => '001',
                        'kindName'     => '易碎品',
                        'mainFlag'     => 'Y',
                        'rate'         => 0.5,
                        'kindNameElse' => ''
                    )
                )
            );
            $kinds['1302'] = array(
                'detailName' => '普通货物',
                'kind'       => array(
                    '001' => array(
                        'kindCode'     => '001',
                        'kindName'     => '普货',
                        'mainFlag'     => 'Y',
                        'rate'         => 0.25,
                        'kindNameElse' => ''
                    )
                )
            );
        }
        //==========水陆路
        if ($insType == '0907') {
            $kinds['0206'] = array(
                'detailName' => '蔬菜、瓜果',
                'kind'       => array(
                    '010' => array(
                        'kindCode'     => '010',
                        'kindName'     => '陆上运输冷藏货物保险条款',
                        'mainFlag'     => 'Y',
                        'rate'         => 0.3,
                        'kindNameElse' => '（冷藏冷冻）'
                    )
                )
            );

            $kinds['0301'] = array(
                'detailName' => '玻璃制品',
                'kind'       => array(
                    '002' => array(
                        'kindCode'     => '002',
                        'kindName'     => '国内水路、陆路货物运输保险基本险条款',
                        'mainFlag'     => 'Y',
                        'rate'         => 0.5,
                        'kindNameElse' => '易碎品（火灾爆炸交通事故）'
                    )
                )
            );

            $kinds['1302'] = array(
                'detailName' => '普通货物',
                'kind'       => array(
                    '001' => array(
                        'kindCode'     => '001',
                        'kindName'     => '国内水路、陆路货物运输保险综合险条款',
                        'mainFlag'     => 'Y',
                        'rate'         => 0.15,
                        'kindNameElse' => '',
                        'DQX'          => array(
                            'kindCode'     => '107',
                            'kindName'     => '国内公路货物运输盗抢险条款',
                            'mainFlag'     => 'N',
                            'rate'         => 0.07,
                            'kindNameElse' => '',
                        )
                    ),
                    '002' => array(
                        'kindCode'     => '002',
                        'kindName'     => '国内水路、陆路货物运输保险基本险条款',
                        'mainFlag'     => 'Y',
                        'rate'         => 0.12,
                        'kindNameElse' => ''
                    )
                )
            );

            $kinds['0109'] = array(
                'detailName' => '其它粮油食品类',
                'kind'       => array(
                    '010' => array(
                        'kindCode'     => '010',
                        'kindName'     => '陆上运输冷藏货物保险条款',
                        'mainFlag'     => 'Y',
                        'rate'         => 0.3,
                        'kindNameElse' => ''
                    )
                )
            );
        }


        $kind = $kinds[$category]['kind'][$main];

        $kind_str = '';
        $kind_str .= '
        <kind>
            <!--险别代码,必传-->
            <kindCode>' . $kind['kindCode'] . '</kindCode>
            <!--险别名称,必传-->
            <kindName>' . $kind['kindName'] . '</kindName>
            <!--主险标识,Y-主险，N-附加险,必传-->
            <mainFlag>' . $kind['mainFlag'] . '</mainFlag>
            <!--费率,必传-->
            <rate>' . $kind['rate'] . '</rate>
            <!--险种别名,如果配置协议时险种别名为空,则此节点传空值即可-->
            <kindNameElse>' . $kind['kindNameElse'] . '</kindNameElse>
        </kind>
        ';

        if ($attach != '') {
            $add_risk = $kind[$attach];
            $kind_str .= '
            <kind>
                <!--险别代码,必传-->
                <kindCode>' . $add_risk['kindCode'] . '</kindCode>
                <!--险别名称,必传-->
                <kindName>' . $add_risk['kindName'] . '</kindName>
                <!--主险标识,Y-主险，N-附加险,必传-->
                <mainFlag>' . $add_risk['mainFlag'] . '</mainFlag>
                <!--费率,必传-->
                <rate>' . $add_risk['rate'] . '</rate>
                <!--险种别名,如果配置协议时险种别名为空,则此节点传空值即可-->
                <kindNameElse>' . $add_risk['kindNameElse'] . '</kindNameElse>
            </kind>';
        }

        return $kind_str;
    }


    private function getProvinceNo($str, $index)
    {
        $province = array();
        $province['北京市'] = array('008601',    '中国 北京');
        $province['上海市'] = array('008602',    '中国 上海');
        $province['天津市'] = array('008603',    '中国 天津');
        $province['重庆市'] = array('008604',    '中国 重庆');
        $province['黑龙江省'] = array('008605', '中国 黑龙江');
        $province['吉林省'] = array('008606',    '中国 吉林');
        $province['辽宁省'] = array('008607',    '中国 辽宁');
        $province['河北省'] = array('008608',    '中国 河北');
        $province['山东省'] = array('008609',    '中国 山东');
        $province['安徽省'] = array('008610',    '中国 安徽');
        $province['江苏省'] = array('008611',    '中国 江苏');
        $province['浙江省'] = array('008612',    '中国 浙江');
        $province['江西省'] = array('008613',    '中国 江西');
        $province['内蒙古自治区'] = array('008614', '中国 内蒙古');
        $province['内蒙古区'] = array('008614', '中国 内蒙古'); //优孚
        $province['宁夏回族自治区'] = array('008615',    '中国 宁夏');
        $province['宁夏区'] = array('008615',    '中国 宁夏'); //优孚
        $province['甘肃省'] = array('008616',    '中国 甘肃');
        $province['青海省'] = array('008617',    '中国 青海');
        $province['陕西省'] = array('008618',    '中国 陕西');
        $province['山西省'] = array('008619',    '中国 山西');
        $province['河南省'] = array('008620',    '中国 河南');
        $province['湖北省'] = array('008621',    '中国 湖北');
        $province['湖南省'] = array('008622',    '中国 湖南');
        $province['福建省'] = array('008623',    '中国 福建');
        $province['广东省'] = array('008624',    '中国 广东');
        $province['广西壮族自治区'] = array('008625',    '中国 广西');
        $province['广西区'] = array('008625',    '中国 广西');
        $province['贵州省'] = array('008626',    '中国 贵州');
        $province['云南省'] = array('008627',    '中国 云南');
        $province['四川省'] = array('008628',    '中国 四川');
        $province['西藏自治区'] = array('008629',    '中国 西藏');
        $province['西藏区'] = array('008629',    '中国 西藏');
        $province['新疆维吾尔族自治区'] = array('008630',    '中国 新疆');
        $province['新疆区'] = array('008630',    '中国 新疆');
        $province['海南省'] = array('008631',    '中国 海南');


        $find = '|||';
        $pos = strpos($str, $find);
        if ($pos == false) {
            //
        } else {
            $str = explode('|||', $str)[0];
        }

        $address = explode('-', $str);

        return $province[$address[0]][$index];
    }

    private function replace_sign($sign, $replace, $str)
    {
        $str = explode($sign, htmlspecialchars($str));
        return implode($replace, $str);
    }


    private function parse_coverage_to_num($num)
    {
//        return $num * 10000;
        return $num;
    }

    private function convert_address($str)
    {
        $str = str_replace("|||", "", $str);
        $str = str_replace("-", "", $str);

        return $str;
    }

    private function cut_str($string, $sublen, $start = 0, $code = 'UTF-8')
    {
        if ($code == 'UTF-8') {
            $pa = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|\xe0[\xa0-\xbf][\x80-\xbf]|[\xe1-\xef][\x80-\xbf][\x80-\xbf]|\xf0[\x90-\xbf][\x80-\xbf][\x80-\xbf]|[\xf1-\xf7][\x80-\xbf][\x80-\xbf][\x80-\xbf]/";
            preg_match_all($pa, $string, $t_string);
            if (count($t_string[0]) - $start > $sublen) return join('', array_slice($t_string[0], $start, $sublen)) . "...";
            return join('', array_slice($t_string[0], $start, $sublen));
        } else {
            $start = $start * 2;
            $sublen = $sublen * 2;
            $strlen = strlen($string);
            $tmpstr = '';
            for ($i = 0; $i < $strlen; $i++) {
                if ($i >= $start && $i < ($start + $sublen)) {
                    if (ord(substr($string, $i, 1)) > 129) {
                        $tmpstr .= substr($string, $i, 2);
                    } else {
                        $tmpstr .= substr($string, $i, 1);
                    }
                }
                if (ord(substr($string, $i, 1)) > 129) $i++;
            }
            if (strlen($tmpstr) < $strlen) $tmpstr .= "...";
            return $tmpstr;
        }
    }

    public function getProductSettings($product,$policy)
    {
        if($product['product_code'] == '2020022194297'){
            //保呀
            $array = array(
                'sysFlag' => 'BAOYA',
                'comCode' => '07710200',
                'protocolNo' => '10771YAB02023000006',
                'operateCode' => 'BAOYA',
                'underwriteStatus' => '00',
                'ladingNo' => $policy['freightNo'],
                'invoiceNo' => $policy['invNo'],
                'claimSite' => $this->cut_str($this->convert_address($policy['toLoc']), 18),
                'packerCode' => $this->getPackerCode($policy['pack'],0),
                'packerName' => $this->getPackerCode($policy['pack'],1),
            );
        }else{
            //阿拉丁
            $array = array(
                'sysFlag' => 'aladdin',
                'comCode' => '07514300',
//                'protocolNo' => '10771YAB02020000006',
                'protocolNo' => '10771YAB02020000035',
                'operateCode' => 'aladdin',
                'underwriteStatus' => '01',
                'ladingNo' => $policy['invNo'] . '-' . $policy["orderNo"],
                'invoiceNo' => '',
                'claimSite' => '中国',
                'packerCode' => '024',
                'packerName' => '标准包装',
            );
        }

        return $array;
    }

    public function getPackerCode($type,$index)
    {
        $packerCode = array(
            '裸装'=>array('024','标准包装'),
            '散装'=>array('024','标准包装'),
            '纸箱'=>array('002','纸箱'),
            '木箱'=>array('001','木箱'),
            '捆包'=>array('024','标准包装'),
            '袋装'=>array('023','袋子'),
            '篓装'=>array('024','标准包装'),
            '托盘'=>array('020','托盘'),
            '桶装'=>array('019','桶'),
            '罐装'=>array('024','标准包装'),
        );
        return isset($packerCode[$type][$index]) ? $packerCode[$type][$index] : $packerCode['散装'][$index];
    }

    /* private function str_to_bin($str)
    {
        //1.列出每个字符
        $arr = preg_split('/(?<!^)(?!$)/u', $str);
        //2.unpack字符
        foreach ($arr as &$v) {
            $temp = unpack('H*', $v);
            $v = base_convert($temp[1], 16, 2);
            unset($temp);
        }

        return join(' ', $arr);
    } */

}
