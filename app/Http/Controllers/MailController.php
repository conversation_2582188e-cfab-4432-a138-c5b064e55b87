<?php

namespace App\Http\Controllers;

use App\Jobs\SendReminderEmail;
use App\Models\DataBaoyaNew;
use App\Models\MailDelivery;
use App\Models\Message;
use App\Models\Platform;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class MailController extends Controller
{

    public $data;

    public function __construct()
    {

        $this->data = new \stdClass();
        $this->data->user = [];
    }

    public function autoins()
    {
        $messages = Message::whereIn('status', [0, 1])
            ->where('created_at', '<', date('Y-m-d H:i:s', (time() - 10 * 60)))
            ->where(function ($q) {
                $q->where(function ($q) {
                    $q->whereIn('mode', ['AUTO_PICCLTD_INTL', 'AUTO_PICCLTD'])
                        ->whereNull('apply_no')
                        ->whereNull('policy_no');
                })->orWhere(function ($q) {
                    $q->whereNotIn('mode', ['AUTO_PICCLTD_INTL', 'AUTO_PICCLTD']);
                });
            })
            ->with(['platform', 'product'])
            ->get();
        foreach ($messages as $message) {
            try {
                //是否发送邮件
                if ($this->isMail($message->platform_id, $message->order_no)) {
                    continue;
                }

                //找出订单对应子系统
                $modelName = null;
                $sender = null;
                foreach (subsystem() as $key => $value) {
                    if ($value[3] == $message->platform->app_id) {
                        $modelName = $key;
                        $sender = $value[4];
                    }
                }

                if (!$modelName || !$sender) {
                    continue;
                }
                //查询出子系统保单数据
                $modelPath = '\\App\\Models\\' . $modelName;
                $model = new $modelPath();
                $data = $model->index(subsystem()[$modelName][0], $message->order_no);
                if (!$data) {
                    continue;
                }
                //合并收件人
                $this->data->user = array_merge(mail_address(), $data['email']);
                Log::debug('Message: ', [$message]);
                Log::debug('Emails: ', $data['email']);
                Log::debug('Emails-All: ', $this->data->user);
                //发送邮件
                $add = $this->addMail($message->platform_id, $message->order_no);
                if ($add) {
                    $mail_message = [
                        'title' => $message->platform->title,
                        'create_time' => $data->create_time,
                        'species' => $data->species,
                        'insured_name' => $data->insured_name,
                        'departure' => $data->departure,
                        'stopovers' => $data->stopovers,
                        'destination' => $data->destination,
                        'start_time' => $data->start_time,
                        'goods_name' => $data->goods_name,
                        'goods_amount' => $data->goods_amount,
                        'pack_type' => $data->pack_type,
                        'vehicle_license_no' => $data->vehicle_license_no,
                        'coverage' => '￥' . $data->coverage . ' (' . num_to_rmb($data->coverage) . ')',
                        'deductible' => $data->deductible,
                        'specials' => $data->specials,
                        'order_no' => $message->order_no
                    ];
                    $this->data->content = $mail_message;
                    $this->data->sender = $sender;
                    $this->data->theme = '【' . $message->order_no . '】投保邮件报备';
                    ;
                    $this->data->platform_id = $message->platform_id;
                    $this->data->order_no = $message->order_no;
                    $this->dispatch(new SendReminderEmail($this->data));
                }
            } catch (\Exception $e) {
                Log::error('Message:' . $e->getMessage());
                continue;
            }
        }
    }

    public function subsystem()
    {
        foreach (subsystem() as $key => $value) {
            $modelPath = '\\App\\Models\\' . $key;
            $model = new $modelPath();
            $time1 = time() - 10 * 60;
            $time2 = time() - 40 * 60;
            $sender = $value[4];
            if (!$value[2]) {
                $time1 = date('Y-m-d H:i:s', $time1);
                $time2 = date('Y-m-d H:i:s', $time2);
            }
            //查询子系统未提交的保单
            $data = $model->indexs($value[1], $time1, $time2);
            //平台数据
            $platform = Platform::where('app_id', $value[3])->first();
            if (isset($data)) {
                foreach ($data as $k2 => $v2) {
                    //合并收件人
                    $this->data->user = array_merge($this->data->user, $data[$k2]['email']);
                    //是否已提交到自动录单系统
                    $message_data = Message::where('platform_id', $platform->id)->where('order_no', $v2[$value[0]])->first();
                    if (!$message_data) {
                        //是否发送邮件
                        if (!($this->isMail($platform->id, $v2[$value[0]]))) {
                            //发送邮件
                            $add = $this->addMail($platform->id, $v2[$value[0]]);
                            if ($add) {
                                $mail_message = [
                                    'title' => '投保邮件报备',
                                    'create_time' => $v2->create_time,
                                    'species' => $v2->species,
                                    'insured_name' => $v2->insured_name,
                                    'departure' => $v2->departure,
                                    'stopovers' => $v2->stopovers,
                                    'destination' => $v2->destination,
                                    'start_time' => $v2->start_time,
                                    'goods_name' => $v2->goods_name,
                                    'goods_amount' => $v2->goods_amount,
                                    'pack_type' => $v2->pack_type,
                                    'vehicle_license_no' => $v2->vehicle_license_no,
                                    'coverage' => '￥' . $v2->coverage . ' (' . num_to_rmb($v2->coverage) . ')',
                                    'deductible' => $v2->deductible,
                                    'specials' => $v2->specials,
                                    'order_no' => $v2[$value[0]]
                                ];
                                $this->data->content = $mail_message;
                                $this->data->sender = $sender;
                                $this->data->theme = '【' . $v2[$value[0]] . '】投保邮件报备';
                                $this->data->platform_id = $platform->id;
                                $this->data->order_no = $v2[$value[0]];
                                $this->dispatch(new SendReminderEmail($this->data));
                            }
                        }
                    }
                }
            }
        }
    }

    //是否发送过邮件
    public function isMail($platform_id, $order_no)
    {
        if (MailDelivery::where('platform_id', $platform_id)->where('order_no', $order_no)->first()) {
            return true;
        } else {
            return false;
        }
    }

    //邮件发送记录表添加数据
    public function addMail($platform_id, $order_no)
    {
        $add = MailDelivery::insertGetId(['platform_id' => $platform_id, 'order_no' => $order_no]);
        if ($add) {
            return true;
        } else {
            return false;
        }
    }
}
