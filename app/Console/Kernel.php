<?php
/*
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2020-05-12 17:01:53
 * @LastEditors: yanb
 * @LastEditTime: 2023-09-13 15:30:51
 */

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // 录单异常事件
        $schedule->command('messages:failure-event')->everyFiveMinutes();
        // 检查优孚录单报文状态
        $schedule->command('messages:checking-yf-policy')->everyMinute();
        // 自动发送报备邮件
        $schedule->command('autoins:send-mail')->everyTenMinutes();
        // 自动发送子系统报备邮件
        $schedule->command('autoins:send-sub-system-mail')->everyTenMinutes();

        // $schedule->command('inspire')
        //          ->hourly();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
