<?php

namespace App\Console\Commands;

use App\Models\Message;
use App\Models\MailDelivery;
use App\Models\Platform;
use App\Jobs\SendReminderEmail;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;

class AutoSubSystemSendMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'autoins:send-sub-system-mail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '自动发送报备邮件';

    public $data;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->data = new \stdClass();
        $this->data->user = [];
    }

    public function handle()
    {
        foreach (subsystem() as $key => $value) {
            $modelPath = '\\App\\Models\\' . $key;
            $model = new $modelPath();
            $time1 = time() - 10 * 60;
            $time2 = time() - 40 * 60;
            $sender = $value[4];
            if (!$value[2]) {
                $time1 = date('Y-m-d H:i:s', $time1);
                $time2 = date('Y-m-d H:i:s', $time2);
            }
            //查询子系统未提交的保单
            $data = $model->indexs($value[1], $time1, $time2);
            //平台数据
            $platform = Platform::where('app_id', $value[3])->first();
            if (isset($data)) {
                foreach ($data as $k2 => $v2) {
                    //合并收件人
                    $this->data->user = array_merge($this->data->user, $data[$k2]['email']);
                    //是否已提交到自动录单系统
                    $message_data = Message::where('platform_id', $platform->id)->where('order_no', $v2[$value[0]])->first();
                    if (!$message_data) {
                        //是否发送邮件
                        if (!($this->isMail($platform->id, $v2[$value[0]]))) {
                            //发送邮件
                            $add = $this->addMail($platform->id, $v2[$value[0]]);
                            if ($add) {
                                $mail_message = [
                                    'title' => '投保邮件报备',
                                    'create_time' => $v2->create_time,
                                    'species' => $v2->species,
                                    'insured_name' => $v2->insured_name,
                                    'departure' => $v2->departure,
                                    'stopovers' => $v2->stopovers,
                                    'destination' => $v2->destination,
                                    'start_time' => $v2->start_time,
                                    'goods_name' => $v2->goods_name,
                                    'goods_amount' => $v2->goods_amount,
                                    'pack_type' => $v2->pack_type,
                                    'vehicle_license_no' => $v2->vehicle_license_no,
                                    'coverage' => '￥' . $v2->coverage . ' (' . num_to_rmb($v2->coverage) . ')',
                                    'deductible' => $v2->deductible,
                                    'specials' => $v2->specials,
                                    'order_no' => $v2[$value[0]]
                                ];
                                $this->data->content = $mail_message;
                                $this->data->sender = $sender;
                                $this->data->theme = '【' . $v2[$value[0]] . '】投保邮件报备';
                                $this->data->platform_id = $platform->id;
                                $this->data->order_no = $v2[$value[0]];
                                dispatch(new SendReminderEmail($this->data));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 是否发送过邮件
     * 
     * @param mixed $platform_id
     * @param mixed $order_no
     * @return bool
     */
    protected function isMail($platform_id, $order_no)
    {
        if (MailDelivery::where('platform_id', $platform_id)->where('order_no', $order_no)->first()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 邮件发送记录表添加数据
     * 
     * @param mixed $platform_id
     * @param mixed $order_no
     * @return bool
     */
    protected function addMail($platform_id, $order_no)
    {
        $add = MailDelivery::insertGetId(['platform_id' => $platform_id, 'order_no' => $order_no]);
        if ($add) {
            return true;
        } else {
            return false;
        }
    }
}
