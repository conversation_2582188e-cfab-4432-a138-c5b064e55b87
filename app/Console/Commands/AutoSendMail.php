<?php

namespace App\Console\Commands;

use App\Models\Message;
use App\Models\MailDelivery;
use App\Jobs\SendReminderEmail;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;

class AutoSendMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'autoins:send-mail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '自动发送报备邮件';

    public $data;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->data = new \stdClass();
        $this->data->user = [];
    }

    public function handle()
    {
        $messages = Message::whereIn('status', [0, 1])
            ->where('created_at', '<', date('Y-m-d H:i:s', (time() - 10 * 60)))
            ->where(function ($q) {
                $q->where(function ($q) {
                    $q->whereIn('mode', ['AUTO_PICCLTD_INTL', 'AUTO_PICCLTD'])
                        ->whereNull('apply_no')
                        ->whereNull('policy_no');
                })->orWhere(function ($q) {
                    $q->whereNotIn('mode', ['AUTO_PICCLTD_INTL', 'AUTO_PICCLTD']);
                });
            })
            ->with(['platform', 'product'])
            ->get();
        foreach ($messages as $message) {
            try {
                //是否发送邮件
                if ($this->isMail($message->platform_id, $message->order_no)) {
                    continue;
                }

                //找出订单对应子系统
                $modelName = null;
                $sender = null;
                foreach (subsystem() as $key => $value) {
                    if ($value[3] == $message->platform->app_id) {
                        $modelName = $key;
                        $sender = $value[4];
                    }
                }

                if (!$modelName || !$sender) {
                    continue;
                }
                //查询出子系统保单数据
                $modelPath = '\\App\\Models\\' . $modelName;
                $model = new $modelPath();
                $data = $model->index(subsystem()[$modelName][0], $message->order_no);
                if (!$data) {
                    continue;
                }
                //合并收件人
                $this->data->user = array_merge(mail_address(), $data['email']);
                Log::debug('Message: ', [$message]);
                Log::debug('Emails: ', $data['email']);
                Log::debug('Emails-All: ', $this->data->user);
                //发送邮件
                $add = $this->addMail($message->platform_id, $message->order_no);
                if ($add) {
                    $mail_message = [
                        'title' => $message->platform->title,
                        'create_time' => $data->create_time,
                        'species' => $data->species,
                        'insured_name' => $data->insured_name,
                        'departure' => $data->departure,
                        'stopovers' => $data->stopovers,
                        'destination' => $data->destination,
                        'start_time' => $data->start_time,
                        'goods_name' => $data->goods_name,
                        'goods_amount' => $data->goods_amount,
                        'pack_type' => $data->pack_type,
                        'vehicle_license_no' => $data->vehicle_license_no,
                        'coverage' => '￥' . $data->coverage . ' (' . num_to_rmb($data->coverage) . ')',
                        'deductible' => $data->deductible,
                        'specials' => $data->specials,
                        'order_no' => $message->order_no
                    ];
                    $this->data->content = $mail_message;
                    $this->data->sender = $sender;
                    $this->data->theme = '【' . $message->order_no . '】投保邮件报备';
                    ;
                    $this->data->platform_id = $message->platform_id;
                    $this->data->order_no = $message->order_no;
                    dispatch(new SendReminderEmail($this->data));
                }
            } catch (\Exception $e) {
                Log::error('SendMailError:' . $e->getMessage());
                continue;
            }
        }
    }

    /**
     * 是否发送过邮件
     * 
     * @param mixed $platform_id
     * @param mixed $order_no
     * @return bool
     */
    protected function isMail($platform_id, $order_no)
    {
        if (MailDelivery::where('platform_id', $platform_id)->where('order_no', $order_no)->first()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 邮件发送记录表添加数据
     * 
     * @param mixed $platform_id
     * @param mixed $order_no
     * @return bool
     */
    protected function addMail($platform_id, $order_no)
    {
        $add = MailDelivery::insertGetId(['platform_id' => $platform_id, 'order_no' => $order_no]);
        if ($add) {
            return true;
        } else {
            return false;
        }
    }
}
