<?php

namespace App\Console\Commands;

use App\Models\Message;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckingYFPolicy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'messages:checking-yf-policy';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查处理优孚平台录单异常保单';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        DB::transaction(function (){
            $messages = Message::where('status', 1)
                ->where('platform_id', 1)
                ->whereNull('apply_no')
                ->whereNull('policy_no')
                ->where('handle_num', '<', 1)
                ->with('attaches')
                ->orderBy('id', 'desc')
                ->get();
            foreach ($messages as $message){
                switch ($message['mode']){
                    case 'AUTO_CPIC':
                        $this->handleCPIC($message);
                        break;
                    default:
                        break;
                }
            }
        });
    }

    /**
     * 处理太保报文
     *
     * @param $message
     * @return void
     */
    protected function handleCPIC($message)
    {
        if(date('Y-m-d') == date('Y-m-d', strtotime($message['created_at']))){
            if((time() - strtotime($message['created_at'])) > 300){
                $message->update(['status' => 0, 'is_entry' => 0, 'handle_num' => $message['handle_num'] +1, 'error_num' => $message['error_num'] +1]);
                $content = json_decode($message['attaches']['content'], true);
                if($content['message']['cargoInfoDto']['sailDateHours'] <= date('H')){
                    $content['message']['cargoInfoDto']['sailDateHours'] = (string) (date('H') + 1);
                    $message->attaches()->update(['content' => jsonFormat($content)]);
                }
            }
        }
    }
}
