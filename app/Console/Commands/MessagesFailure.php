<?php

namespace App\Console\Commands;

use App\Models\Message;
use Carbon\Carbon;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;
use Illuminate\Support\Str;

class MessagesFailure extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'messages:failure-event';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '[Messages] 获取录入失败的消息并触发失败回调';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        Message::where('status', 1)
            ->where(function ($q) {
                $q->where(function ($q) {
                    $q->where('mode', 'AUTO_PINGAN_CBEC')
                        ->whereBetween('done_at', [Carbon::now()->subMinute(120)->startOfMinute(), Carbon::now()->subMinute(5)]);
                })->orWhere(function ($q) {
                    $q->where('mode', '!=', 'AUTO_PINGAN_CBEC')
                        ->whereBetween('done_at', [Carbon::now()->subMinute(10)->startOfMinute(), Carbon::now()->subMinute(5)]);
                });
            })
            ->with(['notifies', 'attaches'])
            ->chunk(100, function ($messages) {
                $messages->each(function ($message) {
                    if ($this->isTriggerable($message)) {
                        // 标记为已发送
                        Cache::put('failure.events.' . $message['id'], Carbon::now(), Carbon::now()->addDay(5));

                        // 触发失败回调
                        $this->sendFailureEvent($this->baoyaNotificationURL($message), [
                            'event' => 'failure',
                            'sys_order_no' => $message['order_no'],
                            'reason' => $this->guessFailureReason($message['attaches']['callback']),
                        ]);
                    }
                });
            });
    }

    /**
     * 推测失败原因.
     *
     * @param  string  $reason
     *
     * @return string
     */
    protected function guessFailureReason(?string $reason = null)
    {
        if (is_null($reason)) {
            return '未知原因';
        }

        if (Str::startsWith($reason, '{')) {
            $decodedReason = json_decode($reason, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return '未知原因';
            }

            if (isset($decodedReason['error'])) {
                return $decodedReason['error'];
            }
        }

        return $reason;
    }

    /**
     * 获取保呀回调地址.
     *
     * @param  \App\Models\Message  $message
     *
     * @return string|void
     */
    protected function baoyaNotificationURL(Message $message)
    {
        foreach ($message['notifies'] as $notifier) {
            if (Str::contains($notifier['url'], '51baoya')) {
                return $notifier['url'];
            }
        }
    }

    /**
     * 是否可触发.
     *
     * @param  \App\Models\Message  $message
     *
     * @return bool
     */
    protected function isTriggerable(Message $message)
    {
        if (Cache::has('failure.events.' . $message['id'])) {
            return false;
        }

        $isTriggerable = false;
        foreach ($message['notifies'] as $notifier) {
            if (Str::contains($notifier['url'], '51baoya')) {
                $isTriggerable = true;
                break;
            }
        }

        return $isTriggerable;
    }

    /**
     * 发送异常回调.
     *
     * @param  string  $url
     * @param  array   $data
     *
     * @return void
     */
    protected function sendFailureEvent(string $url, array $data)
    {
        Log::info('[Failure-Event] 发送录单异常回调', [
            'url' => $url,
            'data' => $data,
        ]);

        try {
            retry(3, function () use ($url, $data) {
                (new HttpClient([
                    'base_uri' => $url,
                    'timeout' => 30.0,
                    'verify' => false,
                ]))->post('', [
                            'json' => $data,
                        ]);
            }, 100);
        } catch (Throwable $e) {
            $this->error($e->getMessage());

            Log::error('[Failure-Event] 发送录单异常回调失败', [
                'exception' => $e->getMessage(),
            ]);
        }
    }
}
