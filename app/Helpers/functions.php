<?php

function jsonFormat($data, $indent=null, $isApi = false){
 
    // 对数组中每个元素递归进行urlencode操作，保护中文字符
    if($isApi){
        array_walk_recursive($data, 'jsonFormatProtect');
        $data = json_encode($data);
    }else{
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
    }
//    array_walk_recursive($data, 'jsonFormatProtect');
    // json encode

    $data = str_ireplace(array('\u202d','\u202c'),'', $data);

 
    // 将urlencode的内容进行urldecode
    $data = urldecode($data);
 
    // 缩进处理
    $ret = '';
    $pos = 0;
    $length = strlen($data);
    $indent = isset($indent)? $indent : '    ';
    $newline = "\n";
    $prevchar = '';
    $outofquotes = true;
 
    for($i=0; $i<=$length; $i++){
 
        $char = substr($data, $i, 1);
 
        if($char=='"' && $prevchar!='\\'){
            $outofquotes = !$outofquotes;
        }elseif(($char=='}' || $char==']') && $outofquotes){
            $ret .= $newline;
            $pos --;
            for($j=0; $j<$pos; $j++){
                $ret .= $indent;
            }
        }
 
        $ret .= $char;
        
        if(($char==',' || $char=='{' || $char=='[') && $outofquotes){
            $ret .= $newline;
            if($char=='{' || $char=='['){
                $pos ++;
            }
 
            for($j=0; $j<$pos; $j++){
                $ret .= $indent;
            }
        }
 
        $prevchar = $char;
    }
 
    return $ret;
}


function jsonFormatProtect(&$val){
    if($val!==true && $val!==false && $val!==null){
        $val = urlencode($val);
    }
}

/*
 * true代表创建时间是时间戳
 */
function subsystem(){
    return [
        // 'DataAladdin' => ['sys_order_no','create_time',true,'INS1569689628','<EMAIL>'],
        'DataYoufu' => ['SerialNo','DateTime',true,'INS1569686500','<EMAIL>'],
        'DataBaoya' => ['sys_order_no','created_at',false,'INS1569723235','<EMAIL>'],
        // 'DataBaoyaOld' => ['sys_order_no','create_time',true,'INS1569689637','<EMAIL>'],
        // 'DataZhongLian' => ['sys_order_no','create_time',true,'INS1569689647','<EMAIL>'],
        'DataBaoyaNew' => ['order_no','submitted_at',false,'INS1635757943','<EMAIL>'],
    ];
}

/*
 * 金额转换中文
 */
function num_to_rmb($num){

    $c1 = "零壹贰叁肆伍陆柒捌玖";
    $c2 = "分角元拾佰仟万拾佰仟亿";
    //精确到分后面就不要了，所以只留两个小数位
    $num = round($num, 2);
    //将数字转化为整数
    $num = $num * 100;
    if (strlen($num) > 10) {
        return "金额太大，请检查";
    }
    $i = 0;
    $c = "";
    while (1) {
        if ($i == 0) {
            //获取最后一位数字
            $n = substr($num, strlen($num)-1, 1);
        } else {
            $n = $num % 10;
        }
        //每次将最后一位数字转化为中文
        $p1 = substr($c1, 3 * $n, 3);
        $p2 = substr($c2, 3 * $i, 3);
        if ($n != '0' || ($n == '0' && ($p2 == '亿' || $p2 == '万' || $p2 == '元'))) {
            $c = $p1 . $p2 . $c;
        } else {
            $c = $p1 . $c;
        }
        $i = $i + 1;
        //去掉数字最后一位了
        $num = $num / 10;
        $num = (int)$num;
        //结束循环
        if ($num == 0) {
            break;
        }
    }
    $j = 0;
    $slen = strlen($c);
    while ($j < $slen) {
        //utf8一个汉字相当3个字符
        $m = substr($c, $j, 6);
        //处理数字中很多0的情况,每次循环去掉一个汉字“零”
        if ($m == '零元' || $m == '零万' || $m == '零亿' || $m == '零零') {
            $left = substr($c, 0, $j);
            $right = substr($c, $j + 3);
            $c = $left . $right;
            $j = $j-3;
            $slen = $slen-3;
        }
        $j = $j + 3;
    }
    //这个是为了去掉类似23.0中最后一个“零”字
    if (substr($c, strlen($c)-3, 3) == '零') {
        $c = substr($c, 0, strlen($c)-3);
    }
    //将处理的汉字加上“整”
    if (empty($c)) {
        return "零元整";
    }else{
        return $c . "整";
    }
}

function mail_address(){
    return [
        '<EMAIL>',
        '<EMAIL>',
    ];
}

function sys_mail($mailto, $mailcc, $title, $content, $setting){
    $title = '=?utf-8?B?'.base64_encode($title).'?=';

    $mail = new \App\Libs\Org\Net\SendMail();
    $mail->setServer($setting['host'], $setting['username'], $setting['password'], $setting['port'], true);
    $mail->setFrom($setting['username']);
    $mail->setFromName("system");

    $_mailto = explode(';', $mailto);
    if(count($_mailto)>1){
        $mail->setReceiver($_mailto);
    }else{
        $mail->setReceiver($mailto);
    }

    $_mailcc = explode(';', $mailcc);
    if(count($_mailcc)>1){
        $mail->setCc($_mailcc);
    }else if(!empty($mailcc)){
        $mail->setCc($mailcc);
    }

    $mail->setMail($title, $content);
    return $mail->sendMail();
}