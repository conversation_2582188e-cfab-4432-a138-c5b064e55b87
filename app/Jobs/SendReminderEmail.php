<?php

namespace App\Jobs;

use App\Models\MailDelivery;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendReminderEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;

    /**
     * 创建一个新的任务实例。
     *
     * @param  Podcast  $podcast
     * @return void
     */
    public function __construct($data)
    {
        foreach ($data->user as $k=>$v){
            if(empty($v)){
                unset($data->user[$k]);
            }
        }
        $this->data = $data;
    }

    /**
     * 任务可以尝试的最大次数。
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 定义任务超时时间。
     *
     * @return \DateTime
     */
    public function retryUntil()
    {
        return now()->addSeconds(40);
    }

    public function handle()
    {
        if($this->data->sender == '<EMAIL>'){
            Mail::send('workbench.mail.mail', $this->data->content, function ($message){
                // 收件人的邮箱地址
                $message->to($this->data->user);
                // 邮件主题
                $message->subject($this->data->theme);
            });
        }else{
            $content = $this->getMailContent($this->data->content);
            $setting = config('services.other_mail');
            sys_mail(implode(';',$this->data->user), '', $this->data->theme, $content, $setting);
        }
        MailDelivery::where('platform_id',$this->data->platform_id)
            ->where('order_no',$this->data->order_no)
            ->update(['status'=>2,'send_time'=>date("Y-m-d H:i:s",time())]);
    }

    /**
     * 任务失败的处理过程。
     *
     * @param  Exception  $exception
     * @return void
     */
    public function failed(\Exception $exception)
    {
        Log::error('Message:'.$exception->getMessage());
        MailDelivery::where('platform_id',$this->data->platform_id)
            ->where('order_no',$this->data->order_no)
            ->update(['status'=>3]);
    }

    protected function getMailContent($data)
    {
        $mail_message  = '<p><strong>'.$data['insured_name'].'，提交于：'.$data['create_time'].'</strong></p>';
        $mail_message .= '<br>';
        $mail_message .= '---------------------------';
        $mail_message .= '<br>';
        $mail_message .= '<p><strong>保险种类：</strong>'.$data['species'].'</p>';
        $mail_message .= '<p><strong>被保险人：</strong>'.$data['insured_name'].'</p>';
        $mail_message .= '<p><strong>起运地：</strong>'.$data['departure'].'</p>';
        $mail_message .= '<p><strong>中转地：</strong>'.$data['stopovers'].'</p>';
        $mail_message .= '<p><strong>目的地：</strong>'.$data['destination'].'</p>';
        $mail_message .= '<p><strong>起运日期：</strong>'.$data['start_time'].'</p>';
        $mail_message .= '<p><strong>货物信息：</strong>'.$data['goods_name'].'&nbsp;&nbsp;'.$data['goods_amount'].'&nbsp;&nbsp;'.$data['pack_type'].'</p>';
        $mail_message .= '<p><strong>车牌号：</strong>'.$data['vehicle_license_no'].'</p>';
        $mail_message .= '<p><strong>保额：</strong>'.$data['coverage'].'</p>';
        $mail_message .= '<p><strong>流水号：</strong>'.$data['order_no'].'</p>';
        $mail_message .= '<br>';
        $mail_message .= '---------------------------';
        $mail_message .= '<br>';
        $mail_message .= '<p><strong>免赔：</strong>'.$data['deductible'].'</p>';
        $mail_message .= '<p><strong>特约：</strong>'.$data['specials'].'</p>';
        $mail_message .= '<br>';
        $mail_message .= '---------------------------';
        $mail_message .= '<br>';
        $mail_message .= '<p>此邮件由系统自动发送</p>';

//        $mail_message  = '<p><strong>张三，提交于：2021-02-04 17</strong></p>';
//        $mail_message .= '<br>';
//        $mail_message .= '---------------------------';
//        $mail_message .= '<br>';
//        $mail_message .= '<p><strong>保险种类：</strong>国内货运险</p>';
//        $mail_message .= '<p><strong>被保险人：</strong>张三</p>';
//        $mail_message .= '<p><strong>起运地：</strong>四川</p>';
//        $mail_message .= '<p><strong>中转地：</strong></p>';
//        $mail_message .= '<p><strong>目的地：</strong>重庆</p>';
//        $mail_message .= '<p><strong>起运日期：</strong>2021-02-04 17</p>';
//        $mail_message .= '<p><strong>货物信息：</strong>火龙果&nbsp;&nbsp;一吨&nbsp;&nbsp;纸箱</p>';
//        $mail_message .= '<p><strong>车牌号：</strong>川A87962</p>';
//        $mail_message .= '<p><strong>保额：</strong>100000</p>';
//        $mail_message .= '<p><strong>流水号：</strong>CGO135135151313513</p>';
//        $mail_message .= '<br>';
//        $mail_message .= '---------------------------';
//        $mail_message .= '<br>';
//        $mail_message .= '<p><strong>免赔：</strong></p>';
//        $mail_message .= '<p><strong>特约：</strong></p>';
//        $mail_message .= '<br>';
//        $mail_message .= '---------------------------';
//        $mail_message .= '<br>';
//        $mail_message .= '<p>此邮件由系统自动发送</p>';
        return $mail_message;
    }
}
